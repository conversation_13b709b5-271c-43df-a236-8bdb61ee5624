# Google API Key - Required for Google ADK
GOOGLE_API_KEY=
GOOGLE_GENAI_USE_VERTEXAI=FALSE
#GOOGLE_APPLICATION_CREDENTIALS=

GOOGLE_CLOUD_PROJECT=
GOOGLE_CLOUD_LOCATION=


# AWS Cognito Configuration (Optional - disabled for simplicity)
# COGNITO_USER_POOL_ID=us-east-1_example
# COGNITO_CLIENT_ID=example_client_id
# COGNITO_REGION=us-east-1

# Environment Configuration
ENVIRONMENT=dev
USE_MOCK_DATA=true

# API Configuration
API_VERSION=v1.0.0

# Azure Active Directory credentials (legacy - not used anymore)
A<PERSON>URE_TENANT_ID=
AZURE_CLIENT_ID=
AZURE_CLIENT_SECRET= 
AZURE_SCOPE= https://graph.microsoft.com/.default
AZURE_TOKEN_URL=https://login.microsoftonline.com/{AZURE_TENANT_ID}/oauth2/v2.0/token



# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key

# Database Configuration
# Use SQLite for local development (comment out for PostgreSQL)
DATABASE_URL=sqlite:///itsm_audit.db
# For PostgreSQL, uncomment and configure the line below:
# DATABASE_URL=*****************************************************/postgres

# Model Configuration
ROOT_MODEL=gemini-2.5-flash
SUB_AGENT_MODEL=gemini-2.5-flash

# Logging Configuration
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# WagonHR API Configuration
WAGONHR_API_URL=https://qa-api-wagonhr.mouritech.net
WAGONHR_USERNAME=your-username
WAGONHR_PASSWORD=your-password

# HR API URLs
LEAVE_API_BASE_URL=https://qa-api-wagonhr.mouritech.net
ATTENDANCE_API_BASE_URL=https://qa-api-wagonhr.mouritech.net

# Redis Configuration (Optional - disabled for simplicity)
USE_REDIS_CACHE=false
REDIS_URL=localhost:6379
REDIS_PASSWORD=

# HR API Headers Configuration
HR_ORGANIZATION_ID=your-organization-id

# Redis Configuration for Token Caching
USE_REDIS_CACHE=true
REDIS_URL=localhost:6379
REDIS_PASSWORD=

# API Timeout (in seconds)
HR_API_TIMEOUT=10

HR_TENANT_ID=mouritech
HR_ENTITY_ID=IN-MT-001
HR_ORGANIZATION_ID=


OPENSEARCH_HOST=
OPENSEARCH_PASS=
OPENSEARCH_USER=
POLICY_INDEX_NAME=

GOOGLE_EMBEDDING_MODEL=


# 🔐 AWS Credentials
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_DEFAULT_REGION="us-east-2"  # e.g., us-east-1