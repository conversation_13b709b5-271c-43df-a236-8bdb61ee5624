"""
Main application module.

This module provides the main entry point for the HRAgent solution.
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Mock Google Generative AI types
class MockTypes:
    class HarmCategory:
        HARM_CATEGORY_HARASSMENT = "HARM_CATEGORY_HARASSMENT"
        HARM_CATEGORY_HATE_SPEECH = "HARM_CATEGORY_HATE_SPEECH"
        HARM_CATEGORY_SEXUALLY_EXPLICIT = "HARM_CATEGORY_SEXUALLY_EXPLICIT"
        HARM_CATEGORY_DANGEROUS_CONTENT = "HARM_CATEGORY_DANGEROUS_CONTENT"

    class HarmBlockThreshold:
        BLOCK_NONE = "BLOCK_NONE"
        BLOCK_LOW = "BLOCK_LOW"
        BLOCK_MEDIUM = "BLOCK_MEDIUM"
        BLOCK_HIGH = "BLOCK_HIGH"

# Mock the types module
types = MockTypes()

# Import other modules
from agents.root_agent import create_root_agent
from runners.runner_service import ItsmsRunnerService
from services.session_service import EnhancedInMemorySessionService
from services.memory_service import EnhancedInMemoryMemoryService
from utils.logging_config import configure_logging


class HRAgentApplication:
    """Main HR Application.

    This class provides the main entry point for the HR solution.
    """

    def __init__(
        self,
        app_name: str = "hragent_solution",
        root_model: str = "gemini-2.5-flash",
        sub_agent_model: str = "gemini-2.5-flash",
        safety_callbacks: bool = True,
        log_level: int = logging.INFO
    ):
        """Initialize the HRAgent Application.

        Args:
            app_name: The name of the application
            root_model: The model to use for the root agent
            sub_agent_model: The model to use for sub-agents
            safety_callbacks: Whether to enable safety callbacks
            log_level: The logging level to use
        """
        # Configure logging
        configure_logging(
            log_level=log_level,
            log_file=f"{app_name}.log"
        )

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Initializing HRAgent Application with app_name={app_name}")

        # Create agents
        try:
            self.root_agent = create_root_agent(
                model_name=root_model,
                safety_callbacks=safety_callbacks,
                sub_agent_model=sub_agent_model
            )
        except Exception as e:
            self.logger.error(f"Error creating root agent: {str(e)}")
            raise ValueError(f"Failed to create root agent: {str(e)}")

        # Always use In-Memory Session Service following ADK standards
        self.logger.info("Using Enhanced In-Memory Session Service with ADK standards")
        self.session_service = EnhancedInMemorySessionService()

        # Initialize Memory Service for long-term knowledge storage
        self.logger.info("Using Enhanced In-Memory Memory Service for cross-session knowledge")
        self.memory_service = EnhancedInMemoryMemoryService()

        # Create runner service with both session and memory services
        self.runner_service = ItsmsRunnerService(
            root_agent=self.root_agent,
            app_name=app_name,
            session_service=self.session_service,
            memory_service=self.memory_service
        )


    async def create_session(
        self,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        initial_state: Optional[Dict[str, Any]] = None
    ) -> Dict[str, str]:
        """Create a new session.

        Args:
            user_id: The user ID, or None to generate a random one
            session_id: The session ID, or None to generate a random one
            initial_state: Optional initial state for the session

        Returns:
            A dictionary with the user_id and session_id
        """
        return await self.runner_service.create_session(
            user_id=user_id,
            session_id=session_id,
            initial_state=initial_state
        )

    async def process_message(
        self,
        message: str,
        user_id: str,
        session_id: str,
        api_auth_token: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process a user message and return the final response.

        Args:
            message: The user message
            user_id: The user ID
            session_id: The session ID
            api_auth_token: API authentication token information

        Returns:
            Dictionary with the final response
        """
        final_response = None
        print("process message in hragent app")
        async for event in self.runner_service.process_message(
            message=message,
            user_id=user_id,
            session_id=session_id,
            api_auth_token=api_auth_token
        ):

            print("event in hragent app: ", event)
            if event.get("is_final", False) or event.get("type") == "error":
                final_response = event

        return final_response or {"type": "message", "content": "I'm experiencing technical difficulties. Please try again."}

    async def process_message_stream(
        self,
        message: str,
        user_id: str,
        session_id: str
    ):
        """Process a user message and stream all events.

        Args:
            message: The user message
            user_id: The user ID
            session_id: The session ID

        Returns:
            An async generator yielding event dictionaries
        """

        async for event in self.runner_service.process_message(
            message=message,
            user_id=user_id,
            session_id=session_id
        ):
            yield event

    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get all active sessions.

        Returns:
            Dictionary of active sessions
        """
        return self.runner_service.get_active_sessions()

    def get_session_events(self, session_id: str) -> list:
        """Get events for a specific session.

        Args:
            session_id: The session ID

        Returns:
            List of events for the session
        """
        return self.runner_service.get_session_events(session_id)


async def run_interactive_demo():
    """Run an interactive demo of the HRAgent Application in the console."""

    # Set up environment from .env file
    try:
        # Initialize environment variables from .env file
        print("Initializing environment variables from .env file")
    except Exception as e:
        print(f"Error: {str(e)}. Failed to set up environment.")
        sys.exit(1)

    print(f"\n===Virtual HR AI Assistant Demo  ===\n")
    print("Initializing application...")

    # Get log level from environment
    log_level_name = os.environ.get("LOG_LEVEL", "INFO")
    log_levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    log_level = log_levels.get(log_level_name, logging.INFO)

    # Use models specified in environment or defaults
    root_model = os.environ.get("ROOT_MODEL", "gemini-2.5-flash")
    sub_agent_model = os.environ.get("SUB_AGENT_MODEL", "gemini-2.5-flash")

    # Always use hragent_demo as app_name
    app_name = "hragent_demo"

    # Create application
    app = HRAgentApplication(
        app_name=app_name,
        root_model=root_model,
        sub_agent_model=sub_agent_model,
        safety_callbacks=True,
        log_level=log_level
    )

    print("Creating a new session...")
    session_info = await app.create_session(user_id="demo_user")
    user_id = session_info["user_id"]
    session_id = session_info["session_id"]

    print(f"\nSession created: {session_id}")
    print("\nType your requests and press Enter. Type 'exit' to quit.\n")

    # Main interaction loop
    while True:
        user_input = input("\nYou: ")
        if user_input.lower() in ["exit", "quit", "bye"]:
            print("\nExiting demo. Thank you for using the HR AI Assistant!")
            break

        print("\nProcessing...")

        # Stream responses
        print("\n HRAgent AI: ", end="", flush=True)
        async for event in app.process_message_stream(
            message=user_input,
            user_id=user_id,
            session_id=session_id
        ):

            if event.get("type") == "error":
                print(f"\nError: {event.get('content', 'I am experiencing technical difficulties. Please try again.')}")
            elif event.get("content") and event.get("is_final", False):
                print(event["content"])
            # Ignore intermediate events for simplicity

def main():
    """Main entry point for the application."""
    asyncio.run(run_interactive_demo())

if __name__ == "__main__":
    main()