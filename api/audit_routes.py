"""
Audit API routes.

This module contains API routes for accessing audit data.
"""

import logging
import datetime
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from services.audit_service import AuditService
from utils.db_config import get_db_session

# Set up logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/audit",
    tags=["audit"],
    responses={404: {"description": "Not found"}},
)


# Models
class AuditEventResponse(BaseModel):
    """Audit event response model."""

    id: int
    session_id: str
    agent_id: int
    user_identifier: str
    actor_type: str
    actor_id: Optional[str] = None
    action_type: str
    entity_type: Optional[str] = None
    entity_id: Optional[str] = None
    status: str
    error_message: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = None
    created_at: str
    modified_at: Optional[str] = None


class SessionMessageResponse(BaseModel):
    """Session message response model."""

    id: int
    actor_type: str
    message: str
    timestamp: str


class SessionHistoryResponse(BaseModel):
    """Session history response model."""

    session_id: str
    user_identifier: str
    agent_id: int
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    messages: Optional[List[SessionMessageResponse]] = None
    events: Optional[List[AuditEventResponse]] = None


class AgentRegistryResponse(BaseModel):
    """Agent registry response model."""

    id: int
    name: str
    description: Optional[str] = None
    is_active: bool
    config: Optional[Dict[str, Any]] = None
    created_at: str
    modified_at: Optional[str] = None


class AgentPromptResponse(BaseModel):
    """Agent prompt response model."""

    id: int
    agent_id: int
    prompt_type: str
    prompt_text: str
    created_at: str


class AgentToolResponse(BaseModel):
    """Agent tool response model."""

    id: int
    agent_id: int
    tool_name: str
    tool_description: Optional[str] = None
    tool_config: Optional[Dict[str, Any]] = None
    created_at: str


# Routes
@router.get("/events", response_model=List[AuditEventResponse])
async def get_audit_events(
    session_id: Optional[str] = None,
    agent_id: Optional[int] = None,
    user_identifier: Optional[str] = None,
    actor_type: Optional[str] = None,
    action_type: Optional[str] = None,
    status: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0)
):
    """Get audit events based on filters."""
    try:
        # Convert string dates to datetime objects
        start_datetime = None
        end_datetime = None
        if start_time:
            start_datetime = datetime.datetime.fromisoformat(start_time)
        if end_time:
            end_datetime = datetime.datetime.fromisoformat(end_time)

        # Get events from service
        events = AuditService.get_audit_events(
            session_id=session_id,
            agent_id=agent_id,
            user_identifier=user_identifier,
            actor_type=actor_type,
            action_type=action_type,
            status=status,
            start_time=start_datetime,
            end_time=end_datetime,
            limit=limit,
            offset=offset
        )

        return events
    except Exception as e:
        logger.error(f"Error getting audit events: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions/{session_id}", response_model=SessionHistoryResponse)
async def get_session_history(
    session_id: str,
    include_messages: bool = True,
    include_events: bool = True
):
    """Get the complete history of a session."""
    try:
        # Get session history from service
        history = AuditService.get_session_history(
            session_id=session_id,
            include_messages=include_messages,
            include_events=include_events
        )

        if "error" in history:
            raise HTTPException(status_code=404, detail=history["error"])

        return history
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions", response_model=List[str])
async def get_sessions(
    user_identifier: Optional[str] = None,
    agent_id: Optional[int] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0)
):
    """Get session IDs based on filters."""
    try:
        # Get database session
        db = get_db_session()

        # Build query
        from models.audit_models import Session
        query = db.query(Session.id)

        # Apply filters
        if user_identifier:
            query = query.filter(Session.user_identifier == user_identifier)
        if agent_id:
            query = query.filter(Session.agent_id == agent_id)
        if start_time:
            start_datetime = datetime.datetime.fromisoformat(start_time)
            query = query.filter(Session.start_time >= start_datetime)
        if end_time:
            end_datetime = datetime.datetime.fromisoformat(end_time)
            query = query.filter(Session.start_time <= end_datetime)

        # Apply pagination
        query = query.order_by(Session.start_time.desc())
        query = query.limit(limit).offset(offset)

        # Execute query
        session_ids = [row[0] for row in query.all()]

        return session_ids
    except Exception as e:
        logger.error(f"Error getting sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        db.close()


@router.get("/agents", response_model=List[AgentRegistryResponse])
async def get_agents(
    is_active: Optional[bool] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0)
):
    """Get registered agents."""
    try:
        # Get database session
        db = get_db_session()

        # Build query
        from models.audit_models import AgentRegistry
        query = db.query(AgentRegistry)

        # Apply filters
        if is_active is not None:
            query = query.filter(AgentRegistry.is_active == is_active)

        # Apply pagination
        query = query.order_by(AgentRegistry.name)
        query = query.limit(limit).offset(offset)

        # Execute query
        agents = query.all()

        # Convert to response model
        result = []
        for agent in agents:
            result.append({
                "id": agent.id,
                "name": agent.name,
                "description": agent.description,
                "is_active": agent.is_active,
                "config": agent.config,
                "created_at": agent.created_at.isoformat(),
                "modified_at": agent.modified_at.isoformat() if agent.modified_at else None
            })

        return result
    except Exception as e:
        logger.error(f"Error getting agents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        db.close()


@router.get("/reports/user-activity/{user_identifier}")
async def get_user_activity_report(
    user_identifier: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
):
    """Get a report of user activity."""
    try:
        # Get database session
        db = get_db_session()

        # Convert string dates to datetime objects
        start_datetime = None
        end_datetime = None
        if start_time:
            start_datetime = datetime.datetime.fromisoformat(start_time)
        if end_time:
            end_datetime = datetime.datetime.fromisoformat(end_time)

        # Get sessions for user
        from models.audit_models import Session
        sessions_query = db.query(Session).filter(Session.user_identifier == user_identifier)
        if start_datetime:
            sessions_query = sessions_query.filter(Session.start_time >= start_datetime)
        if end_datetime:
            sessions_query = sessions_query.filter(Session.start_time <= end_datetime)
        sessions = sessions_query.all()

        # Get audit events for user
        from models.audit_models import AuditEvent
        events_query = db.query(AuditEvent).filter(AuditEvent.user_identifier == user_identifier)
        if start_datetime:
            events_query = events_query.filter(AuditEvent.created_at >= start_datetime)
        if end_datetime:
            events_query = events_query.filter(AuditEvent.created_at <= end_datetime)
        events = events_query.all()

        # Count events by type
        event_counts = {}
        for event in events:
            action_type = event.action_type
            if action_type not in event_counts:
                event_counts[action_type] = 0
            event_counts[action_type] += 1

        # Count events by status
        status_counts = {}
        for event in events:
            status = event.status
            if status not in status_counts:
                status_counts[status] = 0
            status_counts[status] += 1

        # Count events by entity type
        entity_counts = {}
        for event in events:
            if event.entity_type:
                entity_type = event.entity_type
                if entity_type not in entity_counts:
                    entity_counts[entity_type] = 0
                entity_counts[entity_type] += 1

        # Build report
        report = {
            "user_identifier": user_identifier,
            "start_time": start_time,
            "end_time": end_time,
            "session_count": len(sessions),
            "event_count": len(events),
            "event_counts_by_type": event_counts,
            "event_counts_by_status": status_counts,
            "event_counts_by_entity": entity_counts
        }

        return report
    except Exception as e:
        logger.error(f"Error generating user activity report: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        db.close()


@router.get("/reports/tool-usage")
async def get_tool_usage_report(
    agent_id: Optional[int] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
):
    """Get a report of tool usage."""
    try:
        # Get database session
        db = get_db_session()

        # Convert string dates to datetime objects
        start_datetime = None
        end_datetime = None
        if start_time:
            start_datetime = datetime.datetime.fromisoformat(start_time)
        if end_time:
            end_datetime = datetime.datetime.fromisoformat(end_time)

        # Get tool events
        from models.audit_models import AuditEvent
        query = db.query(AuditEvent).filter(AuditEvent.actor_type == "tool")
        if agent_id:
            query = query.filter(AuditEvent.agent_id == agent_id)
        if start_datetime:
            query = query.filter(AuditEvent.created_at >= start_datetime)
        if end_datetime:
            query = query.filter(AuditEvent.created_at <= end_datetime)
        events = query.all()

        # Count events by tool
        tool_counts = {}
        for event in events:
            tool_name = event.actor_id
            if tool_name not in tool_counts:
                tool_counts[tool_name] = {
                    "total": 0,
                    "success": 0,
                    "error": 0,
                    "pending": 0
                }
            tool_counts[tool_name]["total"] += 1
            tool_counts[tool_name][event.status] += 1

        # Build report
        report = {
            "agent_id": agent_id,
            "start_time": start_time,
            "end_time": end_time,
            "total_tool_calls": len(events),
            "tool_usage": tool_counts
        }

        return report
    except Exception as e:
        logger.error(f"Error generating tool usage report: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        db.close()