"""
Test cases for intelligent leave processing system.

This module contains comprehensive test cases for all prompt variations
and error scenarios to ensure robust behavior.
"""

import unittest
from unittest.mock import patch, MagicMock
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.intelligent_leave_processor import IntelligentLeaveProcessor
from utils.user_friendly_responses import UserFriendlyResponseGenerator
from utils.date_parser import IntelligentDateParser


class TestIntelligentLeaveProcessing(unittest.TestCase):
    """Test cases for intelligent leave processing."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.processor = IntelligentLeaveProcessor()
        self.response_generator = UserFriendlyResponseGenerator()
        self.date_parser = IntelligentDateParser()
    
    def test_casual_prompts(self):
        """Test casual prompt handling."""
        casual_prompts = [
            "I want to take a day off tomorrow.",
            "Need a leave for next Friday.",
            "Can I apply for leave from 1st to 3rd August?",
            "Taking sick leave today, not feeling well."
        ]
        
        for prompt in casual_prompts:
            with self.subTest(prompt=prompt):
                result = self.processor.process_leave_request(prompt)
                
                # Should not fail completely
                self.assertIsInstance(result, dict)
                
                # Should have user-friendly handling
                if not result.get("success"):
                    self.assertTrue(result.get("user_friendly", False))
                    self.assertIn("message", result)
    
    def test_formal_prompts(self):
        """Test formal prompt handling."""
        formal_prompts = [
            "I'd like to apply for annual leave from July 28 to August 2.",
            "Please apply earned leave on my behalf from September 5 to 9.",
            "I would like to submit a sick leave request for today.",
            "Kindly process my casual leave for next Monday."
        ]
        
        for prompt in formal_prompts:
            with self.subTest(prompt=prompt):
                result = self.processor.process_leave_request(prompt)
                
                # Should not fail completely
                self.assertIsInstance(result, dict)
                
                # Should extract some information
                if result.get("success"):
                    self.assertIn("intent", result)
                    self.assertEqual(result["intent"], "apply_leave")
    
    def test_reason_based_prompts(self):
        """Test reason-based prompt handling."""
        reason_prompts = [
            "I need to apply for medical leave for 3 days due to surgery.",
            "Applying for emergency leave due to a family matter.",
            "Requesting maternity/paternity leave starting next month.",
            "I'd like to take leave for a personal event on Friday."
        ]
        
        for prompt in reason_prompts:
            with self.subTest(prompt=prompt):
                result = self.processor.process_leave_request(prompt)
                
                # Should not fail completely
                self.assertIsInstance(result, dict)
                
                # Should extract reason information
                if result.get("success"):
                    extracted_info = result
                elif result.get("extracted_info"):
                    extracted_info = result["extracted_info"]
                else:
                    extracted_info = {}
                
                # Should have some reason-related information
                self.assertTrue(
                    extracted_info.get("reason") or 
                    extracted_info.get("leave_type") or
                    "reason" not in result.get("missing_fields", [])
                )
    
    def test_interactive_prompts(self):
        """Test interactive prompt handling."""
        interactive_prompts = [
            "Help me apply for leave.",
            "Can you check my leave balance and apply leave for next week?",
            "What types of leave can I apply for?",
            "I want to take a 2-day leave next week. What's the process?"
        ]
        
        for prompt in interactive_prompts:
            with self.subTest(prompt=prompt):
                result = self.processor.process_leave_request(prompt)
                
                # Should not fail completely
                self.assertIsInstance(result, dict)
                
                # Should provide helpful response
                self.assertTrue(
                    result.get("success") or 
                    result.get("user_friendly", False)
                )
                
                if "help" in prompt.lower():
                    self.assertEqual(result.get("intent"), "help")
    
    def test_date_duration_variants(self):
        """Test date and duration variant handling."""
        date_prompts = [
            "Apply leave for 2 days starting from 10th Aug.",
            "Take half-day leave tomorrow.",
            "Leave request for next Wednesday only.",
            "Out of office from 24th to 26th – apply leave accordingly."
        ]
        
        for prompt in date_prompts:
            with self.subTest(prompt=prompt):
                result = self.processor.process_leave_request(prompt)
                
                # Should not fail completely
                self.assertIsInstance(result, dict)
                
                # Should handle dates in some way
                if result.get("success"):
                    self.assertTrue(
                        result.get("start_date") or 
                        result.get("extracted_info", {}).get("start_date")
                    )
    
    def test_error_handling_user_friendly(self):
        """Test that error handling is user-friendly."""
        problematic_prompts = [
            "please apply a leave for me",  # Missing details
            "I need leave",  # Very vague
            "tomorrow leave",  # Incomplete
            "sick"  # Just one word
        ]
        
        for prompt in problematic_prompts:
            with self.subTest(prompt=prompt):
                result = self.processor.process_leave_request(prompt)
                
                # Should not fail completely
                self.assertIsInstance(result, dict)
                
                # Should be user-friendly
                if not result.get("success"):
                    self.assertTrue(result.get("user_friendly", False))
                    self.assertIn("message", result)
                    
                    # Message should be helpful, not technical
                    message = result["message"].lower()
                    self.assertNotIn("error", message)
                    self.assertNotIn("failed", message)
                    self.assertNotIn("parse", message)
                    self.assertNotIn("exception", message)
    
    def test_date_parsing_comprehensive(self):
        """Test comprehensive date parsing capabilities."""
        date_expressions = [
            "tomorrow",
            "next Friday",
            "5 aug to 10 aug",
            "August 1st to 5th",
            "from Monday to Wednesday",
            "next week",
            "1st Aug",
            "July 25th",
            "2024-08-01"
        ]
        
        for expression in date_expressions:
            with self.subTest(expression=expression):
                result = self.date_parser.parse_date_expression(expression)
                
                # Should either succeed or fail gracefully
                self.assertIsInstance(result, dict)
                
                if result.get("success"):
                    # Should have proper date format
                    self.assertTrue(
                        result.get("date") or 
                        (result.get("start_date") and result.get("end_date"))
                    )
    
    def test_user_friendly_responses(self):
        """Test user-friendly response generation."""
        # Test different response types
        response_types = [
            ("date_parsing_error", {"original_text": "please apply a leave for me"}),
            ("leave_type_clarification", {"extracted_info": {"start_date": "2024-08-01"}}),
            ("reason_clarification", {"extracted_info": {"leave_type": "sick", "start_date": "2024-08-01"}}),
            ("general_error", {"context": "processing your request"}),
            ("help", {})
        ]
        
        for response_type, kwargs in response_types:
            with self.subTest(response_type=response_type):
                if response_type == "date_parsing_error":
                    result = self.response_generator.generate_date_parsing_error_response(
                        kwargs["original_text"]
                    )
                elif response_type == "leave_type_clarification":
                    result = self.response_generator.generate_leave_type_clarification(
                        kwargs["extracted_info"]
                    )
                elif response_type == "reason_clarification":
                    result = self.response_generator.generate_reason_clarification(
                        kwargs["extracted_info"]
                    )
                elif response_type == "general_error":
                    result = self.response_generator.generate_general_error_response(
                        kwargs["context"]
                    )
                elif response_type == "help":
                    result = self.response_generator.generate_help_response()
                
                # Should be user-friendly
                self.assertTrue(result.get("user_friendly", False))
                self.assertIn("message", result)
                
                # Message should be helpful
                message = result["message"]
                self.assertGreater(len(message), 10)  # Should be substantial
                self.assertNotIn("error", message.lower())
                self.assertNotIn("failed", message.lower())
    
    def test_no_technical_errors_exposed(self):
        """Test that no technical errors are exposed to users."""
        # Test various edge cases that might cause technical errors
        edge_cases = [
            "",  # Empty string
            "   ",  # Whitespace only
            "!@#$%^&*()",  # Special characters
            "a" * 1000,  # Very long string
            "leave leave leave leave",  # Repetitive
            "123456789",  # Numbers only
        ]
        
        for case in edge_cases:
            with self.subTest(case=case):
                result = self.processor.process_leave_request(case)
                
                # Should not fail completely
                self.assertIsInstance(result, dict)
                
                # Should not expose technical details
                if "message" in result:
                    message = result["message"].lower()
                    technical_terms = [
                        "exception", "traceback", "error", "failed", 
                        "parse", "llm", "api", "token", "json"
                    ]
                    
                    for term in technical_terms:
                        self.assertNotIn(term, message, 
                                       f"Technical term '{term}' found in user message: {message}")


def demonstrate_intelligent_processing():
    """Demonstrate the intelligent leave processing system."""
    print("🚀 Intelligent Leave Processing Demonstration")
    print("=" * 60)

    processor = IntelligentLeaveProcessor()

    # Test cases from the requirements
    test_prompts = [
        # Casual Prompts
        ("Casual", "I want to take a day off tomorrow."),
        ("Casual", "Need a leave for next Friday."),
        ("Casual", "Can I apply for leave from 1st to 3rd August?"),
        ("Casual", "Taking sick leave today, not feeling well."),

        # Formal Prompts
        ("Formal", "I'd like to apply for annual leave from July 28 to August 2."),
        ("Formal", "Please apply earned leave on my behalf from September 5 to 9."),
        ("Formal", "I would like to submit a sick leave request for today."),
        ("Formal", "Kindly process my casual leave for next Monday."),

        # Reason-based Prompts
        ("Reason-based", "I need to apply for medical leave for 3 days due to surgery."),
        ("Reason-based", "Applying for emergency leave due to a family matter."),
        ("Reason-based", "I'd like to take leave for a personal event on Friday."),

        # Interactive Style Prompts
        ("Interactive", "Help me apply for leave."),
        ("Interactive", "Can you check my leave balance and apply leave for next week?"),
        ("Interactive", "What types of leave can I apply for?"),

        # Date & Duration Variants
        ("Date/Duration", "Apply leave for 2 days starting from 10th Aug."),
        ("Date/Duration", "Take half-day leave tomorrow."),
        ("Date/Duration", "Leave request for next Wednesday only."),
        ("Date/Duration", "Out of office from 24th to 26th – apply leave accordingly."),

        # Problematic cases (from the image)
        ("Problematic", "please apply a leave for me"),
        ("Problematic", "please apply a vacation leave from 5 aug to 10 aug"),
    ]

    for category, prompt in test_prompts:
        print(f"\n📝 {category}: '{prompt}'")
        print("-" * 50)

        result = processor.process_leave_request(prompt)

        if result.get("success"):
            print("✅ SUCCESS")
            if result.get("intent") == "help":
                print(f"💬 Response: {result.get('message', '')[:100]}...")
            else:
                print(f"🎯 Intent: {result.get('intent')}")
                print(f"📅 Dates: {result.get('start_date')} to {result.get('end_date')}")
                print(f"🏷️  Type: {result.get('leave_type')}")
                print(f"📝 Reason: {result.get('reason')}")
                print(f"💭 Interpretation: {result.get('interpretation')}")
        else:
            print("🔄 NEEDS CLARIFICATION")
            print(f"💬 User-friendly message: {result.get('message', '')}")
            if result.get("missing_fields"):
                print(f"❓ Missing: {', '.join(result['missing_fields'])}")

    print("\n" + "=" * 60)
    print("✨ Demonstration complete! All prompts handled intelligently.")


if __name__ == '__main__':
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'demo':
        demonstrate_intelligent_processing()
    else:
        # Run the tests
        unittest.main(verbosity=2)
