"""
Profile Tools Module.

This module provides tools for handling profile-related queries using the
Flexible WagonHR Agent for real-time API integration. All profile-related
operations are consolidated here for better organization and maintainability.
"""

import logging
from typing import Dict, Any, Optional, List
import requests
from utils.hr_auth import hr_auth
from google.adk.tools.tool_context import ToolContext
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up logger
logger = logging.getLogger(__name__)

# Base URL for all API calls
BASE_URL = "https://qa-api-wagonhr.mouritech.net"

def _get_auth_headers() -> Dict[str, str]:
    """
    Get authentication headers for API calls.
    
    Returns:
        Dictionary containing authentication headers
    """
    auth_headers = hr_auth.get_wagonhr_auth_headers()
    if not auth_headers:
        logger.error("Failed to get WagonHR authentication headers")
        return None

    return {
        **auth_headers,
        "Accept": "application/json",
        "Content-Type": "application/json",
        "x-tenantid": "mouritech",
        "x-entityid": "IN-MT-001"
    }

def _make_api_request(
    method: str,
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    json_data: Optional[Dict[str, Any]] = None,
    tool_context: Optional[ToolContext] = None,
    timeout: int = 30
) -> Dict[str, Any]:
    """
    Generic method to make API requests.
    
    Args:
        method: HTTP method (GET, POST, PUT, DELETE)
        endpoint: API endpoint
        params: Query parameters
        json_data: Request body for POST/PUT
        tool_context: Optional tool context for additional parameters
        timeout: Request timeout in seconds
        
    Returns:
        Dictionary containing response data and status
    """
    print("======== _make_api_request ==========")
    print(f"method: {method}")
    print(f"endpoint: {endpoint}")
    print(f"params: {params}")
    print(f"json_data: {json_data}")
    print(f"tool_context: {tool_context}")
    print("======== _make_api_request ==========")
    try:
        headers = _get_auth_headers()
        if not headers:
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Handle tool context parameters if provided
        if tool_context:
            if isinstance(tool_context, dict):
                # If tool_context is a dict, merge it with params
                if params is None:
                    params = {}
                params.update(tool_context)
            elif hasattr(tool_context, "__dict__"):
                # If tool_context is an object, get its attributes
                context_dict = {
                    k: v for k, v in tool_context.__dict__.items()
                    if not k.startswith('_') and v is not None
                }
                if params is None:
                    params = {}
                params.update(context_dict)

        url = f"{BASE_URL}{endpoint}"
        logger.info(f"Making {method} request to: {url}")
        if params:
            logger.info(f"Query parameters: {params}")
        if json_data:
            logger.info(f"Request body: {json_data}")

        response = requests.request(
            method=method,
            url=url,
            headers=headers,
            params=params,
            json=json_data,
            timeout=timeout
        )

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201, 204]:
            try:
                data = response.json() if response.content else {}
            except:
                data = {}
            return {
                "status": "success",
                "data": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"
            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        error_msg = f"Error making API request: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }

def get_employee_info(tool_context: Optional[ToolContext] = None) -> Dict[str, Any]:
    """Gets the current employee's profile information."""
    print("======== get_employee_info ==========")
    return _make_api_request("GET", "/api/hrms/hris/employee/id", tool_context=tool_context)

def get_employee_work_profile(tool_context: Optional[ToolContext] = None) -> Dict[str, Any]:
    """Gets the current employee's work profile information."""
    print("======== get_employee_work_profile ==========")
    return _make_api_request("GET", "/api/hrms/hris/employee/myprofile/work-details", tool_context=tool_context)

def get_my_team(tool_context: Optional[ToolContext] = None) -> Dict[str, Any]:
    """Gets the current employee's team members."""
    print("======== get_my_team ==========")
    return _make_api_request("GET", "/api/hrms/hris/employee/my-team", tool_context=tool_context)

def search_employee(
    search_string: Optional[str] = None,
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Searches for employees based on criteria."""
    print("======== search_employee ==========")
    params = {}
    if search_string:
        params["searchString"] = search_string
    elif tool_context and hasattr(tool_context, "searchString"):
        params["searchString"] = tool_context.searchString

    return _make_api_request(
        "GET",
        "/api/hrms/hris/employee-criteria",
        params=params,
        tool_context=tool_context
    )

def get_employee_details(
    employee_id: Optional[str] = None,
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Gets detailed information for a specific employee."""
    params = {"employeeId": employee_id} if employee_id else None
    return _make_api_request(
        "GET",
        "/api/hrms/hris/employee/details",
        params=params,
        tool_context=tool_context
    )

def get_employee_work_details(
    employee_id: Optional[str] = None,
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Gets work details for a specific employee."""
    print("======== get_employee_work_details ==========")
    params = {"employeeId": employee_id} if employee_id else None
    return _make_api_request(
        "GET",
        "/api/hrms/hris/employee/work-details",
        params=params,
        tool_context=tool_context
    )

def get_employee_contact_details(
    employee_id: Optional[str] = None,
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Gets contact details for a specific employee."""
    print("======== get_employee_contact_details ==========")
    params = {"employeeId": employee_id} if employee_id else None
    return _make_api_request(
        "GET",
        "/api/hrms/hris/employee/contact-details",
        params=params,
        tool_context=tool_context
    )

def get_employee_documents(
    employee_id: Optional[str] = None,
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Gets documents for a specific employee."""
    print("======== get_employee_documents ==========")
    params = {"employeeId": employee_id} if employee_id else None
    return _make_api_request(
        "GET",
        "/api/hrms/hris/employee/documents",
        params=params,
        tool_context=tool_context
    )

def get_employee_attendance(
    employee_id: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Gets attendance records for a specific employee."""
    print("======== get_employee_attendance ==========")
    params = {}
    if employee_id:
        params["employeeId"] = employee_id
    if start_date:
        params["startDate"] = start_date
    if end_date:
        params["endDate"] = end_date

    return _make_api_request(
        "GET",
        "/api/hrms/attendance/employee/records",
        params=params,
        tool_context=tool_context
    )

def get_employee_salary(
    employee_id: Optional[str] = None,
    month: Optional[str] = None,
    year: Optional[str] = None,
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Gets salary information for a specific employee."""
    params = {}
    if employee_id:
        params["employeeId"] = employee_id
    if month:
        params["month"] = month
    if year:
        params["year"] = year

    return _make_api_request(
        "GET",
        "/api/hrms/payroll/employee/salary",
        params=params,
        tool_context=tool_context
    )
        
        
        
        
        
        