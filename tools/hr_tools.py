"""
Unified HR Management Tools Module.

This module provides comprehensive HR management functionality using the
Flexible WagonHR Agent for real-time API integration. All routine HR operations
are consolidated here for better organization and maintainability.

Consolidates employee information, HR cases, and general HR query functionality
from hr_api.py and hrms.py into a single module.
"""

import logging
import datetime
from typing import Dict, Any, Optional, List

from google.adk.tools.tool_context import ToolContext
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up logger
logger = logging.getLogger(__name__)

def get_employee_info(
    employee_id: Optional[str] = None,
    email: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves employee information using WagonHR API.

    This tool directly calls the WagonHR employee info endpoint.
    The API automatically identifies the employee using the auth token.

    Args:
        employee_id: Optional employee ID (for compatibility)
        email: Optional employee email (for compatibility)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and employee information
    """
    try:
        logger.info("=== GET EMPLOYEE INFO ===")
        logger.info("Calling WagonHR employee info endpoint directly")

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/employee/info"
        url = f"{base_url}{endpoint}"

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare query parameters
        params = {}
        if employee_id:
            params["employeeId"] = employee_id
        if email:
            params["email"] = email

        logger.info(f"Making GET request to: {url}")
        if params:
            logger.info(f"Query parameters: {params}")

        # Make the API call
        response = requests.get(url, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info("Employee information retrieved successfully")

            return {
                "status": "success",
                "message": "Employee information retrieved successfully",
                "employee": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except Exception:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error retrieving employee info: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving employee info: {str(e)}"
        }

def create_hr_case(
    subject: str,
    description: str,
    category: Optional[str] = "General",
    priority: Optional[int] = 2,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Creates an HR case using WagonHR API.

    This tool directly calls the WagonHR case creation endpoint.

    Args:
        subject: Subject of the HR case
        description: Detailed description of the case
        category: Category of the case (General, Leave, Payroll, etc.)
        priority: Priority level (1=High, 2=Medium, 3=Low)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and case details
    """
    try:
        logger.info("=== CREATE HR CASE ===")
        logger.info("Calling WagonHR case creation endpoint directly")

        # Validate required parameters
        if not subject or not description:
            return {
                "status": "error",
                "message": "subject and description are required"
            }

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/case/create"
        url = f"{base_url}{endpoint}"

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare request payload
        payload = {
            "subject": subject,
            "description": description,
            "category": category,
            "priority": priority
        }

        logger.info(f"Making POST request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.post(url, json=payload, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("HR case created successfully")

            return {
                "status": "success",
                "message": "HR case created successfully",
                "case": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except Exception:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error creating HR case: {e}")
        return {
            "status": "error",
            "message": f"Error creating HR case: {str(e)}"
        }

def get_hr_cases(
    status: Optional[str] = None,
    category: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves HR cases for the authenticated employee using WagonHR API.

    This tool directly calls the WagonHR case list endpoint.

    Args:
        status: Optional status filter (Open, In Progress, Closed)
        category: Optional category filter (General, Leave, Payroll, etc.)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and HR cases
    """
    try:
        logger.info("=== GET HR CASES ===")
        logger.info("Calling WagonHR case list endpoint directly")

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/case/list"
        url = f"{base_url}{endpoint}"

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare query parameters
        params = {}
        if status:
            params["status"] = status
        if category:
            params["category"] = category

        logger.info(f"Making GET request to: {url}")
        if params:
            logger.info(f"Query parameters: {params}")

        # Make the API call
        response = requests.get(url, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info("HR cases retrieved successfully")

            return {
                "status": "success",
                "message": "HR cases retrieved successfully",
                "cases": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except Exception:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error retrieving HR cases: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving HR cases: {str(e)}"
        }

def update_hr_case(
    case_id: str,
    status: Optional[str] = None,
    notes: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Updates an HR case using WagonHR API.

    This tool directly calls the WagonHR case update endpoint.

    Args:
        case_id: ID of the HR case to update
        status: New status for the case
        notes: Additional notes for the case
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated case details
    """
    try:
        logger.info("=== UPDATE HR CASE ===")
        logger.info(f"Calling WagonHR case update endpoint for case: {case_id}")

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/case/update"
        url = f"{base_url}{endpoint}"

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare request payload
        payload = {
            "caseId": case_id
        }

        if status:
            payload["status"] = status
        if notes:
            payload["notes"] = notes

        logger.info(f"Making PUT request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.put(url, json=payload, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("HR case updated successfully")

            return {
                "status": "success",
                "message": f"HR case {case_id} updated successfully",
                "case": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except Exception:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error updating HR case: {e}")
        return {
            "status": "error",
            "message": f"Error updating HR case: {str(e)}"
        }

def get_payroll_info(
    month: Optional[str] = None,
    year: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves payroll information for the authenticated employee using WagonHR API.

    This tool directly calls the WagonHR payroll info endpoint.

    Args:
        month: Optional month filter (MM format)
        year: Optional year filter (YYYY format)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and payroll information
    """
    try:
        logger.info("=== GET PAYROLL INFO ===")
        logger.info("Calling WagonHR payroll info endpoint directly")

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/payroll/info"
        url = f"{base_url}{endpoint}"

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare query parameters
        params = {}
        if month:
            params["month"] = month
        if year:
            params["year"] = year
        elif month:
            # If month is provided without year, use current year
            current_year = datetime.datetime.now().year
            params["year"] = str(current_year)

        logger.info(f"Making GET request to: {url}")
        if params:
            logger.info(f"Query parameters: {params}")

        # Make the API call
        response = requests.get(url, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info("Payroll information retrieved successfully")

            return {
                "status": "success",
                "message": "Payroll information retrieved successfully",
                "payroll": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except Exception:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error retrieving payroll info: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving payroll info: {str(e)}"
        }
