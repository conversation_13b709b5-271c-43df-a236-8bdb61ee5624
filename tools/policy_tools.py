"""
Unified Policy Management Tools Module.

This module provides comprehensive policy search and management functionality using
AWS OpenSearch vector database and the Flexible WagonHR Agent for real-time integration.
All policy-related operations are consolidated here for better organization and maintainability.

Consolidates policy_search_tools.py and aws_opensearch_policy_tool.py into a single module.
"""

import logging
import json
from typing import Dict, Any, Optional, List

from google.adk.tools.tool_context import ToolContext
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up logger
logger = logging.getLogger(__name__)

def search_policies(
    query: str,
    category: Optional[str] = None,
    max_results: Optional[int] = 5,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Searches for HR policies using AWS OpenSearch vector database.
    
    Args:
        query: Search query for policies
        category: Optional policy category filter
        max_results: Maximum number of results to return
        tool_context: Tool context for accessing session state
        
    Returns:
        Dictionary containing operation status and search results
    """
    try:
        logger.info("=== SEARCH POLICIES ===")
        logger.info(f"Searching for policies with query: {query}")
        
        # Import the AWS OpenSearch policy tool
        from tools.aws_opensearch_policy_tool import search_policy_documents
        
        # Use the AWS OpenSearch tool to search policies
        result = search_policy_documents(
            query=query,
            max_results=max_results or 5,
            tool_context=tool_context
        )
        
        if result.get("status") == "success":
            logger.info("Policy search completed successfully")
            
            # Extract search results
            documents = result.get("documents", [])
            
            # Filter by category if specified
            if category:
                filtered_documents = []
                for doc in documents:
                    if doc.get("category", "").lower() == category.lower():
                        filtered_documents.append(doc)
                documents = filtered_documents
            
            return {
                "status": "success",
                "message": f"Found {len(documents)} policy documents",
                "policies": documents,
                "total_results": len(documents)
            }
        else:
            logger.error(f"Failed to search policies: {result.get('message', 'Unknown error')}")
            return result
            
    except Exception as e:
        logger.error(f"Error searching policies: {e}")
        return {
            "status": "error",
            "message": f"Error searching policies: {str(e)}"
        }

def get_policy_by_id(
    policy_id: str,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves a specific policy document by ID using AWS OpenSearch.
    
    Args:
        policy_id: ID of the policy document to retrieve
        tool_context: Tool context for accessing session state
        
    Returns:
        Dictionary containing operation status and policy document
    """
    try:
        logger.info("=== GET POLICY BY ID ===")
        logger.info(f"Retrieving policy with ID: {policy_id}")
        
        # Import the AWS OpenSearch policy tool
        from tools.aws_opensearch_policy_tool import get_policy_document_by_id
        
        # Use the AWS OpenSearch tool to get policy by ID
        result = get_policy_document_by_id(
            document_id=policy_id,
            tool_context=tool_context
        )
        
        if result.get("status") == "success":
            logger.info("Policy document retrieved successfully")
            
            # Extract policy document
            document = result.get("document", {})
            
            return {
                "status": "success",
                "message": "Policy document retrieved successfully",
                "policy": document
            }
        else:
            logger.error(f"Failed to retrieve policy: {result.get('message', 'Unknown error')}")
            return result
            
    except Exception as e:
        logger.error(f"Error retrieving policy: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving policy: {str(e)}"
        }

def search_policy_by_category(
    category: str,
    max_results: Optional[int] = 10,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Searches for policies by category using AWS OpenSearch.
    
    Args:
        category: Policy category to search for
        max_results: Maximum number of results to return
        tool_context: Tool context for accessing session state
        
    Returns:
        Dictionary containing operation status and policy documents
    """
    try:
        logger.info("=== SEARCH POLICIES BY CATEGORY ===")
        logger.info(f"Searching for policies in category: {category}")
        
        # Use the general search function with category filter
        result = search_policies(
            query=f"category:{category}",
            category=category,
            max_results=max_results,
            tool_context=tool_context
        )
        
        return result
            
    except Exception as e:
        logger.error(f"Error searching policies by category: {e}")
        return {
            "status": "error",
            "message": f"Error searching policies by category: {str(e)}"
        }

def get_policy_summary(
    policy_id: str,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Gets a summary of a policy document directly from the vector database.

    This tool retrieves the policy summary that's already stored in the vector database,
    avoiding the need for external processing.

    Args:
        policy_id: ID of the policy document to summarize
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and policy summary
    """
    try:
        logger.info("=== GET POLICY SUMMARY ===")
        logger.info(f"Getting summary for policy ID: {policy_id}")

        # Get the policy document from vector database
        policy_result = get_policy_by_id(policy_id, tool_context)

        if policy_result.get("status") != "success":
            return policy_result

        policy_doc = policy_result.get("policy", {})

        # Extract summary from the policy document (already stored in vector DB)
        summary = policy_doc.get("summary", "")
        title = policy_doc.get("title", "Unknown Policy")
        category = policy_doc.get("category", "Unknown")
        effective_date = policy_doc.get("effective_date", "Unknown")

        if not summary:
            # If no summary is stored, create a basic one from the content
            content = policy_doc.get("content", "")
            if content:
                # Extract first 200 characters as a basic summary
                summary = content[:200].strip()
                if len(content) > 200:
                    summary += "..."
            else:
                summary = "No summary available for this policy."

        logger.info("Policy summary retrieved successfully from vector database")

        return {
            "status": "success",
            "message": "Policy summary retrieved successfully",
            "policy_id": policy_id,
            "policy_title": title,
            "category": category,
            "effective_date": effective_date,
            "summary": summary,
            "source": "vector_database"
        }

    except Exception as e:
        logger.error(f"Error retrieving policy summary: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving policy summary: {str(e)}"
        }

def ask_policy_question(
    question: str,
    policy_id: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Answers questions about HR policies using vector database search and policy content.

    This tool uses semantic search to find relevant policies and provides answers
    based on the policy content stored in the vector database.

    Args:
        question: Question about HR policies
        policy_id: Optional specific policy ID to ask about
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and answer
    """
    try:
        logger.info("=== ASK POLICY QUESTION ===")
        logger.info(f"Answering policy question: {question}")

        relevant_policies = []
        answer_context = ""

        # If specific policy ID provided, get that policy
        if policy_id:
            policy_result = get_policy_by_id(policy_id, tool_context)
            if policy_result.get("status") == "success":
                policy_doc = policy_result.get("policy", {})
                relevant_policies.append(policy_doc)
                answer_context = f"Specific Policy: {policy_doc.get('title', 'Unknown')}"
        else:
            # Search for relevant policies based on the question using vector search
            search_result = search_policies(question, max_results=3, tool_context=tool_context)
            if search_result.get("status") == "success":
                relevant_policies = search_result.get("policies", [])[:2]  # Use top 2 results
                answer_context = f"Found {len(relevant_policies)} relevant policies"

        if not relevant_policies:
            return {
                "status": "error",
                "message": "No relevant policies found to answer your question",
                "question": question,
                "answer": "I couldn't find any policies relevant to your question. Please try rephrasing your question or contact HR directly.",
                "context_used": False
            }

        # Analyze the question and extract relevant information from policies
        answer_parts = []
        policy_references = []

        for policy in relevant_policies:
            title = policy.get("title", "Unknown Policy")
            content = policy.get("content", "")
            summary = policy.get("summary", "")
            category = policy.get("category", "")

            # Use summary if available, otherwise use content excerpt
            policy_info = summary if summary else content[:300] + "..."

            answer_parts.append(f"**{title}** ({category}): {policy_info}")
            policy_references.append({
                "policy_id": policy.get("policy_id"),
                "title": title,
                "category": category,
                "relevance": "High" if policy == relevant_policies[0] else "Medium"
            })

        # Construct comprehensive answer
        answer = f"Based on the relevant HR policies, here's the information regarding your question:\n\n"
        answer += "\n\n".join(answer_parts)

        # Add guidance note
        answer += "\n\n*Note: This information is based on current HR policies. For specific situations or clarifications, please contact the HR department.*"

        logger.info("Policy question answered successfully using vector database")

        return {
            "status": "success",
            "message": "Policy question answered successfully",
            "question": question,
            "answer": answer,
            "context_used": True,
            "policies_referenced": policy_references,
            "source": "vector_database_search",
            "search_method": "semantic_similarity"
        }

    except Exception as e:
        logger.error(f"Error answering policy question: {e}")
        return {
            "status": "error",
            "message": f"Error answering policy question: {str(e)}"
        }

def get_policy_categories(
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves all available policy categories from the vector database.

    This tool searches the vector database to find all unique policy categories
    and provides a summary of available policy types.

    Args:
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and policy categories
    """
    try:
        logger.info("=== GET POLICY CATEGORIES ===")
        logger.info("Retrieving all policy categories from vector database")

        # Import the AWS OpenSearch policy tool
        from tools.aws_opensearch_policy_tool import search_hr_policies_vector

        # Search for all policies to get categories
        result = search_hr_policies_vector(
            query="*",  # Search for all policies
            top_k=50,   # Get more results to capture all categories
            tool_context=tool_context
        )

        if result.get("status") == "success":
            policies = result.get("results", [])

            # Extract unique categories
            categories = {}
            for policy in policies:
                category = policy.get("category", "Unknown")
                subcategory = policy.get("subcategory", "")

                if category not in categories:
                    categories[category] = {
                        "category": category,
                        "subcategories": set(),
                        "policy_count": 0
                    }

                categories[category]["policy_count"] += 1
                if subcategory:
                    categories[category]["subcategories"].add(subcategory)

            # Convert sets to lists for JSON serialization
            category_list = []
            for cat_name, cat_info in categories.items():
                category_list.append({
                    "category": cat_name,
                    "subcategories": list(cat_info["subcategories"]),
                    "policy_count": cat_info["policy_count"]
                })

            logger.info(f"Found {len(category_list)} policy categories")

            return {
                "status": "success",
                "message": f"Found {len(category_list)} policy categories",
                "categories": category_list,
                "total_policies": len(policies),
                "source": "vector_database"
            }
        else:
            logger.error(f"Failed to retrieve policy categories: {result.get('message', 'Unknown error')}")
            return result

    except Exception as e:
        logger.error(f"Error retrieving policy categories: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving policy categories: {str(e)}"
        }

def get_recent_policy_updates(
    days: Optional[int] = 30,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves recently updated policies from the vector database.

    This tool searches for policies that have been updated within the specified
    number of days.

    Args:
        days: Number of days to look back for updates (default: 30)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and recent policy updates
    """
    try:
        logger.info("=== GET RECENT POLICY UPDATES ===")
        logger.info(f"Retrieving policies updated in the last {days} days")

        # Import the AWS OpenSearch policy tool
        from tools.aws_opensearch_policy_tool import search_hr_policies_vector

        # Search for all policies to check update dates
        result = search_hr_policies_vector(
            query="updated recent new",  # Search for recently updated content
            top_k=20,
            tool_context=tool_context
        )

        if result.get("status") == "success":
            policies = result.get("results", [])

            # Filter policies by update date (if available)
            recent_policies = []
            from datetime import datetime, timedelta

            cutoff_date = datetime.now() - timedelta(days=days)

            for policy in policies:
                last_updated = policy.get("last_updated", "")

                # Try to parse the date
                try:
                    if last_updated:
                        # Assume ISO format date
                        update_date = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                        if update_date >= cutoff_date:
                            recent_policies.append({
                                "policy_id": policy.get("policy_id"),
                                "title": policy.get("title"),
                                "category": policy.get("category"),
                                "last_updated": last_updated,
                                "summary": policy.get("summary", "")[:200] + "..." if policy.get("summary", "") else "No summary available",
                                "relevance_score": policy.get("relevance_score", 0)
                            })
                except (ValueError, TypeError):
                    # If date parsing fails, include the policy anyway
                    recent_policies.append({
                        "policy_id": policy.get("policy_id"),
                        "title": policy.get("title"),
                        "category": policy.get("category"),
                        "last_updated": last_updated or "Unknown",
                        "summary": policy.get("summary", "")[:200] + "..." if policy.get("summary", "") else "No summary available",
                        "relevance_score": policy.get("relevance_score", 0)
                    })

            # Sort by relevance score (most relevant first)
            recent_policies.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

            logger.info(f"Found {len(recent_policies)} recently updated policies")

            return {
                "status": "success",
                "message": f"Found {len(recent_policies)} policies updated in the last {days} days",
                "recent_policies": recent_policies,
                "days_searched": days,
                "source": "vector_database"
            }
        else:
            logger.error(f"Failed to retrieve recent policy updates: {result.get('message', 'Unknown error')}")
            return result

    except Exception as e:
        logger.error(f"Error retrieving recent policy updates: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving recent policy updates: {str(e)}"
        }
