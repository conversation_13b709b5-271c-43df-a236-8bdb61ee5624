"""
Memory tools for HR agents.

This module provides tools for agents to search and retrieve information
from long-term memory following Google ADK standards.
"""

import logging
from typing import Dict, Any, Optional

# Set up logger
logger = logging.getLogger(__name__)
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


async def search_hr_memory(
    context,
    query: str,
    max_results: int = 5
) -> Dict[str, Any]:
    """Search HR memory for relevant information from past conversations.
    
    This tool allows agents to search long-term memory for information
    that might be relevant to the current user query, following ADK standards.
    
    Args:
        context: The tool context containing session and memory services
        query: The search query to find relevant information
        max_results: Maximum number of results to return (default: 5)
        
    Returns:
        Dictionary containing search results and metadata
    """
    try:
        logger.info(f"Searching HR memory with query: '{query}'")
        
        # Get session information from context
        session = context.session
        if not session:
            return {
                "success": False,
                "error": "No active session found",
                "results": []
            }
        
        # Get memory service from context (if available)
        memory_service = getattr(context, 'memory_service', None)
        if not memory_service:
            logger.warning("Memory service not available in context")
            return {
                "success": False,
                "error": "Memory service not available",
                "results": []
            }
        
        # Search memory
        search_response = await memory_service.search_memory(
            app_name=session.app_name,
            user_id=session.user_id,
            query=query,
            max_results=max_results
        )
        
        if not search_response or not search_response.results:
            return {
                "success": True,
                "message": "No relevant information found in memory",
                "results": [],
                "query": query
            }
        
        # Format results for agent consumption
        formatted_results = []
        for result in search_response.results:
            formatted_result = {
                "content": result.content,
                "timestamp": getattr(result, 'timestamp', None),
                "session_id": getattr(result, 'session_id', None),
                "relevance_score": getattr(result, 'score', None),
                "metadata": getattr(result, 'metadata', {})
            }
            formatted_results.append(formatted_result)
        
        # Update context state to track memory usage
        context.state["temp:last_memory_search"] = {
            "query": query,
            "results_count": len(formatted_results),
            "timestamp": context.session.state.get("session_metadata", {}).get("last_activity")
        }
        
        logger.info(f"Found {len(formatted_results)} relevant memory entries")
        
        return {
            "success": True,
            "message": f"Found {len(formatted_results)} relevant entries from past conversations",
            "results": formatted_results,
            "query": query,
            "total_results": len(formatted_results)
        }
        
    except Exception as e:
        logger.error(f"Error searching HR memory: {e}")
        return {
            "success": False,
            "error": f"Failed to search memory: {str(e)}",
            "results": [],
            "query": query
        }


async def add_important_info_to_memory(
    context,
    information: str,
    category: str = "general",
    importance: str = "medium"
) -> Dict[str, Any]:
    """Add important information to long-term memory for future reference.
    
    This tool allows agents to explicitly store important information
    that should be remembered across sessions.
    
    Args:
        context: The tool context containing session and memory services
        information: The important information to store
        category: Category of information (e.g., "preference", "decision", "policy")
        importance: Importance level ("low", "medium", "high")
        
    Returns:
        Dictionary indicating success/failure of the operation
    """
    try:
        logger.info(f"Adding important information to memory: category={category}, importance={importance}")
        
        # Get session information from context
        session = context.session
        if not session:
            return {
                "success": False,
                "error": "No active session found"
            }
        
        # Update session state with the important information
        # This will be captured when the session is added to memory
        context.state[f"important_info_{category}"] = {
            "content": information,
            "importance": importance,
            "added_at": context.session.state.get("session_metadata", {}).get("last_activity"),
            "category": category
        }
        
        # Also add to user-scoped state for immediate availability
        context.state[f"user:important_{category}"] = information
        
        logger.info(f"Important information added to session state for memory capture")
        
        return {
            "success": True,
            "message": f"Important {category} information has been noted and will be remembered for future conversations",
            "category": category,
            "importance": importance
        }
        
    except Exception as e:
        logger.error(f"Error adding information to memory: {e}")
        return {
            "success": False,
            "error": f"Failed to add information to memory: {str(e)}"
        }


async def get_user_memory_summary(
    context,
    categories: Optional[list] = None
) -> Dict[str, Any]:
    """Get a summary of what the system remembers about the user.
    
    Args:
        context: The tool context containing session information
        categories: Optional list of categories to filter by
        
    Returns:
        Dictionary containing user memory summary
    """
    try:
        logger.info("Generating user memory summary")
        
        session = context.session
        if not session:
            return {
                "success": False,
                "error": "No active session found"
            }
        
        # Get user-scoped state
        user_state = {}
        for key, value in session.state.items():
            if key.startswith('user:'):
                clean_key = key[5:]  # Remove 'user:' prefix
                if not categories or any(cat in clean_key for cat in categories):
                    user_state[clean_key] = value
        
        # Get session metadata
        session_metadata = session.state.get("session_metadata", {})
        
        summary = {
            "success": True,
            "user_id": session.user_id,
            "session_count": user_state.get("session_count", 1),
            "profile_loaded": user_state.get("profile_loaded", False),
            "preferences": {k: v for k, v in user_state.items() if "preference" in k},
            "important_info": {k: v for k, v in user_state.items() if "important" in k},
            "last_activity": session_metadata.get("last_activity"),
            "session_created": session_metadata.get("created_at")
        }
        
        return summary
        
    except Exception as e:
        logger.error(f"Error generating user memory summary: {e}")
        return {
            "success": False,
            "error": f"Failed to generate memory summary: {str(e)}"
        }


# Tool definitions for ADK integration
hr_memory_tools = [
    {
        "name": "search_hr_memory",
        "description": "Search long-term memory for relevant information from past conversations with this user",
        "function": search_hr_memory,
        "parameters": {
            "query": {
                "type": "string",
                "description": "The search query to find relevant information"
            },
            "max_results": {
                "type": "integer",
                "description": "Maximum number of results to return (default: 5)",
                "default": 5
            }
        }
    },
    {
        "name": "add_important_info_to_memory",
        "description": "Store important information that should be remembered across sessions",
        "function": add_important_info_to_memory,
        "parameters": {
            "information": {
                "type": "string",
                "description": "The important information to store"
            },
            "category": {
                "type": "string",
                "description": "Category of information (e.g., 'preference', 'decision', 'policy')",
                "default": "general"
            },
            "importance": {
                "type": "string",
                "description": "Importance level ('low', 'medium', 'high')",
                "default": "medium"
            }
        }
    },
    {
        "name": "get_user_memory_summary",
        "description": "Get a summary of what the system remembers about the user",
        "function": get_user_memory_summary,
        "parameters": {
            "categories": {
                "type": "array",
                "description": "Optional list of categories to filter by",
                "items": {"type": "string"},
                "required": False
            }
        }
    }
]
