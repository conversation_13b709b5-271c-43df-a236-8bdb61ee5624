"""
LLM-Powered Intelligent Leave Agent.

This module implements a completely dynamic leave request processing system that uses
LLM intelligence for natural language understanding without any hardcoded patterns,
keywords, or business logic. The system adapts to any leave request format through
pure LLM-driven semantic understanding.
"""

import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

# Set up logger
logger = logging.getLogger(__name__)


class ConversationContext:
    """
    Manages conversation state and context across multiple interactions.
    
    This class maintains the conversation history and extracted information
    to enable natural, context-aware dialogue without repeating questions.
    """
    
    def __init__(self, session_id: str):
        """Initialize conversation context."""
        self.session_id = session_id
        self.conversation_history = []
        self.extracted_info = {}
        self.conversation_stage = "initial"
        self.last_clarification_request = None
        self.user_preferences = {}
        
    def add_user_message(self, message: str):
        """Add user message to conversation history."""
        self.conversation_history.append({
            "role": "user",
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
        
    def add_agent_response(self, response: str, extracted_data: Optional[Dict] = None):
        """Add agent response to conversation history."""
        self.conversation_history.append({
            "role": "agent",
            "message": response,
            "extracted_data": extracted_data,
            "timestamp": datetime.now().isoformat()
        })
        
    def update_extracted_info(self, new_info: Dict[str, Any]):
        """Update extracted information with new data."""
        self.extracted_info.update(new_info)
        
    def get_conversation_summary(self) -> str:
        """Get a summary of the conversation for LLM context."""
        if not self.conversation_history:
            return "This is the start of a new conversation about leave requests."
            
        summary_parts = ["Previous conversation context:"]
        
        # Add extracted information if available
        if self.extracted_info:
            summary_parts.append(f"Information already collected: {json.dumps(self.extracted_info, indent=2)}")
            
        # Add recent conversation history (last 3 exchanges)
        recent_history = self.conversation_history[-6:]  # Last 3 user-agent pairs
        for entry in recent_history:
            role = "User" if entry["role"] == "user" else "Agent"
            summary_parts.append(f"{role}: {entry['message']}")
            
        return "\n".join(summary_parts)


class LLMPoweredLeaveAgent:
    """
    Intelligent leave agent that uses pure LLM understanding for dynamic processing.
    
    This agent doesn't rely on any hardcoded patterns, keywords, or business rules.
    Instead, it uses LLM intelligence to understand leave requests in natural language
    and engage in adaptive conversations to collect necessary information.
    """
    
    def __init__(self):
        """Initialize the LLM-powered leave agent."""
        self.active_conversations = {}
        
    def process_leave_request(
        self,
        user_message: str,
        session_id: str = "default",
        employee_id: Optional[str] = None,
        tool_context: Optional[object] = None
    ) -> Dict[str, Any]:
        """
        Process a leave request using pure LLM intelligence.
        
        Args:
            user_message: Natural language message from user
            session_id: Unique session identifier for conversation tracking
            employee_id: Optional employee ID for admin delegation
            tool_context: Tool context for accessing LLM services
            
        Returns:
            Dictionary containing processing results and next steps
        """
        try:
            # Get or create conversation context
            if session_id not in self.active_conversations:
                self.active_conversations[session_id] = ConversationContext(session_id)
                
            context = self.active_conversations[session_id]
            context.add_user_message(user_message)
            
            # Use LLM to understand and process the request
            llm_response = self._llm_understand_request(
                user_message=user_message,
                conversation_context=context,
                employee_id=employee_id,
                tool_context=tool_context
            )
            
            # Update conversation context with LLM insights
            if llm_response.get("extracted_info"):
                context.update_extracted_info(llm_response["extracted_info"])
                
            # Determine next action based on LLM understanding
            next_action = self._determine_next_action(llm_response, context)
            
            # Generate response
            response = self._generate_response(next_action, context, llm_response)
            
            # Update conversation history
            context.add_agent_response(
                response.get("message", ""),
                response.get("extracted_data")
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error in LLM-powered leave processing: {e}")
            return self._generate_fallback_response(user_message, str(e))
    
    def _llm_understand_request(
        self,
        user_message: str,
        conversation_context: ConversationContext,
        employee_id: Optional[str],
        tool_context: Optional[object]
    ) -> Dict[str, Any]:
        """
        Use LLM to understand the user's request with full context awareness.
        
        This method leverages LLM intelligence to:
        - Understand the intent and content of the user's message
        - Extract relevant leave information
        - Determine what information is still needed
        - Generate appropriate responses or questions
        """
        try:
            # Try to use Google ADK LLM if available
            if tool_context and hasattr(tool_context, 'session'):
                try:
                    from google.adk.llm_request import LlmRequest
                    from google.adk.llm_response import LlmResponse

                    # Build comprehensive prompt for LLM understanding
                    system_prompt = self._build_llm_system_prompt()
                    user_prompt = self._build_llm_user_prompt(user_message, conversation_context, employee_id)

                    # Create LLM request
                    request = LlmRequest(
                        model="gemini-2.5-flash",
                        content=f"{system_prompt}\n\n{user_prompt}",
                        temperature=0.3  # Balanced creativity and consistency
                    )

                    # Get LLM response
                    response = tool_context.session.send_llm_request(request)

                    # Parse LLM response
                    return self._parse_llm_response(response)

                except ImportError:
                    logger.info("Google ADK not available, using enhanced fallback processing")
                    # Fall through to enhanced fallback
                except Exception as e:
                    logger.warning(f"Google ADK LLM call failed: {e}, using enhanced fallback")
                    # Fall through to enhanced fallback

            # Use enhanced fallback processing
            logger.info("Using enhanced fallback processing for intelligent understanding")
            fallback_response = self._intelligent_fallback_processing(user_message)
            return self._parse_llm_response(fallback_response)

        except Exception as e:
            logger.error(f"Error in LLM understanding: {e}")
            return {
                "success": False,
                "error": f"LLM processing error: {str(e)}",
                "fallback_needed": True
            }
    
    def _build_llm_system_prompt(self) -> str:
        """Build the system prompt for LLM understanding."""
        return """You are an intelligent leave management assistant with advanced natural language understanding capabilities. Your role is to help users with leave requests through natural conversation.

**Core Capabilities:**
- Understand any style of leave request (casual, formal, incomplete, ambiguous)
- Extract leave information (dates, type, reason, duration) from natural language
- Engage in conversational dialogue to collect missing information
- Maintain context across multiple interactions
- Provide helpful, human-like responses

**Key Principles:**
1. **Dynamic Understanding**: Use semantic understanding, not pattern matching
2. **Context Awareness**: Remember previous conversation and build upon it
3. **Natural Conversation**: Engage like a helpful human assistant
4. **Intelligent Inference**: Infer information from context when possible
5. **Adaptive Responses**: Tailor responses to the user's communication style

**Response Format:**
Always respond with a JSON object containing:
```json
{
  "intent": "apply_leave|cancel_leave|check_balance|help|clarification",
  "confidence": 0.0-1.0,
  "extracted_info": {
    "start_date": "YYYY-MM-DD or null",
    "end_date": "YYYY-MM-DD or null", 
    "leave_type": "sick|casual|annual|emergency|etc or null",
    "reason": "string or null",
    "duration_days": "number or null",
    "half_day": "boolean",
    "employee_id": "string or null"
  },
  "missing_info": ["list of missing required fields"],
  "next_question": "natural language question to ask user or null",
  "ready_to_process": "boolean - true if we have enough info to submit",
  "conversation_notes": "insights about user's communication style and preferences",
  "suggested_response": "natural, conversational response to the user"
}
```

**Important Guidelines:**
- Always maintain a helpful, conversational tone
- Infer information intelligently from context
- Ask follow-up questions naturally, not like a form
- Remember what the user has already told you
- Adapt to the user's communication style (formal/casual)
- Handle ambiguity gracefully with clarifying questions"""

    def _build_llm_user_prompt(
        self,
        user_message: str,
        conversation_context: ConversationContext,
        employee_id: Optional[str]
    ) -> str:
        """Build the user prompt with full context."""
        prompt_parts = []
        
        # Add conversation context
        context_summary = conversation_context.get_conversation_summary()
        prompt_parts.append(f"**Conversation Context:**\n{context_summary}")
        
        # Add current user message
        prompt_parts.append(f"**Current User Message:**\n{user_message}")
        
        # Add employee context if available
        if employee_id:
            prompt_parts.append(f"**Admin Context:**\nThis request is being made by an admin for employee ID: {employee_id}")
            
        # Add current date context
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_day = datetime.now().strftime("%A")
        prompt_parts.append(f"**Current Date Context:**\nToday is {current_day}, {current_date}")
        
        # Add processing instructions
        prompt_parts.append("""**Processing Instructions:**
1. Analyze the user's message in the context of the conversation
2. Extract any leave-related information you can understand
3. Determine what information is still needed (if any)
4. Generate a natural, helpful response
5. Respond with the JSON format specified above

Remember: Be conversational, helpful, and intelligent in your understanding. Don't just look for keywords - understand the meaning and intent.""")
        
        return "\n\n".join(prompt_parts)
    
    def _parse_llm_response(self, response) -> Dict[str, Any]:
        """Parse the LLM response into structured data."""
        try:
            # Extract text from response
            if hasattr(response, 'text'):
                response_text = response.text
            elif hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)
                
            # Find JSON in the response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                parsed_response = json.loads(json_text)
                
                # Validate required fields
                required_fields = ["intent", "confidence", "extracted_info", "suggested_response"]
                for field in required_fields:
                    if field not in parsed_response:
                        parsed_response[field] = None
                        
                parsed_response["success"] = True
                return parsed_response
            else:
                # No valid JSON found
                return {
                    "success": False,
                    "error": "No valid JSON found in LLM response",
                    "raw_response": response_text,
                    "suggested_response": "I'd be happy to help you with your leave request. Could you please tell me what you need?"
                }
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error: {e}")
            return {
                "success": False,
                "error": f"JSON parsing error: {str(e)}",
                "raw_response": response_text if 'response_text' in locals() else str(response),
                "suggested_response": "I'd be happy to help you with your leave request. Could you please tell me what you need?"
            }
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return {
                "success": False,
                "error": f"Response parsing error: {str(e)}",
                "suggested_response": "I'm here to help with your leave request. What can I do for you?"
            }
    
    def _fallback_llm_call(self, request) -> str:
        """Enhanced fallback processing when LLM is not available."""
        logger.warning("Using enhanced fallback processing - providing intelligent responses without full LLM")

        # Extract the user message from the request content
        content = getattr(request, 'content', '')
        user_message = ""

        # Try to extract the user message from the content
        if "**Current User Message:**" in content:
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "**Current User Message:**" in line and i + 1 < len(lines):
                    user_message = lines[i + 1].strip()
                    break

        # Use intelligent fallback processing
        return self._intelligent_fallback_processing(user_message)

    def _intelligent_fallback_processing(self, user_message: str) -> str:
        """Provide intelligent processing even without full LLM integration."""
        import json

        message_lower = user_message.lower()

        # Detect intent patterns
        if any(word in message_lower for word in ["help", "what", "how", "process", "procedure"]):
            return json.dumps({
              "intent": "help",
              "confidence": 0.8,
              "extracted_info": {},
              "missing_info": [],
              "ready_to_process": False,
              "conversation_notes": "User requesting help",
              "suggested_response": "I'm here to help you with leave management! I can assist you with applying for leave, checking your balance, viewing history, or canceling requests. Just tell me what you need in your own words - like 'I need sick leave for tomorrow' or 'Apply vacation from August 1st to 5th'. What would you like to do?"
            })

        # Detect balance check requests
        if any(word in message_lower for word in ["balance", "available", "remaining", "how many", "check"]):
            return json.dumps({
              "intent": "check_balance",
              "confidence": 0.9,
              "extracted_info": {},
              "missing_info": [],
              "ready_to_process": True,
              "conversation_notes": "User wants to check leave balance",
              "suggested_response": "Let me check your current leave balance for you."
            })

        # Detect cancellation requests
        if any(word in message_lower for word in ["cancel", "withdraw", "remove"]):
            return json.dumps({
              "intent": "cancel_leave",
              "confidence": 0.9,
              "extracted_info": {},
              "missing_info": [],
              "ready_to_process": True,
              "conversation_notes": "User wants to cancel leave",
              "suggested_response": "I'll help you cancel your leave request. Let me find your pending leaves for you."
            })

        # Enhanced leave application detection and processing
        if any(word in message_lower for word in ["apply", "request", "need", "want", "take", "leave", "vacation", "sick", "off"]):
            # Try to extract information intelligently
            extracted_info = self._extract_info_fallback(user_message)

            # Check completeness
            missing_fields = []
            if not extracted_info.get("start_date"):
                missing_fields.append("dates")
            if not extracted_info.get("leave_type"):
                missing_fields.append("leave_type")
            if not extracted_info.get("reason"):
                missing_fields.append("reason")

            if len(missing_fields) == 0:
                # Complete information
                return json.dumps({
                  "intent": "apply_leave",
                  "confidence": 0.8,
                  "extracted_info": extracted_info,
                  "missing_info": [],
                  "ready_to_process": True,
                  "conversation_notes": "Complete leave request information",
                  "suggested_response": "Perfect! I have all the information needed for your leave request. Let me process this for you."
                })
            elif len(missing_fields) == 1:
                # Almost complete - ask for missing piece
                if "dates" in missing_fields:
                    response = "I can help you apply for leave! Which dates do you need off?"
                elif "leave_type" in missing_fields:
                    response = "I see you want to take leave. What type of leave do you need (sick, vacation, personal, etc.)?"
                else:  # reason
                    response = "I can process your leave request. Could you provide a brief reason for the leave?"

                return json.dumps({
                  "intent": "clarification",
                  "confidence": 0.7,
                  "extracted_info": extracted_info,
                  "missing_info": missing_fields,
                  "ready_to_process": False,
                  "conversation_notes": "Partial information provided",
                  "suggested_response": response
                })
            else:
                # Need multiple pieces of information
                return json.dumps({
                  "intent": "clarification",
                  "confidence": 0.6,
                  "extracted_info": extracted_info,
                  "missing_info": missing_fields,
                  "ready_to_process": False,
                  "conversation_notes": "Minimal information provided",
                  "suggested_response": "I'd be happy to help you apply for leave! To get started, I need to know: which dates you need, what type of leave (sick, vacation, personal, etc.), and a brief reason. You can tell me all at once or we can go step by step."
                })

        # Default response for unclear requests
        return json.dumps({
          "intent": "clarification",
          "confidence": 0.3,
          "extracted_info": {},
          "missing_info": ["intent", "details"],
          "ready_to_process": False,
          "conversation_notes": "Unclear request",
          "suggested_response": "I'm here to help with your leave management needs. I can help you apply for leave, check your balance, view your history, or cancel requests. What would you like to do today?"
        })

    def _extract_info_fallback(self, text: str) -> dict:
        """Extract basic information using pattern matching as fallback."""
        import re
        from datetime import datetime, timedelta

        info = {}
        text_lower = text.lower()

        # Extract leave type
        leave_types = {
            'sick': ['sick', 'ill', 'medical', 'doctor', 'hospital', 'unwell', 'not feeling well'],
            'vacation': ['vacation', 'holiday', 'annual', 'pto'],
            'personal': ['personal', 'casual'],
            'emergency': ['emergency', 'urgent', 'family emergency']
        }

        for leave_type, keywords in leave_types.items():
            if any(keyword in text_lower for keyword in keywords):
                info['leave_type'] = leave_type
                break

        # Extract basic date patterns
        today = datetime.now().date()

        if 'tomorrow' in text_lower:
            tomorrow = today + timedelta(days=1)
            info['start_date'] = tomorrow.strftime('%Y-%m-%d')
            info['end_date'] = tomorrow.strftime('%Y-%m-%d')
        elif 'today' in text_lower:
            info['start_date'] = today.strftime('%Y-%m-%d')
            info['end_date'] = today.strftime('%Y-%m-%d')
        elif 'next friday' in text_lower:
            days_ahead = (4 - today.weekday()) % 7
            if days_ahead <= 0:
                days_ahead += 7
            next_friday = today + timedelta(days=days_ahead)
            info['start_date'] = next_friday.strftime('%Y-%m-%d')
            info['end_date'] = next_friday.strftime('%Y-%m-%d')

        # Extract basic reason patterns
        reason_patterns = [
            r'(?:because|due to|for|as)\s+(.+?)(?:\.|$|,)',
            r'(?:not feeling well|sick|ill)',
            r'(?:doctor|medical|appointment)',
            r'(?:personal|family|emergency)'
        ]

        for pattern in reason_patterns:
            match = re.search(pattern, text_lower)
            if match:
                if match.groups():
                    info['reason'] = match.group(1).strip()
                else:
                    info['reason'] = match.group(0).strip()
                break

        # If we detected sick leave but no explicit reason, infer it
        if info.get('leave_type') == 'sick' and not info.get('reason'):
            if any(word in text_lower for word in ['not feeling well', 'sick', 'ill', 'unwell']):
                info['reason'] = 'Not feeling well'
            elif any(word in text_lower for word in ['doctor', 'medical', 'appointment']):
                info['reason'] = 'Medical appointment'
            else:
                info['reason'] = 'Health reasons'

        return info

    def _determine_next_action(
        self,
        llm_response: Dict[str, Any],
        context: ConversationContext
    ) -> Dict[str, Any]:
        """
        Determine the next action based on LLM understanding and conversation context.

        This method uses the LLM's analysis to decide whether to:
        - Process the leave request
        - Ask for clarification
        - Provide help or information
        - Handle other intents
        """
        if not llm_response.get("success"):
            return {
                "action": "error_handling",
                "reason": "LLM processing failed",
                "llm_response": llm_response
            }

        intent = llm_response.get("intent", "clarification")
        confidence = llm_response.get("confidence", 0.0)
        ready_to_process = llm_response.get("ready_to_process", False)

        # High confidence and ready to process
        if confidence >= 0.8 and ready_to_process and intent == "apply_leave":
            return {
                "action": "process_leave_request",
                "reason": "High confidence, complete information",
                "llm_response": llm_response
            }

        # Ready to process with reasonable confidence (for fallback processing)
        if ready_to_process and intent == "apply_leave" and confidence >= 0.7:
            return {
                "action": "process_leave_request",
                "reason": "Complete information, ready to process",
                "llm_response": llm_response
            }

        # Need clarification or more information
        if intent == "clarification" or not ready_to_process:
            return {
                "action": "ask_clarification",
                "reason": "Missing information or low confidence",
                "llm_response": llm_response
            }

        # Other intents (help, cancel, balance check)
        if intent in ["help", "cancel_leave", "check_balance"]:
            return {
                "action": f"handle_{intent}",
                "reason": f"User requested {intent}",
                "llm_response": llm_response
            }

        # Default to clarification
        return {
            "action": "ask_clarification",
            "reason": "Uncertain intent or incomplete information",
            "llm_response": llm_response
        }

    def _generate_response(
        self,
        next_action: Dict[str, Any],
        context: ConversationContext,
        llm_response: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate the appropriate response based on the determined action.

        This method creates user-facing responses that maintain natural conversation flow
        while providing the necessary information or requesting clarification.
        """
        action = next_action.get("action", "ask_clarification")

        if action == "process_leave_request":
            return self._generate_leave_processing_response(llm_response, context)

        elif action == "ask_clarification":
            return self._generate_clarification_response(llm_response, context)

        elif action == "handle_help":
            return self._generate_help_response(llm_response, context)

        elif action == "handle_cancel_leave":
            return self._generate_cancel_response(llm_response, context)

        elif action == "handle_check_balance":
            return self._generate_balance_check_response(llm_response, context)

        elif action == "error_handling":
            return self._generate_error_response(next_action, context)

        else:
            # Default clarification response
            return self._generate_clarification_response(llm_response, context)

    def _generate_leave_processing_response(
        self,
        llm_response: Dict[str, Any],
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Generate response for processing a complete leave request."""
        extracted_info = llm_response.get("extracted_info", {})
        suggested_response = llm_response.get("suggested_response", "")

        return {
            "status": "success",
            "action": "process_leave",
            "message": suggested_response or "Perfect! I have all the information needed for your leave request.",
            "extracted_data": extracted_info,
            "start_date": extracted_info.get("start_date"),
            "end_date": extracted_info.get("end_date"),
            "leave_type": extracted_info.get("leave_type"),
            "reason": extracted_info.get("reason"),
            "half_day": extracted_info.get("half_day", False),
            "employee_id": extracted_info.get("employee_id"),
            "conversation_context": context.extracted_info,
            "llm_powered": True,
            "confidence": llm_response.get("confidence", 1.0)
        }

    def _generate_clarification_response(
        self,
        llm_response: Dict[str, Any],
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Generate response asking for clarification or additional information."""
        suggested_response = llm_response.get("suggested_response", "")
        next_question = llm_response.get("next_question", "")
        missing_info = llm_response.get("missing_info", [])

        # Use LLM's suggested response or next question
        message = suggested_response or next_question or "I'd be happy to help you with your leave request. Could you provide a bit more information?"

        return {
            "status": "clarification_needed",
            "message": message,
            "missing_fields": missing_info,
            "extracted_info": llm_response.get("extracted_info", {}),
            "conversation_context": context.extracted_info,
            "llm_powered": True,
            "confidence": llm_response.get("confidence", 0.5)
        }

    def _generate_help_response(
        self,
        llm_response: Dict[str, Any],
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Generate help response."""
        suggested_response = llm_response.get("suggested_response", "")

        default_help = """I'm here to help you with leave management! I can assist you with:

🏖️ **Applying for Leave**
Just tell me what you need in your own words - like "I need sick leave tomorrow" or "I want to take vacation next week"

📊 **Checking Leave Balance**
Ask me about your available leave days

📋 **Leave History & Status**
I can show you your past leaves and pending requests

❌ **Canceling Leave**
Let me know if you need to cancel any leave requests

What would you like to do?"""

        message = suggested_response or default_help

        return {
            "status": "help",
            "message": message,
            "llm_powered": True,
            "confidence": llm_response.get("confidence", 1.0)
        }

    def _generate_cancel_response(
        self,
        llm_response: Dict[str, Any],
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Generate response for leave cancellation."""
        suggested_response = llm_response.get("suggested_response", "")
        extracted_info = llm_response.get("extracted_info", {})

        message = suggested_response or "I'll help you cancel your leave request. Let me find the details for you."

        return {
            "status": "cancel_leave",
            "message": message,
            "extracted_data": extracted_info,
            "llm_powered": True,
            "confidence": llm_response.get("confidence", 0.8)
        }

    def _generate_balance_check_response(
        self,
        llm_response: Dict[str, Any],
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Generate response for balance check."""
        suggested_response = llm_response.get("suggested_response", "")

        message = suggested_response or "Let me check your current leave balance for you."

        return {
            "status": "check_balance",
            "message": message,
            "llm_powered": True,
            "confidence": llm_response.get("confidence", 1.0)
        }

    def _generate_error_response(
        self,
        next_action: Dict[str, Any],
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Generate intelligent error response using LLM understanding."""
        llm_response = next_action.get("llm_response", {})
        suggested_response = llm_response.get("suggested_response", "")

        # Use LLM's suggested response if available, otherwise provide helpful fallback
        message = suggested_response or "I'm here to help with your leave request. Could you tell me what you need in your own words?"

        return {
            "status": "error",
            "message": message,
            "error_type": "llm_processing",
            "llm_powered": True,
            "confidence": 0.3
        }

    def _generate_fallback_response(self, user_message: str, error: str) -> Dict[str, Any]:
        """Generate fallback response when LLM processing fails completely."""
        return {
            "status": "error",
            "message": "I'm here to help with your leave request. Could you tell me what you need - like the dates, type of leave, and reason?",
            "error_type": "system_error",
            "error_details": error,
            "llm_powered": False,
            "confidence": 0.1
        }


# Global instance for easy access
llm_leave_agent = LLMPoweredLeaveAgent()


def request_leave_llm_powered(
    leave_request_text: str,
    session_id: str = "default",
    employee_id: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Process a leave request using LLM-powered intelligent understanding.

    This function provides a completely dynamic approach to leave request processing
    that adapts to any natural language input without requiring code changes.

    Args:
        leave_request_text: Natural language leave request
        session_id: Unique session identifier for conversation tracking
        employee_id: Optional employee ID for admin delegation
        tool_context: Tool context for accessing LLM services

    Returns:
        Dictionary containing processing results and conversation state
    """
    logger.info(f"Processing LLM-powered leave request: '{leave_request_text}' (session: {session_id})")

    try:
        result = llm_leave_agent.process_leave_request(
            user_message=leave_request_text,
            session_id=session_id,
            employee_id=employee_id,
            tool_context=tool_context
        )

        logger.info(f"LLM-powered processing result: {result.get('status', 'unknown')} (confidence: {result.get('confidence', 0.0)})")
        return result

    except Exception as e:
        logger.error(f"Error in LLM-powered leave processing: {e}")
        return {
            "status": "error",
            "message": "I'm here to help with your leave request. Could you tell me what you need in your own words?",
            "error_type": "processing_error",
            "error_details": str(e),
            "llm_powered": False,
            "confidence": 0.0
        }
