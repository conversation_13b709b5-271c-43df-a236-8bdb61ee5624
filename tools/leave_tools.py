"""
Unified Leave Management Tools Module.

This module provides comprehensive leave management functionality using the
Flexible WagonHR Agent for real-time API integration. All leave-related
operations are consolidated here for better organization and maintainability.

Replaces both hr_api.py leave functions and old leave_tools.py mock functions
with a single, unified module that uses real WagonHR APIs.
"""

import logging
import datetime
from datetime import timedelta
import re
import os
from typing import Dict, Any, Optional, List

from google.adk.tools.tool_context import ToolContext
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up logger with forced configuration
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)  # Ensure all logs are captured

# Force logger configuration to ensure visibility
def setup_logger():
    """Setup logger with guaranteed console output."""
    import sys

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create console handler that outputs to stdout
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # Disable propagation to avoid interference from root logger
    logger.propagate = False

    # Suppress noisy third-party loggers
    noisy_loggers = [
        'google', 'google.adk', 'google.genai', 'google_genai', 'google_adk',
        'google_adk.google.adk.models.google_llm', 'httpx', 'urllib3', 'requests',
        'asyncio', 'werkzeug', 'uvicorn.error', 'uvicorn.access'
    ]

    for logger_name in noisy_loggers:
        noisy_logger = logging.getLogger(logger_name)
        noisy_logger.setLevel(logging.CRITICAL)
        noisy_logger.propagate = False

    # Test the logger
    logger.info("Leave tools logger configured successfully")

# Setup the logger immediately
setup_logger()

def suppress_noisy_logs():
    """Aggressively suppress all noisy third-party logs."""
    # List of all known noisy loggers
    noisy_loggers = [
        'google', 'google.adk', 'google.genai', 'google_genai', 'google_adk',
        'google_adk.google.adk.models.google_llm', 'google.adk.models.google_llm',
        'google.adk.models', 'google.adk.tools', 'google.adk.core',
        'httpx', 'urllib3', 'requests', 'asyncio', 'werkzeug',
        'uvicorn.error', 'uvicorn.access', 'uvicorn', 'fastapi',
        'opensearch', 'opensearchpy', 'elasticsearch'
    ]

    for logger_name in noisy_loggers:
        noisy_logger = logging.getLogger(logger_name)
        noisy_logger.setLevel(logging.CRITICAL)
        noisy_logger.propagate = False
        # Remove all handlers from noisy loggers
        for handler in noisy_logger.handlers[:]:
            noisy_logger.removeHandler(handler)


def get_leave_balance(
    employee_id: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Gets the current leave balance for the authenticated employee.

    This tool directly calls the WagonHR leave balance endpoint.
    The API automatically identifies the employee using the auth token.

    Args:
        employee_id: Optional employee ID (for admin mode)
        start_date: Optional start date for year range (YYYY-MM-DD)
        end_date: Optional end date for year range (YYYY-MM-DD)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing leave balance information in the exact API response format
    """
    try:
        logger.info("=== GET LEAVE BALANCE ===")
        logger.info("Calling WagonHR leave balance endpoint directly")
        logger.info(f"Function called with parameters: employee_id={employee_id}, start_date={start_date}, end_date={end_date}")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-leaves"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        logger.info(f"Authentication headers obtained: {list(auth_headers.keys())}")

        # Prepare headers with authentication and tenant context (matching the curl command)
        headers = {
            **auth_headers,
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.9",
            "origin": "https://qa-wagonhr.mouritech.net",
            "referer": "https://qa-wagonhr.mouritech.net/",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        logger.info(f"get leave balance url: {url}")
        logger.info(f"Making GET request to: {url}")
        logger.info(f"get leave balance employee_id: {employee_id}")

        # Prepare query parameters (matching the curl command structure)
        params = {}
        
        # Set default year range if not provided
        if not start_date:
            start_date = "2025-01-01"  # Default to current year start
        if not end_date:
            end_date = "2025-12-31"    # Default to current year end
            
        params["yearStart"] = start_date
        params["yearEnd"] = end_date
        
        # Add employee number if provided, otherwise use null as in the curl command
        if employee_id and isinstance(employee_id, (str, int)):
            params["employeeNo"] = employee_id
        else:
            params["employeeNo"] = "null"

        logger.info(f"get leave balance params: {params}")
        logger.info(f"Request headers: {headers}")


        logger.info(f"get leave balance employee_id: {employee_id}")

        params = {}
        if employee_id:
            params["employeeNo"] = employee_id
        if start_date:
            params["yearStart"] = start_date
        if end_date:
            params["yearEnd"] = end_date

            logger.info(f"get leave balance params: {params}")
        # Make the API call
        response = requests.get(url, params=params, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        logger.info(f"Response text: {response.text}")

        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"Leave balance data received: {data}")
                logger.info("Leave balance retrieved successfully")

                # Process the data to create formatted balance
                formatted_balance = []
                if isinstance(data, list) and len(data) > 0:
                    for item in data:
                        if isinstance(item, dict):
                            leave_type = item.get("leaveTypeName", "unknown")
                            balance = item.get("availableLeaves", 0)
                            description = item.get("description", "")
                            formatted_balance.append({
                                "leave_type": leave_type,
                                "balance": balance,
                                "unit": "days",
                                "description": description
                            })

                # Return both raw API response and formatted balance
                return {
                    "status": "success",
                    "message": "Leave balance retrieved successfully",
                    "data": data,
                    "formatted_balance": formatted_balance
                }
            except Exception as json_error:
                logger.error(f"Failed to parse JSON response: {json_error}")
                logger.error(f"Raw response: {response.text}")
                return {
                    "status": "error",
                    "message": f"Failed to parse response from server: {str(json_error)}"
                }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error getting leave balance: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting leave balance: {str(e)}"
        }

def request_leave(
    employee_id: Optional[str] = None,
    email: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    leave_type: Optional[str] = None,
    reason: Optional[str] = None,
    half_day: Optional[bool] = False,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Creates a new leave request using WagonHR API.

    This tool directly calls the WagonHR leave request endpoint.
    The API automatically identifies the employee using the auth token.

    Args:
        employee_id: Optional employee ID (for compatibility)
        email: Optional employee email (for compatibility)
        start_date: Start date of the leave (YYYY-MM-DD)
        end_date: End date of the leave (YYYY-MM-DD)
        leave_type: Type of leave as per the leave balance
        reason: Reason for the leave
        half_day: Whether this is a half-day leave
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and leave request details
    """
    try:
        logger.info("=== REQUEST LEAVE ===")
        logger.info("Calling WagonHR leave request endpoint directly")

        # Validate required parameters
        if not start_date or not end_date or not leave_type or not reason:
            return {
                "status": "error",
                "message": "start_date, end_date, leave_type, and reason are required"
            }

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth
        from datetime import datetime

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-leaves/request"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Get current timestamp in the required format
        current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S+05:30")

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.9",
            "content-type": "application/json",
            "origin": "https://qa-wagonhr.mouritech.net",
            "referer": "https://qa-wagonhr.mouritech.net/",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001",
            "x-pub-key": "80183aab-e043-4b56-b78a-f15009c8e08d"
        }

        # Get leave balance to find the correct leave type detail ID
        leave_balance_result = get_leave_balance(employee_id, None, None, tool_context)
        if leave_balance_result.get("status") != "success":
            return {
                "status": "error",
                "message": "Failed to get leave balance information",
                "details": leave_balance_result
            }

        leave_details = leave_balance_result.get("data", [])
        
        # Find the matching leave type in the array
        leave_type_detail = None
        for item in leave_details:
            if isinstance(item, dict):
                item_leave_type = item.get("leaveTypeName", "").lower()
                item_description = item.get("description", "").lower()
                
                # Check if the requested leave type matches this item
                if (leave_type.lower() == item_leave_type or 
                    leave_type.lower() in item_leave_type or 
                    item_leave_type in leave_type.lower() or
                    leave_type.lower() == item_description or
                    leave_type.lower() in item_description or
                    item_description in leave_type.lower()):
                    
                    leave_type_detail = item
                    break

        if not leave_type_detail:
            available_types = []
            for item in leave_details:
                if isinstance(item, dict):
                    leave_type_name = item.get("leaveTypeName", "Unknown")
                    description = item.get("description", "")
                    available_types.append(f"{leave_type_name} ({description})")
            
            return {
                "status": "error",
                "message": f"Leave type '{leave_type}' not found in available leave types.",
                "available_types": available_types
            }

        leave_type_detail_id = leave_type_detail.get("leaveTypeDetailId")
        if not leave_type_detail_id:
            return {
                "status": "error",
                "message": f"Could not find leave type detail ID for '{leave_type}'"
            }

        # Determine day preference based on half_day flag
        if half_day:
            # For half-day, we need to determine if it's first half or second half
            # Since we don't have specific half-day type in the current parameters,
            # we'll default to "First Half" for now
            # TODO: Add a parameter to specify first/second half explicitly
            day_preference = "First Half"
        else:
            day_preference = "Full Day"

        # Prepare request payload according to WagonHR API structure
        payload = {
            "leaveTypeDetailId": leave_type_detail_id,
            "startDate": start_date,
            "endDate": end_date,
            "note": reason,
            "actionTakenOn": current_time,
            "firstDayPreference": day_preference,
            "lastDayPreference": "Full Day",
            "notifyTo": [""],
            "leaveCount": 1,
            "employeeNo": None
        }

        logger.info(f"URL: {url}")
        logger.info(f"Payload: {payload}")
        logger.info(f"Headers: {headers}")

        # Make the API call
        response = requests.post(url, json=payload, headers=headers, timeout=30)

        logger.info(f"API Response status: {response.status_code}")
        logger.info(f"API Response headers: {dict(response.headers)}")
        logger.info(f"API Response text: {response.text}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("Leave request submitted successfully")

            return {
                "status": "success",
                "message": "Leave request submitted successfully",
                "leave_request": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error submitting leave request: {e}")
        return {
            "status": "error",
            "message": f"Error submitting leave request: {str(e)}"
        }

def get_leave_history(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    status: Optional[str] = None,
    tool_context: Optional[object] = None,
    employee_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieves leave history for the authenticated employee using WagonHR API.

    This tool directly calls the WagonHR leave history endpoint.

    Args:
        start_date: Optional start date filter (YYYY-MM-DD)
        end_date: Optional end date filter (YYYY-MM-DD)
        status: Optional status filter (Pending, Approved, Rejected, Cancelled)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and leave history details
    """
    try:
        logger.info("=== GET LEAVE HISTORY ===")
        logger.info("Calling WagonHR leave history endpoint directly")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-leavey-history"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        # Prepare query parameters
        params = {}
        if start_date:
            params["startDate"] = start_date
        if end_date:
            params["endDate"] = end_date
        if status:
            params["status"] = status
        if employee_id:
            params["employeeNo"] = employee_id

        logger.info(f"Making GET request to: {url}")
        if params:
            logger.info(f"Query parameters: {params}")

        # Make the API call
        print("get leave history url", url)
        print("get leave history headers", headers)
        print("get leave history params", params)
        response = requests.get(url, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info("Leave history retrieved successfully")

            return {
                "status": "success",
                "message": "Leave history retrieved successfully",
                "leave_history": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error retrieving leave history: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving leave history: {str(e)}"
        }

def approve_reject_leave(
    leave_id: str,
    notes: Optional[str] = None,
    leave_status: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Approves a leave request using WagonHR API.

    This tool directly calls the WagonHR leave approval endpoint.

    Args:
        leave_id: ID of the leave request to approve
        notes: Optional notes from the approver
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated leave request details
    """
    try:
        logger.info("=== APPROVE LEAVE ===")
        logger.info(f"Calling WagonHR leave approval endpoint for request: {leave_id}")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/managers-view/leave/approval-status"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        # Prepare request payload
        payload = {
            "employeeLeaveApplicationId": leave_id,
            "leaveStatus": leave_status,
            "comments": notes
        }

        if notes:
            payload["comments"] = notes

        logger.info(f"Making PUT request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.put(url, json=payload, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("Leave request approved successfully")

            return {
                "status": "success",
                "message": f"Leave request {leave_id} approved successfully",
                "leave_request": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error approving leave request: {e}")
        return {
            "status": "error",
            "message": f"Error approving leave request: {str(e)}"
        }

def approve_reject_comp_off(
    leave_id: str,
    notes: Optional[str] = None,
    leave_status: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Approves a leave request using WagonHR API.

    This tool directly calls the WagonHR leave approval endpoint.

    Args:
        leave_id: ID of the leave request to approve
        notes: Optional notes from the approver
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated leave request details
    """
    try:
        logger.info("=== APPROVE LEAVE ===")
        logger.info(f"Calling WagonHR leave approval endpoint for request: {leave_id}")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/managers-view/leave/approval-status"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        # Prepare request payload
        payload = {
            "employeeCompOffId": leave_id,
            "leaveStatus": leave_status,
            "comments": notes,
            "days": days
        }

        if notes:
            payload["comments"] = notes

        logger.info(f"Making PUT request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.put(url, json=payload, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("Leave request approved successfully")

            return {
                "status": "success",
                "message": f"Leave request {leave_id} approved successfully",
                "leave_request": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error approving leave request: {e}")
        return {
            "status": "error",
            "message": f"Error approving leave request: {str(e)}"
        }


def cancel_leave(
    leave_id: str,
    reason: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Cancels a leave request using WagonHR API.

    This tool directly calls the WagonHR leave cancellation endpoint.

    Args:
        leave_id: ID of the leave request to cancel
        reason: Reason for cancellation
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated leave request details
    """
    try:
        logger.info("=== CANCEL LEAVE ===")
        logger.info(f"Calling WagonHR leave cancellation endpoint for request: {leave_id}")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = f"/api/hrms/leave-attendance/employee-leave-application/cancel-leave?leaveApplicationId={leave_id}"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        logger.info(f"Making DELETE request to: {url}")
        if reason:
            logger.info(f"Cancellation reason: {reason}")

        # Make the API call (no payload needed as leaveApplicationId is in query parameter)
        response = requests.get(url, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201, 204]:
            try:
                data = response.json() if response.content else {}
            except:
                data = {}
            logger.info("Leave request cancelled successfully")

            return {
                "status": "success",
                "message": f"Leave request cancelled successfully",
                "leave_request": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error cancelling leave request: {e}")
        return {
            "status": "error",
            "message": f"Error cancelling leave request: {str(e)}"
        }

def modify_leave(
    leave_id: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    leave_type: Optional[str] = None,
    reason: Optional[str] = None,
    half_day: Optional[bool] = None,
    modification_reason: Optional[str] = "Schedule change",
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Modifies an existing leave request using WagonHR API.

    This tool directly calls the WagonHR leave modification endpoint.

    Args:
        leave_id: ID of the leave request to modify
        start_date: New start date (YYYY-MM-DD)
        end_date: New end date (YYYY-MM-DD)
        leave_type: New leave type
        reason: New reason for the leave
        half_day: New half-day flag
        modification_reason: Reason for the modification
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated leave request details
    """
    try:
        logger.info("=== MODIFY LEAVE ===")
        logger.info(f"Calling WagonHR leave modification endpoint for request: {leave_id}")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave/modify"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        # Prepare request payload
        payload = {
            "requestId": leave_id
        }

        # Add optional fields only if provided
        if start_date:
            payload["startDate"] = start_date
        if end_date:
            payload["endDate"] = end_date
        if leave_type:
            payload["leaveType"] = leave_type.lower()
        if reason:
            payload["reason"] = reason
        if half_day is not None:
            payload["halfDay"] = half_day
        if modification_reason:
            payload["modificationReason"] = modification_reason

        logger.info(f"Making PUT request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.put(url, json=payload, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("Leave request modified successfully")

            return {
                "status": "success",
                "message": f"Leave request {leave_id} modified successfully",
                "leave_request": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error modifying leave request: {e}")
        return {
            "status": "error",
            "message": f"Error modifying leave request: {str(e)}"
        }

def get_pending_leaves(
    employee_id: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves all pending leave requests for the authenticated employee using WagonHR API.

    This tool directly calls the WagonHR pending leaves endpoint.

    Args:
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and pending leave requests
    """
    try:
        logger.info("=== GET PENDING LEAVES ===")
        logger.info("Calling WagonHR pending leaves endpoint directly")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-leave-application/pending-leaves"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        logger.info(f"Making GET request to: {url}")

        params = {}
        if employee_id:
            params["employeeNo"] = employee_id

        logger.info(f"Params: {params}")

        # Make the API call
        response = requests.get(url, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info("Pending leaves retrieved successfully")

            return {
                "status": "success",
                "message": f"Found {len(data) if isinstance(data, list) else 'unknown number of'} pending leave requests",
                "pending_leaves": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error retrieving pending leaves: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving pending leaves: {str(e)}"
        }

def request_leave_intelligent(
    leave_request_text: Optional[str] = None,
    employee_id: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    leave_type: Optional[str] = None,
    reason: Optional[str] = None,
    half_day: Optional[bool] = False,
    tool_context: Optional[object] = None,
    **kwargs  # Accept any additional keyword arguments for flexibility
) -> Dict[str, Any]:
    """
    Creates a leave request using LLM-powered intelligent parsing of natural language.

    This tool uses AI to understand natural language requests like:
    - "I need sick leave for next Tuesday"
    - "Apply annual leave from tomorrow to Friday"
    - "I want to take a day off on coming Monday for personal reasons"

    Supports two modes:
    1. Self-service mode: When employee_id is not provided, the API identifies
       the employee using the auth token (for regular users)
    2. Admin mode: When employee_id is provided, admin can apply leave on
       behalf of another employee (for HR/managers)

    Args:
        leave_request_text: Natural language leave request (REQUIRED - primary parameter)
        employee_id: Optional employee ID (for admin mode - applying on behalf of others)
        start_date: Optional start date override (YYYY-MM-DD)
        end_date: Optional end date override (YYYY-MM-DD)
        leave_type: Optional leave type override
        reason: Optional reason override
        half_day: Optional half-day flag override
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and leave request details
    """
    try:
        # Suppress noisy third-party logs
        suppress_noisy_logs()

        logger.info("=== LLM-POWERED INTELLIGENT LEAVE REQUEST ===")
        logger.info(f"Processing natural language request: '{leave_request_text}'")
        logger.info(f"Input parameters - employee_id: {employee_id}, start_date: {start_date}, end_date: {end_date}")

        # Add comprehensive debugging
        print("🔍 DEBUG: Function called with parameters:")
        print(f"  leave_request_text: '{leave_request_text}'")
        print(f"  employee_id: '{employee_id}'")
        print(f"  start_date: '{start_date}'")
        print(f"  end_date: '{end_date}'")
        print(f"  leave_type: '{leave_type}'")
        print(f"  reason: '{reason}'")
        print(f"  half_day: {half_day}")
        print(f"  tool_context: {tool_context}")
        print(f"  kwargs: {kwargs}")

        # Handle potential parameter mismatches from agent calling
        if kwargs:
            print(f"🔍 Additional kwargs received: {kwargs}")
            # Check if leave_request_text might be in kwargs
            if not leave_request_text and 'text' in kwargs:
                leave_request_text = kwargs['text']
                print(f"🔄 Using 'text' from kwargs as leave_request_text: '{leave_request_text}'")
            elif not leave_request_text and 'request' in kwargs:
                leave_request_text = kwargs['request']
                print(f"🔄 Using 'request' from kwargs as leave_request_text: '{leave_request_text}'")

        # Validate input parameters
        if leave_request_text and len(leave_request_text.strip()) > 0:
            print("✅ leave_request_text is valid")
        else:
            print("❌ leave_request_text is invalid or empty")

        # Validate that we have a leave request text to parse
        if not leave_request_text:
            logger.error("No leave request text provided")
            logger.error(f"Function called with parameters: leave_request_text={leave_request_text}, employee_id={employee_id}, start_date={start_date}, end_date={end_date}, leave_type={leave_type}, reason={reason}")

            # Check if the agent might have passed the text in the wrong parameter
            if employee_id and not start_date and not end_date and not leave_type and not reason:
                # Looks like the agent might have passed the text as employee_id
                logger.warning(f"Detected possible parameter mismatch - employee_id contains text: '{employee_id}'")
                if len(str(employee_id)) > 20:  # Employee IDs are usually short
                    logger.info("Attempting to use employee_id as leave_request_text")
                    leave_request_text = str(employee_id)
                    employee_id = None  # Reset employee_id
                else:
                    return {
                        "status": "error",
                        "message": "No leave request text provided. Please provide a natural language leave request as the first parameter.",
                        "suggestion": "Example: 'I need sick leave for tomorrow because I'm not feeling well'",
                        "debug_info": f"Received employee_id: '{employee_id}' but no leave_request_text"
                    }
            else:
                return {
                    "status": "error",
                    "message": "No leave request text provided. Please provide a natural language leave request as the first parameter.",
                    "suggestion": "Example: 'I need sick leave for tomorrow because I'm not feeling well'"
                }

        # Use LLM to intelligently parse the leave request
        print("Starting LLM parsing of leave request")
        logger.info("Starting LLM parsing of leave request")
        parsed_info = _llm_parse_leave_request(leave_request_text, tool_context)
        print(f"LLM parsing result: {parsed_info}")
        logger.info(f"LLM parsing result: {parsed_info}")

        if not parsed_info.get("success"):
            print(f"LLM parsing failed: {parsed_info}")
            logger.error(f"LLM parsing failed: {parsed_info}")

            # Try a simple fallback for common cases like "tomorrow"
            print("🔄 Attempting simple fallback parsing...")
            fallback_result = _simple_fallback_parsing(leave_request_text)

            if fallback_result.get("success"):
                print(f"✅ Fallback parsing succeeded: {fallback_result}")
                logger.info(f"✅ Fallback parsing succeeded: {fallback_result}")
                parsed_info = fallback_result
            else:
                print(f"❌ Fallback parsing also failed: {fallback_result}")
                logger.error(f"❌ Fallback parsing also failed: {fallback_result}")

                # Provide more detailed error information
                error_message = parsed_info.get("error", "Could not understand the leave request")

                # If it's a date parsing issue, provide specific guidance
                if "date" in error_message.lower() or "tomorrow" in leave_request_text.lower():
                    return {
                        "status": "error",
                        "message": f"Could not parse the date from your request: '{leave_request_text}'. The system had trouble understanding 'tomorrow' or other date expressions.",
                        "suggestion": "Please try using a specific date format like '2024-07-27' or rephrase your request. Examples: 'Apply sick leave for 2024-07-27' or 'I need sick leave on July 27th, 2024'",
                        "parsing_error": error_message,
                        "fallback_error": fallback_result.get("error")
                    }
                else:
                    return {
                        "status": "error",
                        "message": error_message,
                        "suggestion": "Please specify the date, leave type, and reason. Example: 'I need sick leave for next Tuesday because I'm not feeling well'",
                        "original_request": leave_request_text
                    }

        # Extract the parsed information
        start_date = parsed_info.get("start_date")
        end_date = parsed_info.get("end_date", start_date)  # Default to same day if not specified
        leave_type = parsed_info.get("leave_type")
        reason = parsed_info.get("reason")
        half_day = parsed_info.get("half_day", False)

        print(f"LLM parsed leave request - Start: {start_date}, End: {end_date}, Type: {leave_type}, Reason: {reason}")
        logger.info(f"LLM parsed leave request - Start: {start_date}, End: {end_date}, Type: {leave_type}, Reason: {reason}")

        # Validate that we have all required information
        missing_info = []
        if not leave_type or leave_type.strip() == "":
            missing_info.append("leave type")
        if not reason or reason.strip() == "":
            missing_info.append("reason")
        
        if missing_info:
            # Get available leave types for the user to choose from
            leave_balance_result = get_leave_balance(employee_id, None, None, tool_context)
            available_types = []
            if leave_balance_result.get("status") == "success":
                leave_details = leave_balance_result.get("data", [])
                if isinstance(leave_details, list):
                    for item in leave_details:
                        if isinstance(item, dict):
                            leave_type_name = item.get("leaveTypeName", "Unknown")
                            description = item.get("description", "")
                            available_types.append(f"{leave_type_name} ({description})")
            
            clarification_message = f"I need some additional information to process your leave request. Please provide: {', '.join(missing_info)}."
            
            if "leave type" in missing_info:
                clarification_message += f"\n\nAvailable leave types:\n"
                for leave_type_option in available_types:
                    clarification_message += f"- {leave_type_option}\n"
                clarification_message += "\nPlease specify which type of leave you'd like to apply."
            
            if "reason" in missing_info:
                clarification_message += f"\n\nPlease provide a reason for your leave request."
            
            return {
                "status": "clarification_needed",
                "message": clarification_message,
                "missing_info": missing_info,
                "available_types": available_types,
                "parsed_dates": {
                    "start_date": start_date,
                    "end_date": end_date
                }
            }

        # First get leave balance to get leave type details
        print("=== CALLING GET_LEAVE_BALANCE ===")
        if employee_id:
            print(f"Parameters: employee_id={employee_id}, start_date={start_date}, end_date={end_date} (Admin mode)")
            logger.info(f"Parameters: employee_id={employee_id}, start_date={start_date}, end_date={end_date} (Admin mode)")
        else:
            print(f"Parameters: start_date={start_date}, end_date={end_date} (Self-service mode)")
            logger.info(f"Parameters: start_date={start_date}, end_date={end_date} (Self-service mode)")
        print(f"Tool context passed: {tool_context}")
        logger.info(f"Tool context passed: {tool_context}")

        leave_balance_result = get_leave_balance(employee_id, None, None, tool_context)
        print("=== GET_LEAVE_BALANCE RESULT ===")
        print(f"leave_balance_result: {leave_balance_result}")
        logger.info(f"=== GET_LEAVE_BALANCE RESULT ===")
        logger.info(f"leave_balance_result: {leave_balance_result}")

        # Get leave type detail ID from leave details
        if leave_balance_result.get("status") != "success":
            print(f"Leave balance call failed: {leave_balance_result}")
            logger.error(f"Leave balance call failed: {leave_balance_result}")
            return {
                "status": "error",
                "message": "Failed to get leave balance information",
                "details": leave_balance_result
            }

        leave_details = leave_balance_result.get("data", [])
        print("=== LEAVE DETAILS PROCESSING ===")
        print(f"leave_details: {leave_details}")
        logger.info(f"=== LEAVE DETAILS PROCESSING ===")

        logger.info(f"Looking for leave_type: '{leave_type}' (lowercase: '{leave_type.lower()}')")
        
        # Handle array format - search through the array for matching leave type
        leave_type_detail = None
        matched_leave_type = None
        
        if isinstance(leave_details, list):
            for item in leave_details:
                if isinstance(item, dict):
                    item_leave_type = item.get("leaveTypeName", "").lower()
                    item_description = item.get("description", "").lower()
                    
                    # Check if the requested leave type matches this item
                    if (leave_type.lower() == item_leave_type or 
                        leave_type.lower() in item_leave_type or 
                        item_leave_type in leave_type.lower() or
                        leave_type.lower() == item_description or
                        leave_type.lower() in item_description or
                        item_description in leave_type.lower()):
                        
                        print(f"Found matching leave type: '{item.get('leaveTypeName')}' for '{leave_type}'")
                        logger.info(f"Found matching leave type: '{item.get('leaveTypeName')}' for '{leave_type}'")
                        leave_type_detail = item
                        matched_leave_type = item.get("leaveTypeName")
                        break
        
        print(f"leave_type_detail for '{leave_type.lower()}': {leave_type_detail}")
        logger.info(f"leave_type_detail for '{leave_type.lower()}': {leave_type_detail}")

        if not leave_type_detail:
            print(f"Leave type '{leave_type}' not found in leave_details")
            print("Available leave types:")
            logger.warning(f"Leave type '{leave_type}' not found in leave_details")
            logger.info("Available leave types:")
            
            available_types = []
            if isinstance(leave_details, list):
                for item in leave_details:
                    if isinstance(item, dict):
                        leave_type_name = item.get("leaveTypeName", "Unknown")
                        description = item.get("description", "")
                        available_types.append(f"{leave_type_name} ({description})")
                        print(f"  - {leave_type_name} ({description})")
                        logger.info(f"  - {leave_type_name} ({description})")
            
            print(f"No matching leave type found for '{leave_type}'. Available types: {available_types}")
            logger.error(f"No matching leave type found for '{leave_type}'. Available types: {available_types}")
            return {
                "status": "error",
                "message": f"Leave type '{leave_type}' not found in available leave types.",
                "available_types": available_types
            }
        
        # Update leave_type to the matched name for consistency
        if matched_leave_type:
            leave_type = matched_leave_type

        leave_type_detail_id = leave_type_detail.get("leaveTypeDetailId")
        print(f"leave_type_detail_id: {leave_type_detail_id}")
        logger.info(f"leave_type_detail_id: {leave_type_detail_id}")

        available_leaves = leave_type_detail.get("availableLeaves")
        print(f"available_leaves: {available_leaves}")
        logger.info(f"available_leaves: {available_leaves}")

        if (available_leaves <= 0):
            print(f"Insufficient leave balance: {available_leaves} for leave type '{leave_type}'")
            logger.warning(f"Insufficient leave balance: {available_leaves} for leave type '{leave_type}'")
            return {
                "status": "error",
                "message": f"You can not request leave because you have no {leave_type.lower()} leave balance",
                "available_leaves": available_leaves
            }

        if not leave_type_detail_id:
            print(f"Missing leave_type_detail_id for leave type '{leave_type}'")
            logger.error(f"Missing leave_type_detail_id for leave type '{leave_type}'")
            return {
                "status": "error",
                "message": f"Could not find leave type detail ID for '{leave_type}'"
            }

        print("=== PREPARING API CALL ===")
        print(f"Leave type validation passed - ID: {leave_type_detail_id}, Available: {available_leaves}")
        logger.info(f"=== PREPARING API CALL ===")
        logger.info(f"Leave type validation passed - ID: {leave_type_detail_id}, Available: {available_leaves}")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-leaves/request"
        url = f"{base_url}{endpoint}"
        logger.info(f"API URL: {url}")

        # Calculate leave count first (before auth check for debugging)
        from datetime import datetime
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        leave_count = (end - start).days + 1
        logger.info(f"Calculated leave count: {leave_count} days from {start_date} to {end_date}")

        # Get current timestamp in the required format
        current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S+05:30")

        # Determine day preference based on half_day flag
        if half_day:
            # For half-day, we need to determine if it's first half or second half
            # Since we don't have specific half-day type in the current parameters,
            # we'll default to "First Half" for now
            # TODO: Add a parameter to specify first/second half explicitly
            day_preference = "First Half"
        else:
            day_preference = "Full Day"

        # Prepare request payload (for debugging - show what would be sent)
        payload = {
            "leaveTypeDetailId": leave_type_detail_id,
            "startDate": start_date,
            "endDate": end_date,
            "note": reason,
            "actionTakenOn": current_time,
            "firstDayPreference": day_preference,
            "lastDayPreference": day_preference,
            "notifyTo": [""],
            "leaveCount": leave_count,
            "employeeNo": None
        }

        # Add employeeId for admin scenarios (when admin applies leave on behalf of another employee)
        if employee_id:
            payload["employeeId"] = employee_id
            print(f"✅ Added employeeId to payload for admin mode: {employee_id}")
            logger.info(f"✅ Added employeeId to payload for admin mode: {employee_id}")
        else:
            print("ℹ️  No employeeId in payload - using self-service mode (API will identify user from auth token)")
            logger.info("ℹ️  No employeeId in payload - using self-service mode (API will identify user from auth token)")

        print(f"🔍 DEBUG - Final payload that would be sent: {payload}")
        logger.info(f"🔍 DEBUG - Final payload that would be sent: {payload}")

        # Get authentication headers from hr_auth manager
        logger.info("Getting authentication headers")
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        logger.info(f"Auth headers received: {bool(auth_headers)}")
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token",
                "debug_info": {
                    "employee_id": employee_id,
                    "payload_would_include_employee_id": bool(employee_id),
                    "payload": payload
                }
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }
        logger.info(f"Request headers: {headers}")



        logger.info(f"=== MAKING API CALL ===")
        logger.info(f"URL: {url}")
        logger.info(f"Payload: {payload}")
        logger.info(f"Headers: {headers}")

        # Make the API call
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            logger.info(f"API Response status: {response.status_code}")
            logger.info(f"API Response headers: {dict(response.headers)}")
            logger.info(f"API Response text: {response.text}")

            if response.status_code == 200:
                data = response.json()
                logger.info(f"Leave request created successfully: {data}")
                return {
                    "status": "success",
                    "message": "Leave request created successfully",
                    "leave_request": data,
                    "interpretation": parsed_info.get("interpretation", "Leave request processed")
                }
            else:
                logger.error(f"API call failed with status {response.status_code}: {response.text}")
                return {
                    "status": "error",
                    "message": f"API call failed with status {response.status_code}: {response.text}"
                }
        except Exception as api_error:
            logger.error(f"Exception during API call: {api_error}")
            return {
                "status": "error",
                "message": f"Exception during API call: {str(api_error)}"
            }

    except Exception as e:
        print("=== EXCEPTION IN REQUEST_LEAVE_INTELLIGENT ===")
        print(f"Exception type: {type(e)}")
        print(f"Exception message: {str(e)}")
        import traceback
        print(f"Exception traceback: {traceback.format_exc()}")
        logger.error(f"=== EXCEPTION IN REQUEST_LEAVE_INTELLIGENT ===")
        logger.error(f"Exception type: {type(e)}")
        logger.error(f"Exception message: {str(e)}")
        logger.error(f"Exception traceback: {traceback.format_exc()}")
        return {
            "status": "error",
            "message": f"Error creating leave request: {str(e)}"
        }

def _llm_parse_leave_request(text: str, tool_context: Optional[object] = None) -> Dict[str, Any]:
    """
    Use LLM to intelligently parse natural language leave request text.

    This function leverages AI to understand natural language and extract:
    - Dates (relative, absolute, natural language)
    - Leave types (sick, annual, personal, etc.)
    - Reasons and context
    - Duration and timing preferences

    Args:
        text: Natural language leave request text
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing parsed leave request information
    """
    try:
        logger.info(f"Using LLM to parse leave request: '{text}'")

        # Try to use Google ADK for intelligent parsing if available
        try:
            # Import Google ADK LLM components
            from google.adk.models.google_llm import Gemini
            from datetime import datetime, timedelta
            import os

            logger.info("Google ADK successfully imported for LLM parsing")

            # Get current date for context
            current_date = datetime.now().strftime("%Y-%m-%d")
            current_day = datetime.now().strftime("%A")
            tomorrow_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")

            # Get available leave types from context or fallback
            leave_details = None
            if tool_context and hasattr(tool_context, 'state') and 'leave_details' in tool_context.state:
                leave_details = tool_context.state['leave_details']

            # Create the LLM prompt for generic, robust leave type resolution
            parsing_prompt = f"""
You are an intelligent HR assistant that parses natural language leave requests into structured data.

Current date context:
- Today is {current_date} ({current_day})
- Tomorrow is {tomorrow_date}

Common leave types:
- "casual or sick leave" for sick days, medical appointments, feeling unwell
- "privilege or earned leave" for vacation, holidays, annual leave, personal time off, trips, travel
- "maternity leave" for pregnancy-related leave
- "paternity leave" for new fathers
- "bereavement leave" for death in family
- "comp-off" for compensatory time off

When a user requests a leave type:
- If their input matches, is a synonym, or is a partial/abbreviated form of a valid type, resolve it to the valid type.
- Handle common misspellings like "vacaation" → "privilege or earned leave" (vacation)
- If their input is ambiguous or could refer to more than one type, set needs_clarification to true.
- If their input is a typo, suggest the closest valid type.
- If no leave type is mentioned or implied, set leave_type to null and needs_clarification to true.
- Never return a combined or invalid type.
- Never assume a default leave type unless explicitly mentioned by the user.
- Pay special attention to vacation-related keywords: vacation, holiday, trip, travel, going away

Parse the following leave request and extract the information in JSON format:

Leave Request: \"{text}\"

Extract the following information:
1. success: Always set to true if parsing is successful
2. start_date: The start date in YYYY-MM-DD format (use context dates for relative terms like "tomorrow")
3. end_date: The end date in YYYY-MM-DD format (same as start_date if single day)
4. leave_type: The resolved valid leave type (from the list above)
5. needs_clarification: true/false (true if the leave type is ambiguous or not found)
6. clarification_prompt: If needs_clarification is true, a user-friendly prompt asking the user to clarify
7. reason: The reason for the leave
8. half_day: Boolean indicating if it's a half-day leave
9. interpretation: A human-readable interpretation of what was understood

Return ONLY a valid JSON object with the extracted information. If any information cannot be determined, use null for that field.
"""

            # Check for Google API key
            api_key = os.environ.get("GOOGLE_API_KEY")
            if not api_key:
                raise ImportError("GOOGLE_API_KEY not found in environment")

            # Initialize the Gemini model with proper configuration
            gemini_model = Gemini(
                model_name="gemini-2.5-flash",
                api_key=api_key
            )

            # Get the LLM response (using async method)
            import asyncio
            response = asyncio.run(gemini_model.generate_content_async(parsing_prompt))

            # Parse the JSON response
            import json
            import re

            # Extract JSON from the response
            response_text = response.text if hasattr(response, 'text') else str(response)

            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                parsed_result = json.loads(json_str)

                # Validate the parsed result - check if we have essential fields
                if parsed_result.get("start_date"):
                    # Add success flag if not present
                    parsed_result["success"] = True
                    logger.info(f"LLM successfully parsed leave request: {parsed_result}")
                    return parsed_result
                else:
                    logger.warning(f"LLM parsing incomplete: {parsed_result}")
                    return {
                        "success": False,
                        "error": "LLM could not fully parse the leave request",
                        "llm_response": parsed_result
                    }
            else:
                logger.error(f"No valid JSON found in LLM response: {response_text}")
                return {
                    "success": False,
                    "error": "LLM response did not contain valid JSON",
                    "llm_response": response_text
                }

        except ImportError as e:
            logger.info(f"Google ADK not available ({e}), falling back to enhanced parsing")
        except Exception as e:
            logger.warning(f"Google ADK LLM parsing failed: {e}, falling back to enhanced parsing")

        # Fallback to enhanced parsing if LLM is not available
        logger.info("Using enhanced parsing as fallback")
        return _enhanced_parse_leave_request(text)

    except Exception as e:
        logger.error(f"Error in LLM parsing: {e}")

        # Fallback to basic parsing if everything fails
        logger.info("Falling back to basic parsing due to error")
        return _basic_parse_leave_request(text)

def _simple_fallback_parsing(text: str) -> Dict[str, Any]:
    """
    Simple fallback parsing for common cases when advanced parsing fails.

    This function handles the most basic cases like "tomorrow", "today", etc.
    when the more sophisticated parsing methods fail.

    Args:
        text: Natural language leave request text

    Returns:
        Dictionary containing parsed leave request information
    """
    try:
        from datetime import datetime, timedelta
        import re

        logger.info(f"Simple fallback parsing for: '{text}'")

        # Normalize text
        normalized_text = text.lower().strip()

        # Simple date detection
        start_date = None

        # Check for "tomorrow"
        if "tomorrow" in normalized_text:
            tomorrow = (datetime.now().date() + timedelta(days=1)).strftime("%Y-%m-%d")
            start_date = tomorrow
            logger.info(f"Fallback detected 'tomorrow' as: {start_date}")
            print(f"🔄 Simple fallback: Found 'tomorrow' in '{normalized_text}', calculated as: {start_date}")

        # Check for "today"
        elif "today" in normalized_text:
            today = datetime.now().date().strftime("%Y-%m-%d")
            start_date = today
            logger.info(f"Fallback detected 'today' as: {start_date}")

        # Check for specific date patterns
        elif re.search(r'\b(\d{4})-(\d{1,2})-(\d{1,2})\b', normalized_text):
            match = re.search(r'\b(\d{4})-(\d{1,2})-(\d{1,2})\b', normalized_text)
            start_date = f"{match.group(1)}-{match.group(2).zfill(2)}-{match.group(3).zfill(2)}"
            logger.info(f"Fallback detected ISO date: {start_date}")

        if not start_date:
            return {
                "success": False,
                "error": "Simple fallback could not detect any date in the request"
            }

        # Simple leave type detection - only if explicitly mentioned
        leave_type = None  # Don't default to any type
        if any(word in normalized_text for word in ["sick", "ill", "unwell", "not feeling well"]):
            leave_type = "casual or sick leave"
        elif any(word in normalized_text for word in ["vacation", "holiday", "annual", "personal", "vacaation", "vaction", "trip", "travel", "going away"]):
            leave_type = "privilege or earned leave"

        # Simple reason extraction
        reason = "Leave request"
        if "not feeling well" in normalized_text:
            reason = "not feeling well"
        elif "sick" in normalized_text:
            reason = "sick leave"
        elif "personal" in normalized_text:
            reason = "personal reasons"

        # Check if we have all required information
        if not leave_type:
            return {
                "success": False,
                "error": "Leave type not specified in the request",
                "suggestion": "Please specify the type of leave you want to apply (e.g., sick leave, vacation, casual leave)",
                "parsed_dates": {
                    "start_date": start_date,
                    "end_date": start_date
                }
            }
        
        return {
            "success": True,
            "start_date": start_date,
            "end_date": start_date,  # Single day
            "leave_type": leave_type,
            "reason": reason,
            "half_day": False,
            "interpretation": f"Simple fallback parsing: {leave_type} on {start_date} - {reason}",
            "parsing_method": "simple_fallback"
        }

    except Exception as e:
        logger.error(f"Error in simple fallback parsing: {e}")
        return {
            "success": False,
            "error": f"Error in simple fallback parsing: {str(e)}"
        }

def _enhanced_parse_leave_request(text: str) -> Dict[str, Any]:
    """
    Enhanced parsing with better natural language understanding.

    This function uses improved pattern matching and context analysis
    to parse leave requests more intelligently than basic parsing.

    Args:
        text: Natural language leave request text

    Returns:
        Dictionary containing parsed leave request information
    """
    try:
        from datetime import datetime, timedelta
        import re

        logger.info(f"Using enhanced parsing for: '{text}'")

        # Normalize text
        normalized_text = text.lower().strip()

        # Enhanced date parsing with better context understanding
        today = datetime.now().date()
        start_date = None

        # More sophisticated date pattern matching
        date_patterns = [
            # Specific calendar dates - various formats
            (r'\b(\d{4})-(\d{1,2})-(\d{1,2})\b', lambda m: _parse_iso_date(m.group(1), m.group(2), m.group(3))),
            (r'\b(\d{1,2})/(\d{1,2})/(\d{4})\b', lambda m: _parse_us_date(m.group(1), m.group(2), m.group(3))),
            (r'\b(\d{1,2})-(\d{1,2})-(\d{4})\b', lambda m: _parse_us_date(m.group(1), m.group(2), m.group(3))),
            (r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2})(?:st|nd|rd|th)?,?\s+(\d{4})\b',
             lambda m: _parse_month_day_year(m.group(1), m.group(2), m.group(3))),
            (r'\b(\d{1,2})(?:st|nd|rd|th)?\s+(january|february|march|april|may|june|july|august|september|october|november|december),?\s+(\d{4})\b',
             lambda m: _parse_day_month_year(m.group(1), m.group(2), m.group(3))),
            (r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2})(?:st|nd|rd|th)?\b',
             lambda m: _parse_month_day_current_year(m.group(1), m.group(2))),
            (r'\b(\d{1,2})(?:st|nd|rd|th)?\s+(january|february|march|april|may|june|july|august|september|october|november|december)\b',
             lambda m: _parse_day_month_current_year(m.group(1), m.group(2))),
            # Relative dates with context
            (r'\b(tomorrow)\b', lambda m: today + timedelta(days=1)),
            (r'\b(today)\b', lambda m: today),
            (r'\b(next|coming)\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
             lambda m: _calculate_next_weekday(today, m.group(2))),
            (r'\b(this)\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
             lambda m: _calculate_this_weekday(today, m.group(2))),
            (r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
             lambda m: _calculate_next_weekday(today, m.group(1))),
        ]

        # First, try to find date ranges (from X to Y)
        start_date = None
        end_date = None

        # Look for date range patterns first - more specific patterns
        range_patterns = [
            # From X to Y patterns
            r'from\s+((?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4})\s+to\s+((?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4})',
            r'from\s+(\d{4}-\d{1,2}-\d{1,2})\s+to\s+(\d{4}-\d{1,2}-\d{1,2})',
            r'from\s+(\d{1,2}/\d{1,2}/\d{4})\s+to\s+(\d{1,2}/\d{1,2}/\d{4})',
            # Between X and Y patterns
            r'between\s+((?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4})\s+and\s+((?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4})',
            r'between\s+(\d{4}-\d{1,2}-\d{1,2})\s+and\s+(\d{4}-\d{1,2}-\d{1,2})',
            r'between\s+(\d{1,2}/\d{1,2}/\d{4})\s+and\s+(\d{1,2}/\d{1,2}/\d{4})',
            # X to Y patterns (more specific)
            r'((?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4})\s+to\s+((?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4})',
            r'(\d{4}-\d{1,2}-\d{1,2})\s+to\s+(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{1,2}/\d{1,2}/\d{4})\s+to\s+(\d{1,2}/\d{1,2}/\d{4})',
        ]

        for range_pattern in range_patterns:
            range_match = re.search(range_pattern, normalized_text, re.IGNORECASE)
            if range_match:
                start_text = range_match.group(1).strip()
                end_text = range_match.group(2).strip()
                logger.info(f"Found date range: '{start_text}' to '{end_text}'")

                # Try to parse both dates
                start_parsed = None
                end_parsed = None

                for pattern, date_func in date_patterns:
                    if not start_parsed:
                        start_match = re.search(pattern, start_text)
                        if start_match:
                            try:
                                start_parsed = date_func(start_match)
                                logger.info(f"Parsed start date: {start_parsed}")
                            except Exception as e:
                                logger.warning(f"Error parsing start date with pattern {pattern}: {e}")

                    if not end_parsed:
                        end_match = re.search(pattern, end_text)
                        if end_match:
                            try:
                                end_parsed = date_func(end_match)
                                logger.info(f"Parsed end date: {end_parsed}")
                            except Exception as e:
                                logger.warning(f"Error parsing end date with pattern {pattern}: {e}")

                if start_parsed and end_parsed:
                    start_date = start_parsed.strftime("%Y-%m-%d")
                    end_date = end_parsed.strftime("%Y-%m-%d")
                    logger.info(f"Enhanced parsing found date range: {start_date} to {end_date}")
                    break

        # If no range found, try single date patterns
        if not start_date:
            logger.info(f"Trying single date patterns for: '{normalized_text}'")
            for i, (pattern, date_func) in enumerate(date_patterns):
                logger.info(f"Testing pattern {i+1}: {pattern}")
                match = re.search(pattern, normalized_text)
                if match:
                    logger.info(f"Pattern {i+1} matched: {match.groups()}")
                    try:
                        calculated_date = date_func(match)
                        logger.info(f"Date function returned: {calculated_date} (type: {type(calculated_date)})")
                        start_date = calculated_date.strftime("%Y-%m-%d")
                        end_date = start_date  # Single day leave
                        logger.info(f"Enhanced parsing found single date: {start_date} from pattern: {pattern}")
                        break
                    except Exception as e:
                        logger.warning(f"Error calculating date for pattern {pattern}: {e}")
                        import traceback
                        logger.warning(f"Traceback: {traceback.format_exc()}")
                        continue
                else:
                    logger.info(f"Pattern {i+1} did not match")

        if not start_date:
            logger.error(f"Enhanced parsing failed to find date in: '{text}'")
            logger.error(f"Normalized text was: '{normalized_text}'")
            return {
                "success": False,
                "error": f"Could not determine the date from the request: '{text}'. Please use formats like 'tomorrow', 'next Tuesday', '2024-07-27', or 'July 27th'."
            }

        # Enhanced leave type detection with context
        leave_type = _detect_leave_type_enhanced(normalized_text)
        
        # Enhanced reason extraction (only if we have a leave type)
        reason = _extract_reason_enhanced(text, leave_type) if leave_type else None

        # Check for half-day indicators
        half_day = any(indicator in normalized_text for indicator in
                      ["half day", "half-day", "morning only", "afternoon only", "partial day"])

        # Check if we have all required information
        if not leave_type:
            return {
                "success": False,
                "error": "Leave type not specified in the request",
                "suggestion": "Please specify the type of leave you want to apply (e.g., sick leave, vacation, casual leave)",
                "parsed_dates": {
                    "start_date": start_date,
                    "end_date": end_date
                }
            }
        
        # Create interpretation message
        if start_date == end_date:
            date_description = f"on {start_date}"
        else:
            date_description = f"from {start_date} to {end_date}"

        return {
            "success": True,
            "start_date": start_date,
            "end_date": end_date,
            "leave_type": leave_type,
            "reason": reason,
            "half_day": half_day,
            "interpretation": f"Requesting {leave_type} leave {date_description} - {reason}",
            "parsing_method": "enhanced_fallback"
        }

    except Exception as e:
        logger.error(f"Error in enhanced parsing: {e}")
        return {
            "success": False,
            "error": f"Error in enhanced parsing: {str(e)}"
        }

def _calculate_next_weekday(today, weekday_name):
    """Calculate the next occurrence of a weekday."""
    weekdays = {
        'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
        'friday': 4, 'saturday': 5, 'sunday': 6
    }

    target_weekday = weekdays.get(weekday_name.lower())
    if target_weekday is None:
        raise ValueError(f"Invalid weekday: {weekday_name}")

    current_weekday = today.weekday()
    days_ahead = target_weekday - current_weekday

    if days_ahead <= 0:  # Target day already passed this week
        days_ahead += 7

    return today + timedelta(days=days_ahead)

def _calculate_this_weekday(today, weekday_name):
    """Calculate this week's occurrence of a weekday."""
    weekdays = {
        'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
        'friday': 4, 'saturday': 5, 'sunday': 6
    }

    target_weekday = weekdays.get(weekday_name.lower())
    if target_weekday is None:
        raise ValueError(f"Invalid weekday: {weekday_name}")

    current_weekday = today.weekday()
    days_ahead = target_weekday - current_weekday

    if days_ahead < 0:  # Day already passed this week, assume next week
        days_ahead += 7

    return today + timedelta(days=ahead)

def _parse_iso_date(year, month, day):
    """Parse ISO format date (YYYY-MM-DD)."""
    from datetime import datetime
    return datetime(int(year), int(month), int(day)).date()

def _parse_us_date(month, day, year):
    """Parse US format date (MM/DD/YYYY or MM-DD-YYYY)."""
    from datetime import datetime
    return datetime(int(year), int(month), int(day)).date()

def _parse_month_day_year(month_name, day, year):
    """Parse format like 'June 23rd, 2025' or 'June 23, 2025'."""
    from datetime import datetime
    month_map = {
        'january': 1, 'february': 2, 'march': 3, 'april': 4,
        'may': 5, 'june': 6, 'july': 7, 'august': 8,
        'september': 9, 'october': 10, 'november': 11, 'december': 12
    }
    month_num = month_map.get(month_name.lower())
    if month_num is None:
        raise ValueError(f"Invalid month: {month_name}")
    return datetime(int(year), month_num, int(day)).date()

def _parse_day_month_year(day, month_name, year):
    """Parse format like '23rd June, 2025' or '23 June, 2025'."""
    from datetime import datetime
    month_map = {
        'january': 1, 'february': 2, 'march': 3, 'april': 4,
        'may': 5, 'june': 6, 'july': 7, 'august': 8,
        'september': 9, 'october': 10, 'november': 11, 'december': 12
    }
    month_num = month_map.get(month_name.lower())
    if month_num is None:
        raise ValueError(f"Invalid month: {month_name}")
    return datetime(int(year), month_num, int(day)).date()

def _parse_month_day_current_year(month_name, day):
    """Parse format like 'June 23rd' (assumes current year)."""
    from datetime import datetime
    current_year = datetime.now().year
    month_map = {
        'january': 1, 'february': 2, 'march': 3, 'april': 4,
        'may': 5, 'june': 6, 'july': 7, 'august': 8,
        'september': 9, 'october': 10, 'november': 11, 'december': 12
    }
    month_num = month_map.get(month_name.lower())
    if month_num is None:
        raise ValueError(f"Invalid month: {month_name}")
    return datetime(current_year, month_num, int(day)).date()

def _parse_day_month_current_year(day, month_name):
    """Parse format like '23rd June' (assumes current year)."""
    from datetime import datetime
    current_year = datetime.now().year
    month_map = {
        'january': 1, 'february': 2, 'march': 3, 'april': 4,
        'may': 5, 'june': 6, 'july': 7, 'august': 8,
        'september': 9, 'october': 10, 'november': 11, 'december': 12
    }
    month_num = month_map.get(month_name.lower())
    if month_num is None:
        raise ValueError(f"Invalid month: {month_name}")
    return datetime(current_year, month_num, int(day)).date()

def _detect_leave_type_enhanced(text):
    """Enhanced leave type detection with better context understanding."""
    import re
    
    # Normalize text for better matching
    normalized_text = text.lower()
    
    # More sophisticated leave type patterns with scoring and fuzzy matching
    leave_patterns = {
        "privilege or earned leave": {
            "keywords": ["vacation", "holiday", "annual", "privilege", "earned", "personal time off", "pto", "vacaation", "vaction", "holiday", "trip", "travel"],
            "score": 0
        },
        "casual or sick leave": {
            "keywords": ["sick", "illness", "unwell", "not feeling well", "not well", "medical", "doctor", "hospital", "health", "casual", "ill", "unhealthy"],
            "score": 0
        },
        "maternity leave": {
            "keywords": ["maternity", "pregnancy", "delivery", "baby", "childbirth"],
            "score": 0
        },
        "paternity leave": {
            "keywords": ["paternity", "father", "new born", "newborn", "dad"],
            "score": 0
        },
        "bereavement leave": {
            "keywords": ["bereavement", "death", "funeral", "mourning", "loss"],
            "score": 0
        },
        "comp-off": {
            "keywords": ["comp", "compensatory", "comp-off", "compensatory"],
            "score": 0
        }
    }

    # Score each leave type based on keyword matches (including partial matches)
    for leave_type, config in leave_patterns.items():
        for keyword in config["keywords"]:
            # Check for exact word match
            if re.search(r'\b' + re.escape(keyword) + r'\b', normalized_text):
                config["score"] += 2
            # Check for partial match (for misspellings)
            elif keyword in normalized_text:
                config["score"] += 1
            # Check for similar words (fuzzy matching for common misspellings)
            elif any(similar in normalized_text for similar in _get_similar_words(keyword)):
                config["score"] += 1

    # Return the highest scoring leave type, but only if there's a clear match
    best_type = max(leave_patterns.items(), key=lambda x: x[1]["score"])
    if best_type[1]["score"] > 0:
        return best_type[0]
    else:
        return None  # Don't default to any type if no clear match

def _get_similar_words(word):
    """Get similar words for fuzzy matching of common misspellings."""
    similar_words = {
        "vacation": ["vacaation", "vaction", "vacation", "vacashun"],
        "holiday": ["holiday", "hollyday", "hollyday"],
        "sick": ["sik", "sick", "ill"],
        "annual": ["anual", "annual", "yearly"],
        "casual": ["casual", "casul", "casual"],
        "maternity": ["maternity", "maternaty", "maternaty"],
        "paternity": ["paternity", "paternaty", "paternaty"],
        "bereavement": ["bereavement", "bereavment", "bereavment"],
        "compensatory": ["compensatory", "compensatry", "compensatry"]
    }
    return similar_words.get(word, [word])

def _extract_reason_enhanced(text, leave_type):
    """Enhanced reason extraction with better context understanding."""
    import re

    # Try to extract reason from common patterns
    reason_patterns = [
        r"because\s+(.+?)(?:\.|,|$)",
        r"as\s+(.+?)(?:\.|,|$)",
        r"since\s+(.+?)(?:\.|,|$)",
        r"for\s+(.+?)(?:\.|,|$)",
        r"due to\s+(.+?)(?:\.|,|$)"
    ]

    for pattern in reason_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            reason = match.group(1).strip()
            # Clean up the reason
            reason = re.sub(r'\s+', ' ', reason)
            if len(reason) > 5:  # Ensure it's a meaningful reason
                return reason

    # Generate contextual reason based on leave type and text content
    if leave_type == "sick":
        if any(word in text.lower() for word in ["doctor", "appointment", "medical"]):
            return "Medical appointment"
        elif any(word in text.lower() for word in ["unwell", "not feeling well", "sick"]):
            return "Not feeling well"
        else:
            return "Health reasons"
    elif leave_type == "personal":
        if "family" in text.lower():
            return "Family matters"
        elif "emergency" in text.lower():
            return "Personal emergency"
        else:
            return "Personal reasons"
    elif leave_type == "annual":
        return "Annual leave"
    else:
        return f"{leave_type.title()} leave request"

def _basic_parse_leave_request(text: str) -> Dict[str, Any]:
    """
    Basic fallback parsing when LLM is not available.

    Args:
        text: Natural language leave request text

    Returns:
        Dictionary containing parsed leave request information
    """
    try:
        from datetime import datetime, timedelta
        import re

        logger.info(f"Using basic parsing for: '{text}'")

        # Normalize text
        normalized_text = text.lower().strip()

        # Basic date parsing
        today = datetime.now().date()
        start_date = None

        # Look for common date patterns
        if "tomorrow" in normalized_text:
            start_date = (today + timedelta(days=1)).strftime("%Y-%m-%d")
        elif "today" in normalized_text:
            start_date = today.strftime("%Y-%m-%d")
        elif "next tuesday" in normalized_text or "coming tuesday" in normalized_text:
            # Find next Tuesday
            days_ahead = 1 - today.weekday()  # Tuesday is 1
            if days_ahead <= 0:
                days_ahead += 7
            start_date = (today + timedelta(days=days_ahead)).strftime("%Y-%m-%d")
        elif "next friday" in normalized_text or "coming friday" in normalized_text:
            # Find next Friday
            days_ahead = 4 - today.weekday()  # Friday is 4
            if days_ahead <= 0:
                days_ahead += 7
            start_date = (today + timedelta(days=days_ahead)).strftime("%Y-%m-%d")

        if not start_date:
            return {
                "success": False,
                "error": "Could not determine the date from the request"
            }

        # Basic leave type detection
        leave_type = None
        if any(word in normalized_text for word in ["sick", "ill", "unwell", "not feeling well"]):
            leave_type = "sick"
        elif any(word in normalized_text for word in ["annual", "vacation", "holiday", "vacaation", "vaction", "trip", "travel", "going away"]):
            leave_type = "annual"
        elif any(word in normalized_text for word in ["personal", "family"]):
            leave_type = "personal"
        elif any(word in normalized_text for word in ["casual", "day off"]):
            leave_type = "casual"

        if not leave_type:
            leave_type = "personal"  # Default fallback

        # Basic reason extraction
        reason = "Leave request"
        if "because" in normalized_text:
            reason_match = re.search(r"because\s+(.+?)(?:\.|$)", normalized_text)
            if reason_match:
                reason = reason_match.group(1).strip()
        elif "as" in normalized_text:
            reason_match = re.search(r"as\s+(.+?)(?:\.|$)", normalized_text)
            if reason_match:
                reason = reason_match.group(1).strip()

        return {
            "success": True,
            "start_date": start_date,
            "end_date": start_date,
            "leave_type": leave_type,
            "reason": reason,
            "half_day": "half" in normalized_text,
            "interpretation": f"Requesting {leave_type} leave on {start_date} - {reason}",
            "parsing_method": "basic_fallback"
        }

    except Exception as e:
        logger.error(f"Error in basic parsing: {e}")
        return {
            "success": False,
            "error": f"Error in basic parsing: {str(e)}"
        }


def get_holidays(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves the holidays for the authenticated employee using WagonHR API.
    """
    try:
        # Prepare the API call
        logger.info("=== GET HOLIDAYS ===")
        logger.info("Calling WagonHR get holidays endpoint directly")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth
        
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/hris/employee/holidays"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        params = {}
        if start_date:
            params["startDate"] = start_date
        if end_date:
            params["endDate"] = end_date

        response = requests.get(url, headers=headers, params=params, timeout=30)
        return response.json()
    except Exception as e:
        logger.error(f"Error in get_holidays: {e}")
        return {
            "success": False,
            "error": f"Error in get_holidays: {str(e)}"
        }  

def get_comp_off_leaves(
    employee_id: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves the comp off leaves for the authenticated employee using WagonHR API.
    """
    try:
        # Prepare the API call
        logger.info("=== GET COMP OFF LEAVES ===")
        logger.info("Calling WagonHR get comp off leaves endpoint directly")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth
        
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-comp-off"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }
            
        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        params = {}
        if employee_id:
            params["empNumber"] = employee_id

        response = requests.get(url, headers=headers, params=params, timeout=30)
        return response.json()
    except Exception as e:
        logger.error(f"Error in get_comp_off_leaves: {e}")
        return {
            "success": False,
            "error": f"Error in get_comp_off_leaves: {str(e)}"
        }  

def get_leave_details(
    leave_id: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves the leave details for the authenticated employee using WagonHR API.
    """
    try:
        # Prepare the API call
        logger.info("=== GET LEAVE DETAILS ===")
        logger.info("Calling WagonHR get leave details endpoint directly")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth
        
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-leave-application/detailed-leave"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        params = {}
        if leave_id:
            params["leaveApplicationId"] = leave_id

        response = requests.get(url, headers=headers, params=params, timeout=30)
        return response.json()
    except Exception as e:
        logger.error(f"Error in get_leave_details: {e}")
        return {
            "success": False,
            "error": f"Error in get_leave_details: {str(e)}"
        }

def request_comp_off(
    employee_id: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Requests a comp off for the authenticated employee using WagonHR API.
    """

    try:
        # Prepare the API call
        logger.info("=== REQUEST COMP OFF ===")
        logger.info("Calling WagonHR request comp off endpoint directly")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-comp-off/request"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context

        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        payload = {
            "employeeNo": employee_id,
            "startDate": start_date,
            "endDate": end_date,
            "leaveNote": leaveNote,
            "requestedLeaves": leaveCount
        }

        response = requests.post(url, headers=headers, json=payload, timeout=30)
        return response.json()
    except Exception as e:
        logger.error(f"Error in request_comp_off: {e}")
        return {
            "success": False,
            "error": f"Error in request_comp_off: {str(e)}"
        }

def get_my_team_leaves_overview(
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    try:
        logger.info("=== GET MY TEAM LEAVES OVERVIEW ===")
        
        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        print(f"tool_context: {tool_context}")
        
        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/managers-view/leave-overview"

        url = f"{base_url}{endpoint}"

        print(f"url: {url}")
        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        print(f"auth_headers: {auth_headers}")

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        logger.info(f"Making GET request to: {url}")

        # Make the API call
        response = requests.get(url, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info(f" employee info data: {data}")
            logger.info("Employee info retrieved successfully")
            return {
                "status": "success",
                "data": data
            }
        else:
            logger.error(f"Failed to get employee info: {response.status_code}")
            return {
                "status": "error",
                "message": f"Failed to get employee info: {response.status_code}"
            }
    except Exception as e:
        logger.error(f"Error getting employee info: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting employee info: {str(e)}"
        }


def get_my_team_pending_approvals(
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    try:
        logger.info("=== GET MY TEAM PENDING APPROVALS ===")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth
        
        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/managers-view/pending-approvals"

        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        logger.info(f"Making GET request to: {url}")

        # Make the API call
        response = requests.get(url, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info(f" employee info data: {data}")
            logger.info("Employee info retrieved successfully")
            return {
                "status": "success",
                "data": data
            }
        else:
            logger.error(f"Failed to get employee info: {response.status_code}")
            return {
                "status": "error",
                "message": f"Failed to get employee info: {response.status_code}"
            }
    except Exception as e:
        logger.error(f"Error getting employee info: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting employee info: {str(e)}"
        }


def get_my_team_comp_off_pending_approvals(
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    try:
        logger.info("=== GET MY TEAM COMP OFF PENDING APPROVALS ===")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/managers-view/comp-off/pending-approvals"

        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        logger.info(f"Making GET request to: {url}")

        # Make the API call
        response = requests.get(url, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info(f" employee info data: {data}")
            logger.info("Employee info retrieved successfully")
            return {
                "status": "success",
                "data": data
            }
        else:
            logger.error(f"Failed to get employee info: {response.status_code}")
            return {
                "status": "error",
                "message": f"Failed to get employee info: {response.status_code}"
            }

    except Exception as e:
        logger.error(f"Error getting employee info: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting employee info: {str(e)}"
        }

def get_my_teams_leave_history(
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    try:
        logger.info("=== GET MY TEAMS LEAVE HISTORY ===")
        
        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/managers-view/past-leaves"

        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        logger.info(f"Making GET request to: {url}")

        # Make the API call
        response = requests.get(url, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info(f" employee info data: {data}")
            logger.info("Employee info retrieved successfully")
            return {
                "status": "success",
                "data": data
            }

        else:
            logger.error(f"Failed to get employee info: {response.status_code}")
            return {
                "status": "error",
                "message": f"Failed to get employee info: {response.status_code}"
            }

    except Exception as e:
        logger.error(f"Error getting employee info: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting employee info: {str(e)}"
        }

# HR View Tools

def update_employee_leave_details(
    employee_id: str,
    leave_overview: List[Dict[str, Any]],
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Updates employee leave details using WagonHR API.

    Args:
        employee_id: ID of the employee whose leave details need to be updated
        leave_overview: List of leave objects containing leave details
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated leave details
    """
    try:
        logger.info("=== UPDATE EMPLOYEE LEAVE DETAILS ===")
        
        # Import required modules
        import requests
        from utils.hr_auth import hr_auth
        
        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/hr-view"

        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        # Prepare request payload
        payload = {
            "leaveOverview": leave_overview
        }

        # Add employee_id to params if provided
        params = {}
        if employee_id:
            params["employee_id"] = employee_id

        logger.info(f"Making PUT request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.put(url, json=payload, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("Employee leave details updated successfully")
            return {
                "status": "success",
                "message": "Employee leave details updated successfully",
                "data": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error updating employee leave details: {str(e)}")
        return {
            "status": "error",
            "message": f"Error updating employee leave details: {str(e)}"
        }

def get_all_pending_leaves_approvals(
    page: int = 1,
    page_size: int = 10,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Get all pending leave approvals with pagination support.

    Args:
        page: Page number (default: 1)
        page_size: Number of records per page (default: 10)
        tool_context: Tool context for accessing session state

    Returns:
        Dict containing status, data, and pagination info
    """
    try:
        logger.info("=== GET ALL PENDING LEAVES APPROVALS ===")
        logger.info(f"Page: {page}, Page Size: {page_size}")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth
        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/hr-view/all-pending-approvals"

        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")

            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        logger.info(f"Making GET request to: {url}")
        logger.info(f"Headers: {headers}")

        # Add pagination parameters
        params = {
            "pageNo": page,
            "pageSize": page_size,
            "sortOrder": "asc",
            "sortBy": "employeeName"
        }

        logger.info(f"Making GET request to: {url}")
        logger.info(f"Pagination params: {params}")

        # Make the API call
        #response = requests.get(url, headers=headers, params=params, timeout=30)

        response = requests.get(url, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info(f"Pending leaves approvals data retrieved successfully")
            logger.info(f" employee info data: {data}")
            logger.info("Employee info retrieved successfully")
            return {
                "status": "success",
                "data": data
            }

            # Handle pagination info
            #if isinstance(data, dict) and 'data' in data:
             #   # API returns paginated response (dict with 'data' key and pagination fields)
              #  logger.info(f"Paginated data: {data}")
               # return {
                #    "status": "success",
                 #   "data": data.get('data', []),
                  #  "pagination": {
                   #     "current_page": data.get('current_page', page),
                    #    "total_pages": data.get('total_pages', 1),
                     #   "total_records": data.get('total', 0),
                      #  "page_size": data.get('per_page', page_size),
                       # "has_next": data.get('has_next', False),
                        #"has_previous": data.get('has_previous', False)
                    #}
                #}
            #else:
             #   # API returns a simple array or non-standard structure, so do client-side pagination
              #  logger.info(f"Non-paginated data: {data}")
               # total_records = data.get('totalPages', 0)  # This may not be correct if data is a list
                #logger.info(f"Total records: {total_records}")
                #page_size = 10  # Hardcoded fallback page size
                #page = 1        # Hardcoded fallback page number
                #start_index = (page - 1) * page_size
                #logger.info(f"Start index: {start_index}")
                #end_index = start_index + page_size
                #logger.info(f"End index: {end_index}")
                #paginated_data = data[start_index:end_index] if isinstance(data, list) else []
                #logger.info(f"Paginated data: {paginated_data}")

                #total_pages = (total_records + page_size - 1) // page_size
                #logger.info(f"Total pages: {total_pages}")

                #return {
                 #   "status": "success",
                  #  "data": paginated_data,
                   # "pagination": {
                    #    "current_page": page,
                     #   "total_pages": total_pages,
                      #  "total_records": total_records,
                       # "page_size": page_size,
                        #"has_next": page < total_pages,
                       # "has_previous": page > 1
                    #}
                #}
        else:
            logger.error(f"Failed to get pending leaves approvals: {response.status_code}")
            return {
                "status": "error",
                "message": f"Failed to get pending leaves approvals: {response.status_code}"
            }

    except Exception as e:
        logger.error(f"Error getting pending leaves approvals: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting pending leaves approvals: {str(e)}"
        }

def get_all_past_leaves(
    page: int = 1,
    page_size: int = 10,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Get all past leaves with pagination support.

    Args:
        page: Page number (default: 1)
        page_size: Number of records per page (default: 10)
        tool_context: Tool context for accessing session state

    Returns:
        Dict containing status, data, and pagination info
    """
    try:
        logger.info("=== GET ALL PAST LEAVES ===")
        logger.info(f"Page: {page}, Page Size: {page_size}")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/hr-view/all-past-leaves"

        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        # Add pagination parameters
        params = {
            "page": page,
            "limit": page_size
        }

        logger.info(f"Making GET request to: {url}")
        logger.info(f"Pagination params: {params}")

        # Make the API call
        response = requests.get(url, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info(f"Past leaves data retrieved successfully")

            # Handle pagination info
            if isinstance(data, dict) and 'data' in data:
                # API returns paginated response
                return {
                    "status": "success",
                    "data": data.get('data', []),
                    "pagination": {
                        "current_page": data.get('current_page', page),
                        "total_pages": data.get('total_pages', 1),
                        "total_records": data.get('total', 0),
                        "page_size": data.get('per_page', page_size),
                        "has_next": data.get('has_next', False),
                        "has_previous": data.get('has_previous', False)
                    }
                }
            else:
                # API returns simple array - implement client-side pagination
                total_records = len(data) if isinstance(data, list) else 0
                start_index = (page - 1) * page_size
                end_index = start_index + page_size
                paginated_data = data[start_index:end_index] if isinstance(data, list) else []

                total_pages = (total_records + page_size - 1) // page_size

                return {
                    "status": "success",
                    "data": paginated_data,
                    "pagination": {
                        "current_page": page,
                        "total_pages": total_pages,
                        "total_records": total_records,
                        "page_size": page_size,
                        "has_next": page < total_pages,
                        "has_previous": page > 1
                    }
                }
        else:
            logger.error(f"Failed to get past leaves: {response.status_code}")
            return {
                "status": "error",
                "message": f"Failed to get past leaves: {response.status_code}"
            }

    except Exception as e:
        logger.error(f"Error getting past leaves: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting past leaves: {str(e)}"
        }

def get_all_leaves_overview(
    page: int = 1,
    page_size: int = 10,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Get all leaves overview with pagination support.

    Args:
        page: Page number (default: 1)
        page_size: Number of records per page (default: 10)
        tool_context: Tool context for accessing session state

    Returns:
        Dict containing status, data, and pagination info
    """
    try:
        logger.info("=== GET ALL LEAVES OVERVIEW ===")
        logger.info(f"Page: {page}, Page Size: {page_size}")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/hr-view/all-leave-overview"

        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        # Add pagination parameters
        params = {
            "page": page,
            "limit": page_size
        }

        logger.info(f"Making GET request to: {url}")
        logger.info(f"Pagination params: {params}")

        # Make the API call
        response = requests.get(url, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info(f"Leaves overview data retrieved successfully")

            # Handle pagination info
            if isinstance(data, dict) and 'data' in data:
                # API returns paginated response
                return {
                    "status": "success",
                    "data": data.get('data', []),
                    "pagination": {
                        "current_page": data.get('current_page', page),
                        "total_pages": data.get('total_pages', 1),
                        "total_records": data.get('total', 0),
                        "page_size": data.get('per_page', page_size),
                        "has_next": data.get('has_next', False),
                        "has_previous": data.get('has_previous', False)
                    }
                }
            else:
                # API returns simple array - implement client-side pagination
                total_records = len(data) if isinstance(data, list) else 0
                start_index = (page - 1) * page_size
                end_index = start_index + page_size
                paginated_data = data[start_index:end_index] if isinstance(data, list) else []

                total_pages = (total_records + page_size - 1) // page_size

                return {
                    "status": "success",
                    "data": paginated_data,
                    "pagination": {
                        "current_page": page,
                        "total_pages": total_pages,
                        "total_records": total_records,
                        "page_size": page_size,
                        "has_next": page < total_pages,
                        "has_previous": page > 1
                    }
                }
        else:
            logger.error(f"Failed to get leaves overview: {response.status_code}")
            return {
                "status": "error",
                "message": f"Failed to get leaves overview: {response.status_code}"
            }

    except Exception as e:
        logger.error(f"Error getting leaves overview: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting leaves overview: {str(e)}"
        }

def get_all_comp_off_pending_approvals(
    page: int = 1,
    page_size: int = 10,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Get all comp off pending approvals with pagination support.

    Args:
        page: Page number (default: 1)
        page_size: Number of records per page (default: 10)
        tool_context: Tool context for accessing session state

    Returns:
        Dict containing status, data, and pagination info
    """
    try:
        logger.info("=== GET ALL COMP OFF PENDING APPROVALS ===")
        logger.info(f"Page: {page}, Page Size: {page_size}")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://qa-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/hr-view/all-comp-off/pending-approvals"

        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "IN-MT-001"
        }

        logger.info(f"Making GET request to: {url}")

        # Make the API call
        response = requests.get(url, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info(f"Comp off pending approvals data retrieved successfully")

            # Handle pagination info
            if isinstance(data, dict) and 'data' in data:
                # API returns paginated response
                return {
                    "status": "success",
                    "data": data.get('data', []),
                    "pagination": {
                        "current_page": data.get('current_page', page),
                        "total_pages": data.get('total_pages', 1),
                        "total_records": data.get('total', 0),
                        "page_size": data.get('per_page', page_size),
                        "has_next": data.get('has_next', False),
                        "has_previous": data.get('has_previous', False)
                    }
                }
            else:
                # API returns simple array - implement client-side pagination
                total_records = len(data) if isinstance(data, list) else 0
                start_index = (page - 1) * page_size
                end_index = start_index + page_size
                paginated_data = data[start_index:end_index] if isinstance(data, list) else []

                total_pages = (total_records + page_size - 1) // page_size

                return {
                    "status": "success",
                    "data": paginated_data,
                    "pagination": {
                        "current_page": page,
                        "total_pages": total_pages,
                        "total_records": total_records,
                        "page_size": page_size,
                        "has_next": page < total_pages,
                        "has_previous": page > 1
                    }
                }
        else:
            logger.error(f"Failed to get comp off pending approvals: {response.status_code}")
            return {
                "status": "error",
                "message": f"Failed to get comp off pending approvals: {response.status_code}"
            }

    except Exception as e:
        logger.error(f"Error getting comp off pending approvals: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting comp off pending approvals: {str(e)}"
        }
            
# Old hardcoded helper functions removed - now using LLM-powered intelligent parsing

def cancel_leave_intelligent(
    user_description: str,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Cancels a leave request using natural language description from the user.
    
    This function allows users to cancel leaves by describing them naturally
    (e.g., "the leave for July 22nd", "my sick leave", "the leave I applied yesterday")
    without needing to know internal Leave IDs.

    Args:
        user_description: Natural language description of which leave to cancel
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and cancellation details
    """
    try:
        logger.info("=== CANCEL LEAVE INTELLIGENT ===")
        logger.info(f"User description: {user_description}")

        # First, get all pending leaves
        pending_result = get_pending_leaves(None, tool_context)
        
        if pending_result.get("status") != "success":
            return {
                "status": "error",
                "message": "Failed to retrieve pending leaves",
                "details": pending_result
            }

        pending_leaves = pending_result.get("pending_leaves", [])
        
        if not pending_leaves:
            return {
                "status": "error",
                "message": "You don't have any pending leave requests to cancel."
            }

        # Find the matching leave based on user description
        matching_leave = _find_leave_by_description(pending_leaves, user_description)
        
        if not matching_leave:
            # Show available leaves for user to choose from
            available_leaves = []
            for leave in pending_leaves:
                leave_type = leave.get("leaveTypeName", "Unknown")
                start_date = leave.get("startDate", "")
                end_date = leave.get("endDate", "")
                requested_on = leave.get("requestedOn", "")
                reason = leave.get("note", "")
                
                available_leaves.append({
                    "description": f"{leave_type} from {start_date} to {end_date} - {reason}",
                    "leave_id": leave.get("employeeLeaveApplicationId")
                })
            
            return {
                "status": "clarification_needed",
                "message": "I found multiple pending leaves. Please specify which one you'd like to cancel:",
                "available_leaves": available_leaves,
                "suggestion": "You can specify by date (e.g., 'the leave for July 22nd'), type (e.g., 'my sick leave'), or reason."
            }

        # Cancel the matching leave
        leave_id = matching_leave.get("employeeLeaveApplicationId")
        if not leave_id:
            return {
                "status": "error",
                "message": "Could not identify the leave ID for cancellation."
            }

        # Call the actual cancel function
        cancel_result = cancel_leave(leave_id, None, tool_context)
        
        if cancel_result.get("status") == "success":
            # Get leave details for confirmation message
            leave_type = matching_leave.get("leaveTypeName", "Unknown")
            start_date = matching_leave.get("startDate", "")
            end_date = matching_leave.get("endDate", "")
            
            return {
                "status": "success",
                "message": f"Successfully cancelled your {leave_type} leave from {start_date} to {end_date}.",
                "leave_details": {
                    "type": leave_type,
                    "start_date": start_date,
                    "end_date": end_date
                }
            }
        else:
            return cancel_result

    except Exception as e:
        logger.error(f"Error in cancel_leave_intelligent: {e}")
        return {
            "status": "error",
            "message": f"Error cancelling leave: {str(e)}"
        }


def _find_leave_by_description(pending_leaves: List[Dict], user_description: str) -> Optional[Dict]:
    """
    Finds a leave from the pending leaves list based on user's natural language description.
    
    Args:
        pending_leaves: List of pending leave dictionaries
        user_description: User's natural language description of the leave
        
    Returns:
        Matching leave dictionary or None if no match found
    """
    try:
        user_desc_lower = user_description.lower()
        
        # Extract date patterns from user description
        import re
        date_patterns = [
            r'(\d{1,2})[\/\-](\d{1,2})',  # MM/DD or DD/MM
            r'(\d{1,2})\s+(january|february|march|april|may|june|july|august|september|october|november|december)',  # DD Month
            r'(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2})',  # Month DD
            r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
        ]
        
        # Extract leave type patterns
        leave_type_patterns = [
            r'(sick|casual|annual|vacation|personal|emergency|maternity|paternity|bereavement|compensatory|unpaid)',
            r'(cl|sl|al|vl|pl|ml|bl|el|comp|ul)',  # Abbreviations
        ]
        
        # Extract date information
        extracted_date = None
        for pattern in date_patterns:
            match = re.search(pattern, user_desc_lower)
            if match:
                # Try to parse the date
                try:
                    if len(match.groups()) == 2:
                        # Handle MM/DD or DD/MM patterns
                        if '/' in match.group() or '-' in match.group():
                            parts = re.split(r'[\/\-]', match.group())
                            if len(parts) == 2:
                                # Assume MM/DD format for now
                                month, day = int(parts[0]), int(parts[1])
                                if 1 <= month <= 12 and 1 <= day <= 31:
                                    extracted_date = f"2025-{month:02d}-{day:02d}"
                    elif len(match.groups()) == 3:
                        # Handle YYYY-MM-DD pattern
                        year, month, day = match.groups()
                        extracted_date = f"{year}-{int(month):02d}-{int(day):02d}"
                except:
                    continue
        
        # Extract leave type information
        extracted_leave_type = None
        for pattern in leave_type_patterns:
            match = re.search(pattern, user_desc_lower)
            if match:
                extracted_leave_type = match.group(1).lower()
                break
        
        # Score each leave based on how well it matches the description
        best_match = None
        best_score = 0
        
        for leave in pending_leaves:
            score = 0
            leave_start = leave.get("startDate", "")
            leave_end = leave.get("endDate", "")
            leave_type = leave.get("leaveTypeName", "").lower()
            leave_reason = leave.get("note", "").lower()
            
            # Date matching (highest priority)
            if extracted_date:
                if extracted_date == leave_start or extracted_date == leave_end:
                    score += 10
                elif extracted_date in leave_start or extracted_date in leave_end:
                    score += 8
            
            # Leave type matching
            if extracted_leave_type:
                if extracted_leave_type in leave_type or leave_type in extracted_leave_type:
                    score += 5
                # Handle abbreviations
                type_mapping = {
                    'cl': 'casual', 'sl': 'sick', 'al': 'annual', 'vl': 'vacation',
                    'pl': 'personal', 'ml': 'maternity', 'bl': 'bereavement',
                    'el': 'emergency', 'comp': 'compensatory', 'ul': 'unpaid'
                }
                if extracted_leave_type in type_mapping:
                    mapped_type = type_mapping[extracted_leave_type]
                    if mapped_type in leave_type:
                        score += 5
            
            # Keyword matching in reason
            if any(word in leave_reason for word in user_desc_lower.split()):
                score += 3
            
            # Exact phrase matching
            if user_desc_lower in leave_reason or leave_reason in user_desc_lower:
                score += 4
            
            # Today/tomorrow/yesterday matching
            from datetime import datetime, timedelta
            today = datetime.now().date()
            
            if 'today' in user_desc_lower and leave_start == today.strftime('%Y-%m-%d'):
                score += 7
            elif 'tomorrow' in user_desc_lower:
                tomorrow = today + timedelta(days=1)
                if leave_start == tomorrow.strftime('%Y-%m-%d'):
                    score += 7
            elif 'yesterday' in user_desc_lower:
                yesterday = today - timedelta(days=1)
                if leave_start == yesterday.strftime('%Y-%m-%d'):
                    score += 7
            
            # If this is the only leave, give it a base score
            if len(pending_leaves) == 1:
                score += 2
            
            if score > best_score:
                best_score = score
                best_match = leave
        
        # Only return a match if the score is high enough
        if best_score >= 3:
            return best_match
        
        return None
        
    except Exception as e:
        logger.error(f"Error in _find_leave_by_description: {e}")
        return None
