"""
User roles and permissions module.

This module contains functions for checking user roles and permissions.
"""

import logging
from typing import Dict, Any, Optional, List

# Set up logger
logger = logging.getLogger(__name__)
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Mock admin users database
# In a real implementation, this would be fetched from a database or directory service
_admin_users = {
    "<EMAIL>": True,
    "<EMAIL>": True,
    "<EMAIL>": True,
    "<EMAIL>": True,
    "<EMAIL>": True,
    "<EMAIL>": True,
    "<EMAIL>": True
}

def is_admin_user(email = None) -> bool:
    """Check if a user is an administrator.

    Args:
        email (Optional[str], optional): Email address of the user. Defaults to None.

    Returns:
        bool: True if the user is an admin, False otherwise
    """

    # For testing purposes, if no email is provided, use a mock value
    if not email:
        # Default to a non-admin user for safety
        email = "<EMAIL>"
        logger.info(f"Using mock user email: {email}")

    email = email.lower()

    # Check if the user is in the admin users list
    if email in _admin_users:
        logger.info(f"User {email} is an administrator")
        return True

    # Check if the email contains admin-related keywords
    admin_keywords = ["admin", "sysadmin", "system", "root", "superuser", "helpdesk", "support"]
    for keyword in admin_keywords:
        if keyword in email.split("@")[0].lower():
            logger.info(f"User {email} is identified as an administrator based on email pattern")
            return True

    logger.info(f"User {email} is not an administrator")
    return False

def can_reset_password(requesting_user_email = None, target_user_email = None) -> bool:
    """Check if a user can reset another user's password.

    CRITICAL SECURITY FUNCTION: This enforces the rule that regular users can only reset their own passwords,
    while administrators can reset passwords for any user.

    Args:
        requesting_user_email (Optional[str], optional): Email of the user requesting the password reset. Defaults to None.
        target_user_email (Optional[str], optional): Email of the user whose password is being reset. Defaults to None.

    Returns:
        bool: True if the requesting user can reset the target user's password, False otherwise
    """
    # For testing purposes, if no emails are provided, use mock values
    logger.info(f"Checking password reset permission: requester={requesting_user_email}, target={target_user_email}")
    if not requesting_user_email:
        # Mock requesting user - regular non-admin user
        requesting_user_email = "<EMAIL>"
        logger.info(f"Using mock requesting user email: {requesting_user_email}")

    if not target_user_email:
        # Mock target user - different from requesting user
        target_user_email = "<EMAIL>"
        logger.info(f"Using mock target user email: {target_user_email}")

    requesting_user_email = requesting_user_email.lower()
    target_user_email = target_user_email.lower()

    # Users can always reset their own passwords
    if requesting_user_email == target_user_email:
        logger.info(f"User {requesting_user_email} is resetting their own password")
        return True

    # Only admins can reset other users' passwords
    admin_status = is_admin_user(requesting_user_email)
    if admin_status:
        logger.info(f"Admin {requesting_user_email} is resetting password for {target_user_email}")
        return True

    # If we get here, it means a non-admin user is trying to reset someone else's password
    logger.warning(f"PERMISSION DENIED: Non-admin user {requesting_user_email} attempted to reset password for {target_user_email}")
    return False

def check_password_reset_authorization(target_user_email, requesting_user_email = None) -> Dict[str, Any]:
    """Check if a user is authorized to reset a password.

    With OTP verification, we always allow the password reset to proceed since the user's
    identity will be verified using OTP. This function now simply logs the request and
    returns success.

    Args:
        target_user_email (str): Email of the user whose password is being reset
        requesting_user_email (Optional[str], optional): Email of the user requesting the reset. Defaults to None.

    Returns:
        Dict[str, Any]: Dictionary containing authorization status and details
    """
    # If requesting_user_email is not provided, use the target email
    if not requesting_user_email:
        requesting_user_email = target_user_email

    logger.info(f"Password reset requested for: {target_user_email}")

    # With OTP verification, we always allow the process to continue
    # The actual verification will happen through OTP
    return {
        "status": "success",
        "message": f"Password reset request for {target_user_email} will proceed with OTP verification.",
        "authorized": True,
        "is_admin": False,  # Default to non-admin for security
        "target_email": target_user_email
    }
