"""
Unified Attendance Management Tools Module.

This module provides comprehensive attendance management functionality using the
Flexible WagonHR Agent for real-time API integration. All attendance-related
operations are consolidated here for better organization and maintainability.

Replaces both hr_api.py attendance functions and old attendance_tools.py mock functions
with a single, unified module that uses real WagonHR APIs.
"""

import logging
import datetime
from typing import Dict, Any, Optional, List
import requests
from utils.hr_auth import hr_auth
from google.adk.tools.tool_context import ToolContext
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()
# Set up logger
logger = logging.getLogger(__name__)

# Base URL for all API calls
BASE_URL = "https://qa-api-wagonhr.mouritech.net"

# Attendance status types
ATTENDANCE_STATUS = {
    "present": "Present",
    "absent": "Absent", 
    "late": "Late",
    "half_day": "Half Day",
    "work_from_home": "Work From Home",
    "on_leave": "On Leave",
    "holiday": "Holiday",
    "checkin": "Check In",
    "checkout": "Check Out"
}

def _get_auth_headers() -> Dict[str, str]:
    """
    Get authentication headers for API calls.
    
    Returns:
        Dictionary containing authentication headers
    """
    auth_headers = hr_auth.get_wagonhr_auth_headers()
    if not auth_headers:
        logger.error("Failed to get WagonHR authentication headers")
        return None

    return {
        **auth_headers,
        "Accept": "application/json",
        "Content-Type": "application/json",
        "x-tenantid": "mouritech",
        "x-entityid": "IN-MT-001"
    }

def _make_api_request(
    method: str,
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    json_data: Optional[Dict[str, Any]] = None,
    tool_context: Optional[ToolContext] = None,
    timeout: int = 30
) -> Dict[str, Any]:
    """
    Generic method to make API requests.
    
    Args:
        method: HTTP method (GET, POST, PUT, DELETE)
        endpoint: API endpoint
        params: Query parameters
        json_data: Request body for POST/PUT
        tool_context: Optional tool context for additional parameters
        timeout: Request timeout in seconds
        
    Returns:
        Dictionary containing response data and status
    """
    try:
        headers = _get_auth_headers()
        if not headers:
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Handle tool context parameters if provided
        if tool_context:
            if isinstance(tool_context, dict):
                # If tool_context is a dict, merge it with params
                if params is None:
                    params = {}
                params.update(tool_context)
            elif hasattr(tool_context, "__dict__"):
                # If tool_context is an object, get its attributes
                context_dict = {
                    k: v for k, v in tool_context.__dict__.items()
                    if not k.startswith('_') and v is not None
                }
                if params is None:
                    params = {}
                params.update(context_dict)

        url = f"{BASE_URL}{endpoint}"
        logger.info(f"Making {method} request to: {url}")
        if params:
            logger.info(f"Query parameters: {params}")
        if json_data:
            logger.info(f"Request body: {json_data}")

        response = requests.request(
            method=method,
            url=url,
            headers=headers,
            params=params,
            json=json_data,
            timeout=timeout
        )

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201, 204]:
            try:
                data = response.json() if response.content else {}
            except:
                data = {}
            return {
                "status": "success",
                "data": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"
            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        error_msg = f"Error making API request: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": error_msg
        }


def get_attendance_report(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    employee_email: Optional[str] = None,
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """
    Retrieves attendance report for an employee using WagonHR API.

    Args:
        employee_email: Email of the employee
        start_date: Start date of the report (YYYY-MM-DD)
        end_date: End date of the report (YYYY-MM-DD)
        tool_context: Tool context for additional parameters

    Returns:
        Dictionary containing operation status and attendance report details
    """
    if not start_date or not end_date:
        return {
            "status": "error",
            "message": "start_date and end_date are required"
        }

    params = {
        "startDate": start_date,
        "endDate": end_date
    }

    if employee_email:
        params["employeeEmail"] = employee_email

    return _make_api_request(
        "GET",
        "/api/hrms/attendance/report",
        params=params,
        tool_context=tool_context
    )

def get_attendance_summary(
    query: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    employee_id: Optional[str] = None,
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """
    Retrieves attendance summary for the currently authenticated employee.
    Uses LLM's intelligence to handle date parsing and calculations.

    Args:
        query: Natural language query about attendance period
              (e.g., "this week", "last month", "today", "yesterday")
        start_date: Start date in YYYY-MM-DD format (if LLM provides specific dates)
        end_date: End date in YYYY-MM-DD format (if LLM provides specific dates)
        employee_id: Optional employee ID (if not provided, gets from employee info)
        tool_context: Tool context containing employee information

    Returns:
        Dictionary containing operation status and attendance summary
    """
    print("======== get_attendance_summary ==========")
    print(f"employee_id: {employee_id}")
    print(f"query: {query}")
    print(f"start_date: {start_date}")
    print(f"end_date: {end_date}")
    print(f"tool_context: {tool_context}")
    print("======== get_attendance_summary ==========")
    
    # Validate dates if provided
    if start_date and end_date:
        try:
            start = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.datetime.strptime(end_date, '%Y-%m-%d')
            if end < start:
                return {
                    "status": "error",
                    "message": "End date cannot be before start date"
                }
        except ValueError:
            return {
                "status": "error",
                "message": "Invalid date format. Please use YYYY-MM-DD"
            }

    # If employee_id is not provided, get it from employee info
    if not employee_id:
        print("Getting employee number from employee info...")
        employee_info_result = _make_api_request(
            "GET", 
            "/api/hrms/hris/employee/id", 
            tool_context=tool_context
        )
        
        if employee_info_result.get("status") != "success":
            return {
                "status": "error",
                "message": f"Failed to get employee information: {employee_info_result.get('message', 'Unknown error')}"
            }
        
        employee_data = employee_info_result.get("data", {})
        employee_id = employee_data.get("employeeNo") or employee_data.get("employeeNumber") or employee_data.get("id")
        
        if not employee_id:
            return {
                "status": "error",
                "message": "Could not retrieve employee number from employee information"
            }
        
        print(f"Retrieved employee number: {employee_id}")

    # Now use the employee_id in the attendance summary API call
    params = {
        "employee_id": employee_id
    }

    print(f"params: {params}")
    return _make_api_request(
        "GET",
        "/api/hrms/hris/wordpress/calendar/posts-by-employee",
        params=params,
        tool_context=tool_context
    )
