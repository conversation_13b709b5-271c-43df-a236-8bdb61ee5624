2025-07-24 09:42:12,861 - itsm_solution - INFO - Logging initialized
2025-07-24 09:42:12,861 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:42:12,861 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:42:12,861 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:42:12,861 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:42:12,861 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:42:12,861 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:42:12,861 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:42:12,861 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:42:12,861 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:42:12,861 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:42:12,862 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:42:12,862 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:42:12,862 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:42:12,862 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:42:12,862 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:42:12,862 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:42:12,862 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:42:12,868 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-24 09:42:12,868 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-24 09:42:12,868 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:42:12,871 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:42:12,871 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:42:12,871 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:42:12,871 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:42:12,871 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:42:12,871 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:42:12,872 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-24 09:42:12,872 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-24 09:42:12,872 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:42:12,873 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:42:12,873 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:42:12,873 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:42:12,873 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:42:12,873 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:42:12,873 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:42:12,873 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-24 09:42:12,873 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-24 09:42:12,873 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:42:12,874 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:42:12,874 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:42:12,874 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:42:12,874 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:42:12,874 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:42:12,874 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:42:12,875 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-24 09:42:12,875 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:42:12,875 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:42:12,875 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:42:12,875 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-24 09:42:12,875 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:42:12,876 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:42:12,876 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:42:12,876 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-24 09:42:12,876 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:42:12,876 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:42:12,876 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:42:12,876 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:42:12,877 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:42:12,877 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:42:12,877 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:42:12,877 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:42:12,877 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:42:12,899 - itsm_solution - INFO - Logging initialized
2025-07-24 09:42:12,899 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:42:12,899 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:42:12,899 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:42:12,899 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:42:12,899 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:42:12,899 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:42:12,899 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:42:12,899 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:42:12,899 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:42:12,899 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:42:12,899 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:42:12,899 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:42:12,899 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:42:12,899 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:42:12,899 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:42:12,899 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:42:12,899 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:42:12,899 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:42:12,900 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:42:12,900 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:42:12,900 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:42:12,900 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:42:12,900 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:42:12,900 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:42:12,900 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:42:12,901 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:42:12,901 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:42:12,901 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:42:12,901 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:42:12,902 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:42:12,902 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:42:12,902 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:42:12,902 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:42:12,902 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:42:12,903 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:42:12,903 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:42:12,903 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:42:12,903 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:42:12,903 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:42:12,903 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:42:12,903 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:42:12,903 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:42:12,903 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:42:12,903 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:42:28,409 - api_server - ERROR - Failed to get API auth token: {"timeStamp":"2025-07-24T04:12:28.382871968","message":"An error occurred while attempting to decode the Jwt: Jwt expired at 2025-07-24T04:09:55Z","path":null}
2025-07-24 09:43:35,335 - itsm_solution - INFO - Logging initialized
2025-07-24 09:43:35,335 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:43:35,335 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:43:35,335 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:43:35,335 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:43:35,335 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:43:35,335 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:43:35,335 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:43:35,335 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:43:35,335 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:43:35,335 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:43:35,335 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:43:35,335 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:43:35,335 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:43:35,335 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:43:35,335 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:43:35,335 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:43:35,335 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:43:35,342 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-24 09:43:35,342 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-24 09:43:35,342 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:43:35,343 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:43:35,343 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:43:35,343 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:43:35,343 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:43:35,343 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:43:35,343 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:43:35,344 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-24 09:43:35,344 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-24 09:43:35,344 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:43:35,345 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:43:35,345 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:43:35,345 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:43:35,345 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:43:35,345 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:43:35,345 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:43:35,345 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-24 09:43:35,345 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-24 09:43:35,345 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:43:35,346 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:43:35,346 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:43:35,346 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:43:35,346 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:43:35,346 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:43:35,346 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:43:35,346 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-24 09:43:35,346 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-24 09:43:35,346 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:43:35,346 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:43:35,347 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:43:35,347 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-24 09:43:35,347 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:43:35,347 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:43:35,347 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:43:35,348 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-24 09:43:35,348 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-24 09:43:35,348 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:43:35,348 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:43:35,348 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:43:35,348 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:43:35,348 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:43:35,348 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:43:35,348 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:43:35,348 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:43:35,348 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:43:35,348 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:43:35,348 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:43:35,348 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:43:35,375 - itsm_solution - INFO - Logging initialized
2025-07-24 09:43:35,375 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:43:35,375 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:43:35,375 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:43:35,375 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:43:35,375 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:43:35,375 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:43:35,375 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:43:35,375 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:43:35,375 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:43:35,375 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:43:35,375 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:43:35,375 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:43:35,375 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:43:35,375 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:43:35,375 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:43:35,375 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:43:35,375 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:43:35,375 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:43:35,376 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:43:35,376 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:43:35,376 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:43:35,376 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:43:35,376 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:43:35,376 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:43:35,376 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:43:35,377 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:43:35,377 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:43:35,377 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:43:35,377 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:43:35,378 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:43:35,378 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:43:35,378 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:43:35,378 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:43:35,378 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:43:35,379 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:43:35,379 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:43:35,379 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:43:35,379 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:43:35,379 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:43:35,379 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:43:35,379 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:43:35,379 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:43:35,379 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:43:35,379 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:43:57,237 - itsm_solution - INFO - Logging initialized
2025-07-24 09:43:57,237 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:43:57,237 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:43:57,238 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:43:57,238 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:43:57,238 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:43:57,238 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:43:57,238 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:43:57,238 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:43:57,238 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:43:57,238 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:43:57,238 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:43:57,238 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:43:57,238 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:43:57,238 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:43:57,238 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:43:57,238 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:43:57,238 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:43:57,245 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-24 09:43:57,245 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-24 09:43:57,245 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:43:57,247 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:43:57,247 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:43:57,247 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:43:57,247 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:43:57,247 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:43:57,247 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:43:57,247 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-24 09:43:57,247 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-24 09:43:57,248 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:43:57,248 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:43:57,249 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:43:57,249 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:43:57,249 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:43:57,249 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:43:57,249 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:43:57,249 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-24 09:43:57,249 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-24 09:43:57,249 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:43:57,250 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:43:57,250 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:43:57,250 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:43:57,250 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:43:57,250 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:43:57,250 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:43:57,250 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-24 09:43:57,250 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-24 09:43:57,250 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:43:57,252 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:43:57,252 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:43:57,252 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:43:57,252 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:43:57,252 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:43:57,252 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:43:57,252 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-24 09:43:57,252 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-24 09:43:57,252 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:43:57,253 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:43:57,253 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:43:57,253 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:43:57,253 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:43:57,253 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:43:57,253 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:43:57,253 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-24 09:43:57,253 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-24 09:43:57,253 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:43:57,254 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:43:57,254 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:43:57,254 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:43:57,254 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:43:57,254 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:43:57,254 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:43:57,254 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:43:57,254 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:43:57,254 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:43:57,254 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:43:57,254 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:43:57,278 - itsm_solution - INFO - Logging initialized
2025-07-24 09:43:57,278 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:43:57,278 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:43:57,278 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:43:57,278 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:43:57,278 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:43:57,278 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:43:57,278 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:43:57,278 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:43:57,278 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:43:57,279 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:43:57,279 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:43:57,279 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:43:57,279 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:43:57,279 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:43:57,279 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:43:57,279 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:43:57,279 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:43:57,279 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:43:57,280 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:43:57,280 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:43:57,280 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:43:57,281 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:43:57,281 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:43:57,281 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:43:57,281 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:43:57,281 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:43:57,282 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:43:57,282 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:43:57,282 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:43:57,282 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:43:57,282 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:43:57,282 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:43:57,282 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:43:57,282 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:43:57,282 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:43:57,282 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:43:57,282 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:43:57,282 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:43:57,282 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:44:08,487 - itsm_solution - INFO - Logging initialized
2025-07-24 09:44:08,487 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:44:08,487 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:44:08,487 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:44:08,487 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:44:08,487 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:44:08,487 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:44:08,487 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:44:08,487 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:44:08,487 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:44:08,487 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:44:08,487 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:44:08,487 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:44:08,487 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:44:08,487 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:44:08,487 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:44:08,487 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:44:08,487 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:44:08,493 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-24 09:44:08,493 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-24 09:44:08,493 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:44:08,495 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:44:08,495 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:44:08,495 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:44:08,495 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:44:08,495 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:44:08,495 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:44:08,495 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-24 09:44:08,495 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-24 09:44:08,495 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:44:08,496 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:44:08,496 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:44:08,496 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:44:08,496 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:44:08,496 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:44:08,496 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:44:08,497 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-24 09:44:08,497 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-24 09:44:08,497 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:44:08,497 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:44:08,497 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:44:08,497 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:44:08,497 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:44:08,497 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:44:08,498 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-24 09:44:08,498 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:44:08,498 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:44:08,498 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:44:08,498 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-24 09:44:08,498 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:44:08,499 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:44:08,499 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:44:08,499 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-24 09:44:08,499 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:44:08,500 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:44:08,500 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:44:08,500 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:44:08,500 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:44:08,500 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:44:08,500 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:44:08,500 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:44:08,500 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:44:08,500 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:44:08,500 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:44:08,500 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:44:08,528 - itsm_solution - INFO - Logging initialized
2025-07-24 09:44:08,528 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:44:08,528 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:44:08,528 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:44:08,528 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:44:08,528 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:44:08,528 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:44:08,528 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:44:08,528 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:44:08,528 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:44:08,528 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:44:08,528 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:44:08,528 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:44:08,528 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:44:08,528 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:44:08,528 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:44:08,528 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:44:08,528 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:44:08,528 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:44:08,529 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:44:08,529 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:44:08,529 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:44:08,529 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:44:08,529 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:44:08,529 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:44:08,529 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:44:08,530 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:44:08,530 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:44:08,530 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:44:08,530 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:44:08,531 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:44:08,531 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:44:08,531 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:44:08,531 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:44:08,532 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:44:08,532 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:44:08,532 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:44:08,532 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:44:08,532 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:44:08,532 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:44:08,532 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:44:08,532 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:44:08,532 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:44:08,532 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:44:08,532 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
