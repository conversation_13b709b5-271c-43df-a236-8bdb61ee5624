2025-07-24 09:42:12,861 - itsm_solution - INFO - Logging initialized
2025-07-24 09:42:12,861 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:42:12,861 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:42:12,861 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:42:12,861 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:42:12,861 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:42:12,861 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:42:12,861 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:42:12,861 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:42:12,861 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:42:12,861 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:42:12,862 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:42:12,862 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:42:12,862 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:42:12,862 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:42:12,862 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:42:12,862 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:42:12,862 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:42:12,868 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-24 09:42:12,868 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-24 09:42:12,868 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:42:12,871 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:42:12,871 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:42:12,871 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:42:12,871 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:42:12,871 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:42:12,871 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:42:12,872 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-24 09:42:12,872 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-24 09:42:12,872 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:42:12,873 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:42:12,873 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:42:12,873 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:42:12,873 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:42:12,873 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:42:12,873 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:42:12,873 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-24 09:42:12,873 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-24 09:42:12,873 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:42:12,874 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:42:12,874 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:42:12,874 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:42:12,874 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:42:12,874 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:42:12,874 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:42:12,875 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-24 09:42:12,875 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:42:12,875 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:42:12,875 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:42:12,875 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-24 09:42:12,875 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-24 09:42:12,875 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:42:12,876 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:42:12,876 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:42:12,876 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-24 09:42:12,876 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:42:12,876 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:42:12,876 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:42:12,876 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:42:12,876 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:42:12,877 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:42:12,877 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:42:12,877 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:42:12,877 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:42:12,877 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:42:12,899 - itsm_solution - INFO - Logging initialized
2025-07-24 09:42:12,899 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:42:12,899 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:42:12,899 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:42:12,899 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:42:12,899 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:42:12,899 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:42:12,899 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:42:12,899 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:42:12,899 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:42:12,899 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:42:12,899 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:42:12,899 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:42:12,899 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:42:12,899 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:42:12,899 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:42:12,899 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:42:12,899 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:42:12,899 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:42:12,900 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:42:12,900 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:42:12,900 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:42:12,900 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:42:12,900 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:42:12,900 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:42:12,900 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:42:12,901 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:42:12,901 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:42:12,901 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:42:12,901 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:42:12,901 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:42:12,902 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:42:12,902 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:42:12,902 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:42:12,902 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:42:12,902 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:42:12,902 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:42:12,903 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:42:12,903 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:42:12,903 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:42:12,903 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:42:12,903 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:42:12,903 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:42:12,903 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:42:12,903 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:42:12,903 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:42:12,903 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:42:28,409 - api_server - ERROR - Failed to get API auth token: {"timeStamp":"2025-07-24T04:12:28.382871968","message":"An error occurred while attempting to decode the Jwt: Jwt expired at 2025-07-24T04:09:55Z","path":null}
2025-07-24 09:43:35,335 - itsm_solution - INFO - Logging initialized
2025-07-24 09:43:35,335 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:43:35,335 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:43:35,335 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:43:35,335 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:43:35,335 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:43:35,335 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:43:35,335 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:43:35,335 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:43:35,335 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:43:35,335 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:43:35,335 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:43:35,335 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:43:35,335 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:43:35,335 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:43:35,335 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:43:35,335 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:43:35,335 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:43:35,342 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-24 09:43:35,342 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-24 09:43:35,342 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:43:35,343 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:43:35,343 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:43:35,343 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:43:35,343 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:43:35,343 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:43:35,343 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:43:35,344 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-24 09:43:35,344 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-24 09:43:35,344 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:43:35,345 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:43:35,345 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:43:35,345 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:43:35,345 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:43:35,345 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:43:35,345 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:43:35,345 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-24 09:43:35,345 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-24 09:43:35,345 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:43:35,346 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:43:35,346 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:43:35,346 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:43:35,346 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:43:35,346 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:43:35,346 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:43:35,346 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-24 09:43:35,346 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-24 09:43:35,346 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:43:35,346 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:43:35,347 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:43:35,347 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-24 09:43:35,347 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:43:35,347 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:43:35,347 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:43:35,347 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:43:35,348 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-24 09:43:35,348 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-24 09:43:35,348 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:43:35,348 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:43:35,348 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:43:35,348 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:43:35,348 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:43:35,348 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:43:35,348 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:43:35,348 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:43:35,348 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:43:35,348 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:43:35,348 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:43:35,348 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:43:35,375 - itsm_solution - INFO - Logging initialized
2025-07-24 09:43:35,375 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:43:35,375 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:43:35,375 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:43:35,375 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:43:35,375 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:43:35,375 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:43:35,375 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:43:35,375 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:43:35,375 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:43:35,375 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:43:35,375 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:43:35,375 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:43:35,375 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:43:35,375 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:43:35,375 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:43:35,375 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:43:35,375 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:43:35,375 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:43:35,376 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:43:35,376 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:43:35,376 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:43:35,376 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:43:35,376 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:43:35,376 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:43:35,376 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:43:35,377 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:43:35,377 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:43:35,377 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:43:35,377 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:43:35,377 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:43:35,378 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:43:35,378 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:43:35,378 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:43:35,378 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:43:35,378 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:43:35,378 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:43:35,379 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:43:35,379 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:43:35,379 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:43:35,379 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:43:35,379 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:43:35,379 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:43:35,379 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:43:35,379 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:43:35,379 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:43:35,379 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:43:57,237 - itsm_solution - INFO - Logging initialized
2025-07-24 09:43:57,237 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:43:57,237 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:43:57,238 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:43:57,238 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:43:57,238 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:43:57,238 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:43:57,238 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:43:57,238 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:43:57,238 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:43:57,238 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:43:57,238 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:43:57,238 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:43:57,238 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:43:57,238 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:43:57,238 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:43:57,238 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:43:57,238 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:43:57,245 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-24 09:43:57,245 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-24 09:43:57,245 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:43:57,247 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:43:57,247 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:43:57,247 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:43:57,247 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:43:57,247 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:43:57,247 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:43:57,247 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-24 09:43:57,247 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-24 09:43:57,248 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:43:57,248 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:43:57,249 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:43:57,249 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:43:57,249 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:43:57,249 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:43:57,249 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:43:57,249 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-24 09:43:57,249 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-24 09:43:57,249 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:43:57,250 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:43:57,250 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:43:57,250 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:43:57,250 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:43:57,250 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:43:57,250 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:43:57,250 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-24 09:43:57,250 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-24 09:43:57,250 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:43:57,252 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:43:57,252 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:43:57,252 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:43:57,252 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:43:57,252 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:43:57,252 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:43:57,252 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-24 09:43:57,252 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-24 09:43:57,252 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:43:57,253 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:43:57,253 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:43:57,253 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:43:57,253 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:43:57,253 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:43:57,253 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:43:57,253 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-24 09:43:57,253 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-24 09:43:57,253 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:43:57,254 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:43:57,254 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:43:57,254 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:43:57,254 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:43:57,254 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:43:57,254 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:43:57,254 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:43:57,254 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:43:57,254 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:43:57,254 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:43:57,254 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:43:57,278 - itsm_solution - INFO - Logging initialized
2025-07-24 09:43:57,278 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:43:57,278 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:43:57,278 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:43:57,278 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:43:57,278 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:43:57,278 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:43:57,278 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:43:57,278 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:43:57,278 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:43:57,279 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:43:57,279 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:43:57,279 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:43:57,279 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:43:57,279 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:43:57,279 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:43:57,279 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:43:57,279 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:43:57,279 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:43:57,279 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:43:57,280 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:43:57,280 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:43:57,280 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:43:57,280 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:43:57,281 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:43:57,281 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:43:57,281 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:43:57,281 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:43:57,281 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:43:57,281 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:43:57,282 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:43:57,282 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:43:57,282 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:43:57,282 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:43:57,282 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:43:57,282 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:43:57,282 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:43:57,282 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:43:57,282 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:43:57,282 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:43:57,282 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:43:57,282 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:43:57,282 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:44:08,487 - itsm_solution - INFO - Logging initialized
2025-07-24 09:44:08,487 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:44:08,487 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:44:08,487 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:44:08,487 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:44:08,487 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:44:08,487 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:44:08,487 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:44:08,487 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:44:08,487 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:44:08,487 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:44:08,487 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:44:08,487 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:44:08,487 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:44:08,487 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:44:08,487 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:44:08,487 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:44:08,487 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:44:08,493 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-24 09:44:08,493 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-24 09:44:08,493 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:44:08,495 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:44:08,495 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:44:08,495 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:44:08,495 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:44:08,495 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:44:08,495 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:44:08,495 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-24 09:44:08,495 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-24 09:44:08,495 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:44:08,496 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:44:08,496 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:44:08,496 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:44:08,496 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:44:08,496 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:44:08,496 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:44:08,497 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-24 09:44:08,497 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-24 09:44:08,497 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:44:08,497 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:44:08,497 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:44:08,497 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:44:08,497 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:44:08,497 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:44:08,498 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-24 09:44:08,498 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:44:08,498 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:44:08,498 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:44:08,498 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:44:08,498 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-24 09:44:08,498 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:44:08,499 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:44:08,499 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:44:08,499 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-24 09:44:08,499 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-24 09:44:08,499 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:44:08,500 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:44:08,500 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:44:08,500 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:44:08,500 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:44:08,500 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:44:08,500 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:44:08,500 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:44:08,500 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:44:08,500 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:44:08,500 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:44:08,500 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:44:08,528 - itsm_solution - INFO - Logging initialized
2025-07-24 09:44:08,528 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 09:44:08,528 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 09:44:08,528 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 09:44:08,528 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 09:44:08,528 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 09:44:08,528 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 09:44:08,528 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 09:44:08,528 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 09:44:08,528 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 09:44:08,528 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 09:44:08,528 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 09:44:08,528 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 09:44:08,528 - profile_agent - INFO - Adding post-processing callback
2025-07-24 09:44:08,528 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 09:44:08,528 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 09:44:08,528 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 09:44:08,528 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 09:44:08,528 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 09:44:08,529 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 09:44:08,529 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 09:44:08,529 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 09:44:08,529 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 09:44:08,529 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 09:44:08,529 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 09:44:08,529 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 09:44:08,530 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 09:44:08,530 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 09:44:08,530 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 09:44:08,530 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 09:44:08,530 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 09:44:08,531 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 09:44:08,531 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 09:44:08,531 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 09:44:08,531 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 09:44:08,531 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 09:44:08,532 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 09:44:08,532 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 09:44:08,532 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 09:44:08,532 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 09:44:08,532 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 09:44:08,532 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 09:44:08,532 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 09:44:08,532 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 09:44:08,532 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 09:44:08,532 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 09:44:08,532 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 09:44:57,700 - api_server - INFO - Setting Microsoft token in HR auth manager...
2025-07-24 09:44:57,701 - utils.hr_auth - INFO - Microsoft token set from frontend
2025-07-24 09:44:57,702 - api_server - INFO - Microsoft token set in HR auth manager
2025-07-24 09:44:57,702 - utils.hr_auth - INFO - WagonHR token set directly
2025-07-24 09:44:57,702 - api_server - INFO - WagonHR token set in HR auth manager successfully
2025-07-24 09:44:57,702 - api_server - INFO - Setting tenant context: Entity=MOURI Tech Limited (IN-MT-001)
2025-07-24 09:44:57,705 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-24 09:44:57,705 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-24 09:44:57,705 - api_server - INFO - Tenant/entity context set for API clients
2025-07-24 09:44:57,706 - services.session_service - WARNING - Session 12345 not found
2025-07-24 09:44:57,706 - runners.runner_service - WARNING - Session not found: user_id=12345, session_id=12345. Creating new session.
2025-07-24 09:44:57,707 - services.session_service - INFO - Creating session: app=hragent_api, user=12345, session=12345
2025-07-24 09:44:57,707 - services.session_service - INFO - Session created successfully: 12345
2025-07-24 09:44:57,708 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-24 09:44:57,709 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-24 09:44:57,709 - utils.context_manager - INFO - Detected topic: leave_management for message: please apply a leave for me...
2025-07-24 09:44:57,709 - utils.context_manager - INFO - Suggested agent: leave_management_agent for topic: leave_management
2025-07-24 09:44:57,709 - runners.runner_service - INFO - Context switch detected: False
2025-07-24 09:44:57,709 - runners.runner_service - INFO - Routing recommendation: {'suggested_agent': 'leave_management_agent', 'detected_topic': 'leave_management', 'context_switch': False, 'current_topic': 'unknown', 'active_agent': 'hr_root_agent', 'agent_switching_frequency': 0, 'confidence': 0.****************, 'reasoning': 'Detected topic: leave_management; Recommended agent: leave_management_agent'}
2025-07-24 09:44:58,521 - runners.runner_service - ERROR - Error in LLM intent analysis: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 1
}
]
2025-07-24 09:44:58,522 - runners.runner_service - INFO - Detected intent: {'agent': 'leave_management_agent', 'type': 'leave_management', 'confidence': 0.7, 'reason': 'Keyword-based routing due to LLM failure'}
2025-07-24 09:44:58,522 - runners.runner_service - INFO - Routing to leave_management_agent with enhanced prompt
2025-07-24 09:44:58,522 - runners.runner_service - INFO - Routing through root agent to leave_management_agent
2025-07-24 09:44:58,527 - utils.callbacks - INFO - Generated new session ID for audit: 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 09:44:58,528 - utils.callbacks - INFO - Using unknown_user as user ID for audit
2025-07-24 09:44:58,542 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 created for user unknown_user with agent 1
2025-07-24 09:45:01,399 - utils.callbacks - INFO - Starting combined post-processing for agent: hr_root_agent
2025-07-24 09:45:01,400 - utils.callbacks - INFO - Recorded response in session state for agent: hr_root_agent
2025-07-24 09:45:01,400 - utils.context_manager - INFO - Created new context state for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 09:45:01,400 - utils.context_manager - INFO - Detected topic: leave_management for message: Previous conversation context:
No previous convers...
2025-07-24 09:45:01,400 - utils.context_manager - INFO - Recorded conversation turn for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 09:45:01,400 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 09:45:01,400 - utils.callbacks - INFO - Running audit response callback for agent hr_root_agent
2025-07-24 09:45:01,401 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 09:45:01,404 - utils.callbacks - INFO - Completed combined post-processing for agent hr_root_agent
2025-07-24 09:45:01,405 - utils.callbacks - INFO - Returning response: 
2025-07-24 09:45:01,405 - runners.runner_service - INFO - finalizing event
2025-07-24 09:45:01,405 - runners.runner_service - INFO - finalizing event
2025-07-24 09:45:01,408 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-24 09:45:01,409 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 09:45:04,872 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-24 09:45:04,873 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-24 09:45:04,873 - utils.context_manager - INFO - Recorded conversation turn for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 09:45:04,873 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 09:45:04,873 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-24 09:45:04,873 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-24 09:45:04,875 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 09:45:04,884 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-24 09:45:04,884 - utils.callbacks - INFO - Returning response: 
2025-07-24 09:45:04,885 - runners.runner_service - INFO - finalizing event
2025-07-24 09:45:04,887 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 09:45:04,894 - services.audit_service - INFO - Tool request_leave_llm_powered registered for agent 3 with ID 155
2025-07-24 09:45:04,895 - tools.llm_powered_leave_agent - INFO - Processing LLM-powered leave request: 'please apply a leave for me' (session: default)
2025-07-24 09:45:04,895 - tools.llm_powered_leave_agent - ERROR - Error in LLM understanding: No module named 'google.adk.llm_request'
2025-07-24 09:45:04,895 - tools.llm_powered_leave_agent - INFO - LLM-powered processing result: error (confidence: 0.3)
2025-07-24 09:45:04,895 - runners.runner_service - INFO - finalizing event
2025-07-24 09:45:04,898 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-24 09:45:04,899 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 09:45:07,827 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-24 09:45:07,828 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-24 09:45:07,828 - utils.context_manager - INFO - Recorded conversation turn for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 09:45:07,828 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 09:45:07,828 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-24 09:45:07,828 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-24 09:45:07,830 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 09:45:07,838 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-24 09:45:07,838 - utils.callbacks - INFO - Returning response: I'm here to help with your leave request. Could you tell me what you need in your own words?

2025-07-24 09:45:07,839 - runners.runner_service - INFO - finalizing event
2025-07-24 09:45:07,840 - utils.context_manager - INFO - Detected topic: leave_management for message: please apply a leave for me...
2025-07-24 09:45:07,841 - utils.context_manager - INFO - Recorded conversation turn for session 12345
2025-07-24 10:12:19,749 - api_server - INFO - Setting Microsoft token in HR auth manager...
2025-07-24 10:12:19,751 - utils.hr_auth - INFO - Microsoft token set from frontend
2025-07-24 10:12:19,751 - api_server - INFO - Microsoft token set in HR auth manager
2025-07-24 10:12:19,751 - utils.hr_auth - INFO - WagonHR token set directly
2025-07-24 10:12:19,751 - api_server - INFO - WagonHR token set in HR auth manager successfully
2025-07-24 10:12:19,752 - api_server - INFO - Setting tenant context: Entity=MOURI Tech Limited (IN-MT-001)
2025-07-24 10:12:19,752 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-24 10:12:19,752 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-24 10:12:19,752 - api_server - INFO - Tenant/entity context set for API clients
2025-07-24 10:12:19,755 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-24 10:12:19,756 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-24 10:12:19,756 - utils.context_manager - INFO - Detected topic: leave_management for message: please apply a leave for me...
2025-07-24 10:12:19,757 - utils.context_manager - INFO - Suggested agent: leave_management_agent for topic: leave_management
2025-07-24 10:12:19,757 - runners.runner_service - INFO - Context switch detected: False
2025-07-24 10:12:19,757 - runners.runner_service - INFO - Routing recommendation: {'suggested_agent': 'leave_management_agent', 'detected_topic': 'leave_management', 'context_switch': False, 'current_topic': 'unknown', 'active_agent': 'hr_root_agent', 'agent_switching_frequency': 0, 'confidence': 0.****************, 'reasoning': 'Detected topic: leave_management; Recommended agent: leave_management_agent'}
2025-07-24 10:12:20,374 - runners.runner_service - ERROR - Error in LLM intent analysis: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 39
}
]
2025-07-24 10:12:20,375 - runners.runner_service - INFO - Detected intent: {'agent': 'leave_management_agent', 'type': 'leave_management', 'confidence': 0.7, 'reason': 'Keyword-based routing due to LLM failure'}
2025-07-24 10:12:20,375 - runners.runner_service - INFO - Routing to leave_management_agent with enhanced prompt
2025-07-24 10:12:20,375 - runners.runner_service - INFO - Routing through root agent to leave_management_agent
2025-07-24 10:12:20,386 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-24 10:12:20,394 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 10:12:23,579 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-24 10:12:23,579 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-24 10:12:23,579 - utils.context_manager - INFO - Detected topic: leave_management for message: Previous conversation context:
No previous convers...
2025-07-24 10:12:23,579 - utils.context_manager - INFO - Recorded conversation turn for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 10:12:23,579 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 10:12:23,579 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-24 10:12:23,579 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-24 10:12:23,581 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 10:12:23,587 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-24 10:12:23,587 - utils.callbacks - INFO - Returning response: 
2025-07-24 10:12:23,588 - runners.runner_service - INFO - finalizing event
2025-07-24 10:12:23,590 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 10:12:23,594 - services.audit_service - INFO - Tool request_leave_llm_powered registered for agent 3 with ID 156
2025-07-24 10:12:23,595 - tools.llm_powered_leave_agent - INFO - Processing LLM-powered leave request: 'please apply a leave for me' (session: default)
2025-07-24 10:12:23,595 - tools.llm_powered_leave_agent - ERROR - Error in LLM understanding: No module named 'google.adk.llm_request'
2025-07-24 10:12:23,595 - tools.llm_powered_leave_agent - INFO - LLM-powered processing result: error (confidence: 0.3)
2025-07-24 10:12:23,595 - runners.runner_service - INFO - finalizing event
2025-07-24 10:12:23,600 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-24 10:12:23,602 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 10:12:27,181 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-24 10:12:27,181 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-24 10:12:27,181 - utils.context_manager - INFO - Detected topic: leave_management for message: Previous conversation context:
No previous convers...
2025-07-24 10:12:27,181 - utils.context_manager - INFO - Recorded conversation turn for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 10:12:27,181 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 15eec83d-f421-4f8f-b08b-6d1fa447a574
2025-07-24 10:12:27,181 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-24 10:12:27,181 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-24 10:12:27,183 - services.audit_service - INFO - Session 15eec83d-f421-4f8f-b08b-6d1fa447a574 already exists
2025-07-24 10:12:27,188 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-24 10:12:27,188 - utils.callbacks - INFO - Returning response: I'm here to help with your leave request. Could you tell me what you need in your own words?

2025-07-24 10:12:27,188 - runners.runner_service - INFO - finalizing event
2025-07-24 10:12:27,190 - utils.context_manager - INFO - Detected topic: leave_management for message: please apply a leave for me...
2025-07-24 10:12:27,190 - utils.context_manager - INFO - Recorded conversation turn for session 12345
2025-07-24 10:18:03,756 - itsm_solution - INFO - Logging initialized
2025-07-24 10:18:03,756 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 10:18:03,756 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 10:18:03,757 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 10:18:03,757 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 10:18:03,757 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 10:18:03,757 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 10:18:03,757 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 10:18:03,757 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 10:18:03,757 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 10:18:03,757 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 10:18:03,757 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 10:18:03,757 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 10:18:03,757 - profile_agent - INFO - Adding post-processing callback
2025-07-24 10:18:03,757 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 10:18:03,757 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 10:18:03,757 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 10:18:03,757 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 10:18:03,763 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-24 10:18:03,763 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-24 10:18:03,763 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 10:18:03,765 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 10:18:03,765 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 10:18:03,765 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 10:18:03,765 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 10:18:03,765 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 10:18:03,765 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 10:18:03,765 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-24 10:18:03,766 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-24 10:18:03,766 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 10:18:03,766 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 10:18:03,766 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 10:18:03,766 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 10:18:03,766 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 10:18:03,766 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 10:18:03,766 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 10:18:03,766 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-24 10:18:03,766 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-24 10:18:03,766 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 10:18:03,767 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 10:18:03,767 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 10:18:03,767 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 10:18:03,767 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 10:18:03,767 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 10:18:03,767 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 10:18:03,767 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-24 10:18:03,767 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-24 10:18:03,767 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 10:18:03,768 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 10:18:03,768 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 10:18:03,768 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 10:18:03,768 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 10:18:03,768 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 10:18:03,768 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 10:18:03,768 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-24 10:18:03,768 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-24 10:18:03,768 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 10:18:03,768 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 10:18:03,768 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 10:18:03,768 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 10:18:03,768 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 10:18:03,768 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 10:18:03,768 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 10:18:03,769 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-24 10:18:03,769 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-24 10:18:03,769 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 10:18:03,769 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 10:18:03,769 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 10:18:03,769 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 10:18:03,769 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 10:18:03,769 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 10:18:03,769 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 10:18:03,769 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 10:18:03,769 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 10:18:03,769 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 10:18:03,769 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 10:18:03,769 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 10:18:03,794 - itsm_solution - INFO - Logging initialized
2025-07-24 10:18:03,794 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 10:18:03,794 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 10:18:03,794 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 10:18:03,794 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 10:18:03,794 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 10:18:03,794 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 10:18:03,794 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 10:18:03,794 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 10:18:03,794 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 10:18:03,794 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 10:18:03,794 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 10:18:03,794 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 10:18:03,794 - profile_agent - INFO - Adding post-processing callback
2025-07-24 10:18:03,794 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 10:18:03,794 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 10:18:03,794 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 10:18:03,794 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 10:18:03,794 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 10:18:03,795 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 10:18:03,795 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 10:18:03,795 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 10:18:03,795 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 10:18:03,795 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 10:18:03,795 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 10:18:03,795 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 10:18:03,796 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 10:18:03,796 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 10:18:03,796 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 10:18:03,796 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 10:18:03,796 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 10:18:03,796 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 10:18:03,796 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 10:18:03,796 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 10:18:03,796 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 10:18:03,796 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 10:18:03,796 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 10:18:03,796 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 10:18:03,796 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 10:18:03,796 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 10:18:03,797 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 10:18:03,797 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 10:18:03,797 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 10:18:03,797 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 10:18:03,797 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 10:18:03,797 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 10:18:03,797 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 10:18:03,797 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 10:18:03,797 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 10:18:03,797 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 10:18:03,797 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 10:18:03,797 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 10:18:03,797 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 10:18:03,797 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 10:18:03,798 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 10:18:03,798 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 10:18:03,798 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 10:18:03,798 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 10:18:03,798 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 10:18:03,798 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 10:18:03,798 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 10:18:03,798 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 10:18:03,798 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 10:18:03,798 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 10:18:03,798 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
