"""
Context-Aware Conversation State Manager.

This module provides sophisticated conversation state tracking that maintains
context across multiple interactions, enabling natural dialogue flow and
preventing repetitive questions.
"""

import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

# Set up logger
logger = logging.getLogger(__name__)


class ConversationStage(Enum):
    """Enumeration of conversation stages."""
    INITIAL = "initial"
    GATHERING_INFO = "gathering_info"
    CLARIFYING_DETAILS = "clarifying_details"
    READY_TO_PROCESS = "ready_to_process"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR_RECOVERY = "error_recovery"


@dataclass
class UserPreferences:
    """User preferences and communication style."""
    communication_style: str = "neutral"  # casual, formal, neutral
    preferred_date_format: str = "natural"  # natural, iso, relative
    typical_leave_types: List[str] = None
    timezone: str = "UTC"
    language_preference: str = "en"
    
    def __post_init__(self):
        if self.typical_leave_types is None:
            self.typical_leave_types = []


@dataclass
class ExtractedLeaveInfo:
    """Structured leave information extracted from conversations."""
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    leave_type: Optional[str] = None
    reason: Optional[str] = None
    duration_days: Optional[int] = None
    half_day: bool = False
    employee_id: Optional[str] = None
    urgency: str = "normal"  # low, normal, high, urgent
    confidence_score: float = 0.0
    extraction_method: str = "llm"  # llm, pattern, inference
    
    def is_complete(self) -> bool:
        """Check if we have minimum required information."""
        return bool(self.start_date and self.leave_type and self.reason)
    
    def get_missing_fields(self) -> List[str]:
        """Get list of missing required fields."""
        missing = []
        if not self.start_date:
            missing.append("dates")
        if not self.leave_type:
            missing.append("leave_type")
        if not self.reason:
            missing.append("reason")
        return missing
    
    def merge_with(self, other_info: Dict[str, Any]) -> 'ExtractedLeaveInfo':
        """Merge with additional information, keeping existing values when possible."""
        for key, value in other_info.items():
            if hasattr(self, key) and value is not None:
                # Only update if current value is None or new value has higher confidence
                current_value = getattr(self, key)
                if current_value is None or (key == "confidence_score" and value > current_value):
                    setattr(self, key, value)
        return self


class ConversationStateManager:
    """
    Advanced conversation state manager that tracks context across interactions.
    
    This manager maintains conversation history, extracted information, user preferences,
    and conversation flow to enable natural, context-aware dialogue.
    """
    
    def __init__(self, session_id: str, max_history_length: int = 20):
        """Initialize conversation state manager."""
        self.session_id = session_id
        self.max_history_length = max_history_length
        
        # Conversation tracking
        self.conversation_history: List[Dict[str, Any]] = []
        self.stage = ConversationStage.INITIAL
        self.turn_count = 0
        
        # Information tracking
        self.extracted_info = ExtractedLeaveInfo()
        self.user_preferences = UserPreferences()
        self.context_memory: Dict[str, Any] = {}
        
        # Conversation flow
        self.last_question_asked = None
        self.clarification_attempts = 0
        self.max_clarification_attempts = 3
        self.conversation_start_time = datetime.now()
        
        # LLM insights
        self.llm_insights: List[Dict[str, Any]] = []
        self.confidence_trend: List[float] = []
        
    def add_user_turn(
        self,
        message: str,
        llm_analysis: Optional[Dict[str, Any]] = None
    ) -> None:
        """Add a user turn to the conversation."""
        self.turn_count += 1
        
        turn_data = {
            "turn": self.turn_count,
            "role": "user",
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "stage": self.stage.value,
            "llm_analysis": llm_analysis
        }
        
        self.conversation_history.append(turn_data)
        
        # Update extracted information if LLM provided insights
        if llm_analysis and llm_analysis.get("extracted_info"):
            self.update_extracted_info(llm_analysis["extracted_info"])
            
        # Update user preferences based on communication style
        self._update_user_preferences(message, llm_analysis)
        
        # Trim history if too long
        self._trim_history()
        
        logger.info(f"Added user turn {self.turn_count} to session {self.session_id}")
    
    def add_agent_turn(
        self,
        message: str,
        action_taken: str,
        extracted_data: Optional[Dict[str, Any]] = None,
        confidence: float = 0.0
    ) -> None:
        """Add an agent turn to the conversation."""
        turn_data = {
            "turn": self.turn_count,
            "role": "agent",
            "message": message,
            "action_taken": action_taken,
            "extracted_data": extracted_data,
            "confidence": confidence,
            "timestamp": datetime.now().isoformat(),
            "stage": self.stage.value
        }
        
        self.conversation_history.append(turn_data)
        
        # Track confidence trend
        self.confidence_trend.append(confidence)
        if len(self.confidence_trend) > 10:  # Keep last 10 confidence scores
            self.confidence_trend.pop(0)
            
        # Update stage based on action
        self._update_conversation_stage(action_taken)
        
        logger.info(f"Added agent turn {self.turn_count} to session {self.session_id} (action: {action_taken})")
    
    def update_extracted_info(self, new_info: Dict[str, Any]) -> None:
        """Update extracted information with new data."""
        # Convert dict to ExtractedLeaveInfo if needed
        if isinstance(new_info, dict):
            self.extracted_info = self.extracted_info.merge_with(new_info)
        
        # Update conversation stage based on completeness
        if self.extracted_info.is_complete():
            if self.stage in [ConversationStage.INITIAL, ConversationStage.GATHERING_INFO]:
                self.stage = ConversationStage.READY_TO_PROCESS
                
        logger.info(f"Updated extracted info for session {self.session_id}: {self.extracted_info.get_missing_fields()}")
    
    def get_context_for_llm(self) -> str:
        """Generate comprehensive context for LLM processing."""
        context_parts = []
        
        # Session information
        duration = datetime.now() - self.conversation_start_time
        context_parts.append(f"Session Duration: {duration.total_seconds():.0f} seconds")
        context_parts.append(f"Conversation Stage: {self.stage.value}")
        context_parts.append(f"Turn Count: {self.turn_count}")
        
        # Extracted information
        if self.extracted_info:
            info_dict = asdict(self.extracted_info)
            non_null_info = {k: v for k, v in info_dict.items() if v is not None and v != [] and v != ""}
            if non_null_info:
                context_parts.append(f"Information Already Collected: {json.dumps(non_null_info, indent=2)}")
                
            missing_fields = self.extracted_info.get_missing_fields()
            if missing_fields:
                context_parts.append(f"Still Need: {', '.join(missing_fields)}")
        
        # User preferences
        if self.user_preferences.communication_style != "neutral":
            context_parts.append(f"User Communication Style: {self.user_preferences.communication_style}")
            
        # Recent conversation history
        if self.conversation_history:
            context_parts.append("Recent Conversation:")
            recent_turns = self.conversation_history[-6:]  # Last 3 exchanges
            for turn in recent_turns:
                role = turn["role"].title()
                message = turn["message"][:100] + "..." if len(turn["message"]) > 100 else turn["message"]
                context_parts.append(f"  {role}: {message}")
        
        # Conversation insights
        if self.confidence_trend:
            avg_confidence = sum(self.confidence_trend) / len(self.confidence_trend)
            context_parts.append(f"Average Confidence: {avg_confidence:.2f}")
            
        if self.clarification_attempts > 0:
            context_parts.append(f"Clarification Attempts: {self.clarification_attempts}/{self.max_clarification_attempts}")
        
        return "\n".join(context_parts)
    
    def should_ask_clarification(self) -> bool:
        """Determine if we should ask for clarification."""
        return (
            self.clarification_attempts < self.max_clarification_attempts and
            not self.extracted_info.is_complete() and
            self.stage != ConversationStage.ERROR_RECOVERY
        )
    
    def increment_clarification_attempts(self) -> None:
        """Increment clarification attempt counter."""
        self.clarification_attempts += 1
        if self.clarification_attempts >= self.max_clarification_attempts:
            self.stage = ConversationStage.ERROR_RECOVERY
            
    def reset_clarification_attempts(self) -> None:
        """Reset clarification attempts when progress is made."""
        self.clarification_attempts = 0
        
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get a comprehensive summary of the conversation state."""
        return {
            "session_id": self.session_id,
            "stage": self.stage.value,
            "turn_count": self.turn_count,
            "duration_seconds": (datetime.now() - self.conversation_start_time).total_seconds(),
            "extracted_info": asdict(self.extracted_info),
            "user_preferences": asdict(self.user_preferences),
            "clarification_attempts": self.clarification_attempts,
            "confidence_trend": self.confidence_trend,
            "is_complete": self.extracted_info.is_complete(),
            "missing_fields": self.extracted_info.get_missing_fields()
        }
    
    def _update_user_preferences(
        self,
        message: str,
        llm_analysis: Optional[Dict[str, Any]]
    ) -> None:
        """Update user preferences based on message analysis."""
        # Detect communication style
        if llm_analysis and llm_analysis.get("conversation_notes"):
            notes = llm_analysis["conversation_notes"].lower()
            if "formal" in notes:
                self.user_preferences.communication_style = "formal"
            elif "casual" in notes:
                self.user_preferences.communication_style = "casual"
                
        # Detect preferred date formats
        if "tomorrow" in message.lower() or "next" in message.lower():
            self.user_preferences.preferred_date_format = "relative"
        elif any(char.isdigit() for char in message) and "-" in message:
            self.user_preferences.preferred_date_format = "iso"
    
    def _update_conversation_stage(self, action_taken: str) -> None:
        """Update conversation stage based on agent action."""
        if action_taken == "ask_clarification":
            self.stage = ConversationStage.CLARIFYING_DETAILS
        elif action_taken == "process_leave":
            self.stage = ConversationStage.PROCESSING
        elif action_taken in ["handle_help", "handle_cancel_leave", "handle_check_balance"]:
            self.stage = ConversationStage.COMPLETED
        elif action_taken == "error_handling":
            self.stage = ConversationStage.ERROR_RECOVERY
    
    def _trim_history(self) -> None:
        """Trim conversation history to maximum length."""
        if len(self.conversation_history) > self.max_history_length:
            # Keep the first turn and the most recent turns
            first_turn = self.conversation_history[0]
            recent_turns = self.conversation_history[-(self.max_history_length-1):]
            self.conversation_history = [first_turn] + recent_turns


# Global state manager registry
_state_managers: Dict[str, ConversationStateManager] = {}


def get_conversation_state(session_id: str) -> ConversationStateManager:
    """Get or create a conversation state manager for a session."""
    if session_id not in _state_managers:
        _state_managers[session_id] = ConversationStateManager(session_id)
        logger.info(f"Created new conversation state manager for session {session_id}")
    
    return _state_managers[session_id]


def cleanup_old_sessions(max_age_hours: int = 24) -> int:
    """Clean up old conversation sessions."""
    cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
    sessions_to_remove = []
    
    for session_id, manager in _state_managers.items():
        if manager.conversation_start_time < cutoff_time:
            sessions_to_remove.append(session_id)
    
    for session_id in sessions_to_remove:
        del _state_managers[session_id]
        
    logger.info(f"Cleaned up {len(sessions_to_remove)} old conversation sessions")
    return len(sessions_to_remove)
