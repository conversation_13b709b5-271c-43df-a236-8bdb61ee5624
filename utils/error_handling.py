import time
from utils.llm import llm

def handle_error(error_message: str, error_type: str = None) -> dict:
    """
    Handle errors and generate user-friendly messages using LLM.
    
    Args:
        error_message: The original error message
        error_type: Optional error type (e.g., 'service_unavailable', 'rate_limit', 'auth_error')
    
    Returns:
        dict: Error response with user-friendly message
    """
    logger.error(f"Error occurred: {error_message}")
    
    # Create a prompt for the LLM to generate a user-friendly message
    error_prompt = f"""Given the following error:
    Error Type: {error_type if error_type else 'Unknown'}
    Error Message: {error_message}
    
    Generate a user-friendly error message that:
    1. Is conversational and empathetic
    2. Explains the issue in simple terms
    3. Provides clear next steps
    4. Maintains a professional tone
    5. Avoids technical jargon
    
    The message should be concise (1-2 sentences) and end with a clear action item.
    """
    
    try:
        # Use LLM to generate user-friendly message
        response = llm.generate(error_prompt)
        user_friendly_message = response.strip()
    except Exception as e:
        # Fallback message if LLM fails
        user_friendly_message = "I apologize, but I'm having trouble processing your request. Please try again in a moment."
    
    return {
        "type": "error",
        "content": user_friendly_message,
        "is_final": True,
        "timestamp": time.time()
    }

def handle_service_error(error_message: str) -> dict:
    """
    Handle service-related errors with intelligent message generation.
    
    Args:
        error_message: The original error message
    
    Returns:
        dict: Error response with user-friendly message
    """
    # Map common error patterns to error types
    error_patterns = {
        "503": "service_unavailable",
        "429": "rate_limit",
        "401": "auth_error",
        "403": "auth_error",
        "404": "not_found",
        "500": "server_error",
        "502": "bad_gateway",
        "504": "gateway_timeout"
    }
    
    # Determine error type based on error message
    error_type = None
    for pattern, type_name in error_patterns.items():
        if pattern in error_message:
            error_type = type_name
            break
    
    return handle_error(error_message, error_type)

# Example usage in your main code:
try:
    # Your existing code
    pass
except Exception as e:
    error_message = str(e)
    error_response = handle_service_error(error_message)
    yield error_response 