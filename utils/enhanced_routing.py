"""
Enhanced Routing Service for HR Solution.

This module provides intelligent agent routing capabilities with context awareness,
seamless agent switching, and conversation continuity management.
"""

import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from .context_manager import context_manager, AgentType, TopicType

# Set up logger
logger = logging.getLogger(__name__)


@dataclass
class RoutingDecision:
    """Represents a routing decision."""
    target_agent: str
    confidence: float
    reasoning: str
    context_switch: bool
    previous_agent: Optional[str] = None
    topic: str = "unknown"
    requires_handoff: bool = False
    handoff_message: Optional[str] = None


class EnhancedRoutingService:
    """Enhanced routing service with context awareness."""
    
    def __init__(self):
        """Initialize the routing service."""
        self.routing_rules = {
            # Profile-related queries
            "profile": {
                "agent": "profile_agent",
                "keywords": [
                    "profile", "my details", "employee info", "personal information",
                    "contact details", "employee id", "department", "manager",
                    "reporting manager", "work profile", "job title", "position",
                    "my information", "employee details", "show my profile"
                ],
                "confidence_boost": 0.2
            },
            
            # Team-related queries (handled by profile agent)
            "team": {
                "agent": "profile_agent",
                "keywords": [
                    "team", "team members", "my team", "direct reports", "subordinates",
                    "team structure", "team details", "team information", "show team",
                    "who are in my team", "get team information"
                ],
                "confidence_boost": 0.25
            },
            
            # Leave management queries
            "leave": {
                "agent": "leave_management_agent",
                "keywords": [
                    "leave", "vacation", "holiday", "time off", "pto", "sick leave",
                    "annual leave", "leave balance", "leave request", "leave history",
                    "leave approval", "leave cancellation", "comp off", "apply leave",
                    "check leave balance", "my leave balance"
                ],
                "confidence_boost": 0.3
            },
            
            # Attendance queries
            "attendance": {
                "agent": "attendance_management_agent",
                "keywords": [
                    "attendance", "check in", "check out", "present", "absent",
                    "attendance report", "attendance summary", "late", "early",
                    "mark attendance", "attendance record"
                ],
                "confidence_boost": 0.25
            },
            
            # Policy queries
            "policy": {
                "agent": "policy_agent",
                "keywords": [
                    "policy", "policies", "hr policy", "company policy", "guidelines",
                    "rules", "procedures", "handbook", "compliance", "what is the policy"
                ],
                "confidence_boost": 0.2
            },
            
            # General HR queries
            "general_hr": {
                "agent": "hr_service_desk_agent",
                "keywords": [
                    "help", "support", "issue", "problem", "complaint", "general",
                    "hr support", "human resources", "assistance"
                ],
                "confidence_boost": 0.1
            }
        }
        
        logger.info("Enhanced Routing Service initialized")
    
    def route_message(
        self, 
        session_id: str, 
        message: str, 
        current_agent: Optional[str] = None
    ) -> RoutingDecision:
        """Route a message to the appropriate agent."""
        
        # Get context-aware routing recommendation
        recommendation = context_manager.get_routing_recommendation(session_id, message)
        
        # Analyze message for routing clues
        message_analysis = self._analyze_message(message)
        
        # Determine target agent
        target_agent = self._determine_target_agent(
            recommendation, 
            message_analysis, 
            current_agent
        )
        
        # Calculate confidence
        confidence = self._calculate_routing_confidence(
            recommendation, 
            message_analysis, 
            target_agent
        )
        
        # Generate reasoning
        reasoning = self._generate_routing_reasoning(
            recommendation, 
            message_analysis, 
            target_agent
        )
        
        # Check if handoff is required
        requires_handoff = current_agent and current_agent != target_agent
        handoff_message = self._generate_handoff_message(
            current_agent, 
            target_agent, 
            message
        ) if requires_handoff else None
        
        # Create routing decision
        decision = RoutingDecision(
            target_agent=target_agent,
            confidence=confidence,
            reasoning=reasoning,
            context_switch=recommendation.get("context_switch", False),
            previous_agent=current_agent,
            topic=recommendation.get("detected_topic", "unknown"),
            requires_handoff=requires_handoff,
            handoff_message=handoff_message
        )
        
        logger.info(f"Routing decision: {target_agent} (confidence: {confidence:.2f})")
        return decision
    
    def _analyze_message(self, message: str) -> Dict[str, Any]:
        """Analyze message for routing clues."""
        message_lower = message.lower()
        
        # Score each category
        category_scores = {}
        for category, config in self.routing_rules.items():
            score = 0
            matched_keywords = []
            
            for keyword in config["keywords"]:
                if keyword in message_lower:
                    score += 1
                    matched_keywords.append(keyword)
            
            if score > 0:
                category_scores[category] = {
                    "score": score,
                    "matched_keywords": matched_keywords,
                    "agent": config["agent"],
                    "confidence_boost": config["confidence_boost"]
                }
        
        # Find best match
        best_category = None
        best_score = 0
        
        if category_scores:
            best_category = max(category_scores, key=lambda x: category_scores[x]["score"])
            best_score = category_scores[best_category]["score"]
        
        return {
            "category_scores": category_scores,
            "best_category": best_category,
            "best_score": best_score,
            "message_length": len(message),
            "has_question": "?" in message
        }
    
    def _determine_target_agent(
        self, 
        recommendation: Dict[str, Any], 
        message_analysis: Dict[str, Any], 
        current_agent: Optional[str]
    ) -> str:
        """Determine the target agent based on analysis."""
        
        # Priority 1: Strong keyword match
        if (message_analysis["best_category"] and 
            message_analysis["best_score"] >= 2):
            return message_analysis["category_scores"][message_analysis["best_category"]]["agent"]
        
        # Priority 2: Context manager recommendation with high confidence
        if recommendation.get("confidence", 0) >= 0.8:
            return recommendation["suggested_agent"]
        
        # Priority 3: Single keyword match with context support
        if (message_analysis["best_category"] and 
            recommendation.get("detected_topic") != "unknown"):
            return message_analysis["category_scores"][message_analysis["best_category"]]["agent"]
        
        # Priority 4: Context manager recommendation
        if recommendation.get("suggested_agent") != "hr_root_agent":
            return recommendation["suggested_agent"]
        
        # Priority 5: Stay with current agent if no clear direction
        if current_agent and current_agent != "hr_root_agent":
            return current_agent
        
        # Default: Root agent
        return "hr_root_agent"
    
    def _calculate_routing_confidence(
        self, 
        recommendation: Dict[str, Any], 
        message_analysis: Dict[str, Any], 
        target_agent: str
    ) -> float:
        """Calculate confidence in routing decision."""
        base_confidence = 0.5
        
        # Boost for keyword matches
        if message_analysis["best_category"]:
            keyword_score = message_analysis["best_score"]
            confidence_boost = message_analysis["category_scores"][message_analysis["best_category"]]["confidence_boost"]
            base_confidence += min(keyword_score * confidence_boost, 0.4)
        
        # Boost for context manager confidence
        context_confidence = recommendation.get("confidence", 0)
        base_confidence += context_confidence * 0.3
        
        # Boost for consistency with recommendation
        if recommendation.get("suggested_agent") == target_agent:
            base_confidence += 0.1
        
        # Penalty for context switches
        if recommendation.get("context_switch", False):
            base_confidence -= 0.1
        
        return min(max(base_confidence, 0.1), 1.0)
    
    def _generate_routing_reasoning(
        self, 
        recommendation: Dict[str, Any], 
        message_analysis: Dict[str, Any], 
        target_agent: str
    ) -> str:
        """Generate reasoning for routing decision."""
        reasoning_parts = []
        
        # Add keyword analysis
        if message_analysis["best_category"]:
            category = message_analysis["best_category"]
            score = message_analysis["best_score"]
            keywords = message_analysis["category_scores"][category]["matched_keywords"]
            reasoning_parts.append(f"Matched {score} keywords for {category}: {', '.join(keywords[:3])}")
        
        # Add context information
        if recommendation.get("detected_topic") != "unknown":
            reasoning_parts.append(f"Detected topic: {recommendation['detected_topic']}")
        
        # Add context switch information
        if recommendation.get("context_switch", False):
            reasoning_parts.append("Context switch detected")
        
        # Add agent information
        reasoning_parts.append(f"Routing to: {target_agent}")
        
        return "; ".join(reasoning_parts)
    
    def _generate_handoff_message(
        self, 
        from_agent: Optional[str], 
        to_agent: str, 
        user_message: str
    ) -> Optional[str]:
        """Generate handoff message for agent transitions."""
        if not from_agent or from_agent == to_agent:
            return None
        
        agent_names = {
            "hr_root_agent": "HR Assistant",
            "profile_agent": "Profile Agent",
            "leave_management_agent": "Leave Management Agent",
            "attendance_management_agent": "Attendance Management Agent",
            "policy_agent": "Policy Agent",
            "hr_service_desk_agent": "HR Service Desk Agent"
        }
        
        from_name = agent_names.get(from_agent, from_agent)
        to_name = agent_names.get(to_agent, to_agent)
        
        return f"Transferring your request from {from_name} to {to_name} for specialized assistance."


# Global routing service instance
routing_service = EnhancedRoutingService()
