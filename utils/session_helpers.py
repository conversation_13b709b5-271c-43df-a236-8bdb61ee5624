"""
Session state helper utilities.

This module provides helper functions for working with session state
following Google ADK standards for proper state management.
"""

import logging
from typing import Any, Optional, Dict
import time

# Set up logger
logger = logging.getLogger(__name__)

# ADK State Management Warning
_ADK_WARNING_SHOWN = False

def _show_adk_warning():
    """Show warning about direct state modification."""
    global _ADK_WARNING_SHOWN
    if not _ADK_WARNING_SHOWN:
        logger.warning(
            "Direct session state modification detected. "
            "Consider using CallbackContext.state or ToolContext.state "
            "for proper ADK state management patterns."
        )
        _ADK_WARNING_SHOWN = True

def set_session_value_via_context(context, key: str, value: Any) -> None:
    """Store a value in session state via ADK context (recommended).

    Args:
        context: CallbackContext or ToolContext object
        key: The key to store the value under (use ADK prefixes: user:, app:, temp:)
        value: The value to store
    """
    try:
        # Validate key follows ADK prefix standards
        validated_key = validate_state_key(key)
        context.state[validated_key] = value
        logger.info(f"Stored value in session state via context: {validated_key}")
    except Exception as e:
        logger.error(f"Failed to store value in session state via context: {e}")

def set_session_value(session, key: str, value: Any) -> None:
    """Store a value in the session state (legacy method).

    WARNING: This method directly modifies session state which violates ADK best practices.
    Use set_session_value_via_context() with CallbackContext/ToolContext instead.

    Args:
        session: The current session object
        key: The key to store the value under
        value: The value to store
    """
    _show_adk_warning()
    try:
        validated_key = validate_state_key(key)
        session.state[validated_key] = value
        logger.info(f"Stored value in session state (legacy): {validated_key}")
    except Exception as e:
        logger.error(f"Failed to store value in session state: {e}")

def get_session_value(session, key: str, default: Any = None) -> Any:
    """Retrieve a value from the session state.

    Args:
        session: The current session object
        key: The key to retrieve
        default: Default value to return if key doesn't exist

    Returns:
        The stored value or the default if not found
    """
    try:
        value = session.state.get(key, default)
        if value != default:
            logger.debug(f"Retrieved value from session state: {key}")
        return value
    except Exception as e:
        logger.error(f"Failed to retrieve value from session state: {e}")
        return default

def validate_state_key(key: str) -> str:
    """Validate and potentially modify state key to follow ADK standards.

    Args:
        key: The state key to validate

    Returns:
        Validated key with proper ADK prefix if needed
    """
    # Check if key already has proper ADK prefix
    if key.startswith(('user:', 'app:', 'temp:')):
        return key

    # Special session-scoped keys
    if key in ['session_metadata']:
        return key

    # For backward compatibility, warn but don't modify
    if not key.startswith(('user:', 'app:', 'temp:')):
        logger.warning(
            f"State key '{key}' should use ADK prefixes: "
            "'user:' for user-scoped, 'app:' for app-scoped, 'temp:' for temporary data"
        )

    return key

def create_state_update_event(
    state_delta: Dict[str, Any],
    author: str = "system"
) -> Dict[str, Any]:
    """Create an Event for state updates following ADK standards.

    Args:
        state_delta: The state changes to apply
        author: The author of the state change

    Returns:
        Event dictionary with proper structure for state update
    """
    # Validate all keys in state_delta
    validated_delta = {validate_state_key(k): v for k, v in state_delta.items()}

    return {
        "invocation_id": f"state_update_{int(time.time())}",
        "author": author,
        "state_delta": validated_delta,
        "timestamp": time.time()
    }

def get_user_state(session, key: str = None) -> Any:
    """Retrieve user-scoped state values.

    Args:
        session: The current session object
        key: Optional specific user state key (without 'user:' prefix)

    Returns:
        The user state value(s)
    """
    try:
        if key:
            return session.state.get(f"user:{key}")

        # Return all user-scoped state
        return {k[5:]: v for k, v in session.state.items() if k.startswith('user:')}
    except Exception as e:
        logger.error(f"Failed to retrieve user state: {e}")
        return {} if not key else None

def get_app_state(session, key: str = None) -> Any:
    """Retrieve app-scoped state values.

    Args:
        session: The current session object
        key: Optional specific app state key (without 'app:' prefix)

    Returns:
        The app state value(s)
    """
    try:
        if key:
            return session.state.get(f"app:{key}")

        # Return all app-scoped state
        return {k[4:]: v for k, v in session.state.items() if k.startswith('app:')}
    except Exception as e:
        logger.error(f"Failed to retrieve app state: {e}")
        return {} if not key else None