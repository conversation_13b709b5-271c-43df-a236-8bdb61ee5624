"""
Intelligent Date Parser Utility.

This module provides intelligent parsing of natural language date expressions
for HR applications, converting phrases like "next Tuesday" or "coming Friday"
into proper date formats.
"""

import logging
import re
from datetime import datetime, timedelta
from typing import Optional, Tuple, Dict, Any

# Set up logger
logger = logging.getLogger(__name__)

# Day name mappings
WEEKDAYS = {
    'monday': 0, 'mon': 0,
    'tuesday': 1, 'tue': 1, 'tues': 1,
    'wednesday': 2, 'wed': 2,
    'thursday': 3, 'thu': 3, 'thur': 3, 'thurs': 3,
    'friday': 4, 'fri': 4,
    'saturday': 5, 'sat': 5,
    'sunday': 6, 'sun': 6
}

# Month name mappings
MONTHS = {
    'january': 1, 'jan': 1,
    'february': 2, 'feb': 2,
    'march': 3, 'mar': 3,
    'april': 4, 'apr': 4,
    'may': 5,
    'june': 6, 'jun': 6,
    'july': 7, 'jul': 7,
    'august': 8, 'aug': 8,
    'september': 9, 'sep': 9, 'sept': 9,
    'october': 10, 'oct': 10,
    'november': 11, 'nov': 11,
    'december': 12, 'dec': 12
}

# Relative date keywords
RELATIVE_KEYWORDS = {
    'today': 0,
    'tomorrow': 1,
    'yesterday': -1,
    'next': 'next',
    'coming': 'next',
    'this': 'this',
    'last': 'last',
    'previous': 'last'
}

class IntelligentDateParser:
    """Intelligent date parser for natural language date expressions."""
    
    def __init__(self):
        """Initialize the date parser."""
        self.today = datetime.now().date()
        
    def parse_date_expression(self, date_text: str) -> Dict[str, Any]:
        """
        Parse a natural language date expression into a structured date.
        
        Args:
            date_text: Natural language date expression
            
        Returns:
            Dictionary containing parsed date information
        """
        try:
            logger.info(f"Parsing date expression: '{date_text}'")
            
            # Clean and normalize the input
            normalized_text = self._normalize_text(date_text)
            
            # Try different parsing strategies
            result = None
            
            # Strategy 1: Check for ISO format dates (YYYY-MM-DD)
            result = self._parse_iso_date(normalized_text)
            if result:
                return result
                
            # Strategy 2: Check for relative day expressions (next Tuesday, coming Friday)
            result = self._parse_relative_day(normalized_text)
            if result:
                return result
                
            # Strategy 3: Check for relative date expressions (today, tomorrow)
            result = self._parse_relative_date(normalized_text)
            if result:
                return result
                
            # Strategy 4: Check for specific date formats (Dec 15, 15th December)
            result = self._parse_specific_date(normalized_text)
            if result:
                return result
                
            # Strategy 5: Check for day-only expressions (Tuesday, Friday)
            result = self._parse_day_only(normalized_text)
            if result:
                return result
            
            # If no parsing strategy worked, return error
            logger.warning(f"Could not parse date expression: '{date_text}'")
            return {
                "success": False,
                "error": f"Could not understand date expression: '{date_text}'",
                "suggestion": "Please use formats like 'next Tuesday', 'tomorrow', '2024-01-15', or 'Dec 15'"
            }
            
        except Exception as e:
            logger.error(f"Error parsing date expression '{date_text}': {e}")
            return {
                "success": False,
                "error": f"Error parsing date: {str(e)}"
            }
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for parsing."""
        # Convert to lowercase and remove extra spaces
        normalized = re.sub(r'\s+', ' ', text.lower().strip())
        
        # Remove common punctuation
        normalized = re.sub(r'[,.]', '', normalized)
        
        return normalized
    
    def _parse_iso_date(self, text: str) -> Optional[Dict[str, Any]]:
        """Parse ISO format dates (YYYY-MM-DD)."""
        iso_pattern = r'\b(\d{4})-(\d{1,2})-(\d{1,2})\b'
        match = re.search(iso_pattern, text)
        
        if match:
            try:
                year, month, day = map(int, match.groups())
                parsed_date = datetime(year, month, day).date()
                
                return {
                    "success": True,
                    "date": parsed_date.strftime("%Y-%m-%d"),
                    "parsed_date": parsed_date,
                    "method": "iso_format",
                    "original_text": text
                }
            except ValueError as e:
                logger.warning(f"Invalid ISO date in '{text}': {e}")
                
        return None
    
    def _parse_relative_day(self, text: str) -> Optional[Dict[str, Any]]:
        """Parse relative day expressions (next Tuesday, coming Friday, this Monday)."""
        # Pattern: (next|coming|this|last) (day_name)
        pattern = r'\b(next|coming|this|last|previous)\s+(' + '|'.join(WEEKDAYS.keys()) + r')\b'
        match = re.search(pattern, text)
        
        if match:
            modifier, day_name = match.groups()
            target_weekday = WEEKDAYS[day_name]
            current_weekday = self.today.weekday()
            
            if modifier in ['next', 'coming']:
                # Find the next occurrence of this weekday
                days_ahead = target_weekday - current_weekday
                if days_ahead <= 0:  # Target day already passed this week
                    days_ahead += 7
                target_date = self.today + timedelta(days=days_ahead)
                
            elif modifier == 'this':
                # Find this week's occurrence
                days_ahead = target_weekday - current_weekday
                if days_ahead < 0:  # Day already passed this week, assume next week
                    days_ahead += 7
                target_date = self.today + timedelta(days=days_ahead)
                
            elif modifier in ['last', 'previous']:
                # Find the previous occurrence of this weekday
                days_back = current_weekday - target_weekday
                if days_back <= 0:  # Target day hasn't occurred this week
                    days_back += 7
                target_date = self.today - timedelta(days=days_back)
            
            return {
                "success": True,
                "date": target_date.strftime("%Y-%m-%d"),
                "parsed_date": target_date,
                "method": "relative_day",
                "original_text": text,
                "interpreted_as": f"{modifier} {day_name.title()}"
            }
            
        return None
    
    def _parse_relative_date(self, text: str) -> Optional[Dict[str, Any]]:
        """Parse relative date expressions (today, tomorrow, yesterday)."""
        for keyword, offset in RELATIVE_KEYWORDS.items():
            if isinstance(offset, int) and keyword in text:
                target_date = self.today + timedelta(days=offset)
                
                return {
                    "success": True,
                    "date": target_date.strftime("%Y-%m-%d"),
                    "parsed_date": target_date,
                    "method": "relative_date",
                    "original_text": text,
                    "interpreted_as": keyword.title()
                }
                
        return None
    
    def _parse_specific_date(self, text: str) -> Optional[Dict[str, Any]]:
        """Parse specific date formats (Dec 15, 15th December, January 20th)."""
        # Pattern: (month) (day) or (day) (month)
        month_day_pattern = r'\b(' + '|'.join(MONTHS.keys()) + r')\s+(\d{1,2})(?:st|nd|rd|th)?\b'
        day_month_pattern = r'\b(\d{1,2})(?:st|nd|rd|th)?\s+(' + '|'.join(MONTHS.keys()) + r')\b'
        
        # Try month-day pattern
        match = re.search(month_day_pattern, text)
        if match:
            month_name, day = match.groups()
            month = MONTHS[month_name]
            day = int(day)
            
            # Assume current year if not specified
            year = self.today.year
            
            try:
                target_date = datetime(year, month, day).date()
                
                # If the date is in the past, assume next year
                if target_date < self.today:
                    target_date = datetime(year + 1, month, day).date()
                
                return {
                    "success": True,
                    "date": target_date.strftime("%Y-%m-%d"),
                    "parsed_date": target_date,
                    "method": "specific_date",
                    "original_text": text,
                    "interpreted_as": f"{month_name.title()} {day}, {target_date.year}"
                }
            except ValueError as e:
                logger.warning(f"Invalid specific date in '{text}': {e}")
        
        # Try day-month pattern
        match = re.search(day_month_pattern, text)
        if match:
            day, month_name = match.groups()
            month = MONTHS[month_name]
            day = int(day)
            
            # Assume current year if not specified
            year = self.today.year
            
            try:
                target_date = datetime(year, month, day).date()
                
                # If the date is in the past, assume next year
                if target_date < self.today:
                    target_date = datetime(year + 1, month, day).date()
                
                return {
                    "success": True,
                    "date": target_date.strftime("%Y-%m-%d"),
                    "parsed_date": target_date,
                    "method": "specific_date",
                    "original_text": text,
                    "interpreted_as": f"{day} {month_name.title()}, {target_date.year}"
                }
            except ValueError as e:
                logger.warning(f"Invalid specific date in '{text}': {e}")
                
        return None
    
    def _parse_day_only(self, text: str) -> Optional[Dict[str, Any]]:
        """Parse day-only expressions (Tuesday, Friday) - assume next occurrence."""
        for day_name, weekday in WEEKDAYS.items():
            if day_name in text:
                current_weekday = self.today.weekday()
                
                # Find the next occurrence of this weekday
                days_ahead = weekday - current_weekday
                if days_ahead <= 0:  # Target day already passed this week
                    days_ahead += 7
                    
                target_date = self.today + timedelta(days=days_ahead)
                
                return {
                    "success": True,
                    "date": target_date.strftime("%Y-%m-%d"),
                    "parsed_date": target_date,
                    "method": "day_only",
                    "original_text": text,
                    "interpreted_as": f"Next {day_name.title()}"
                }
                
        return None

# Global instance for easy access
date_parser = IntelligentDateParser()

def parse_natural_date(date_text: str) -> Dict[str, Any]:
    """
    Parse a natural language date expression.
    
    Args:
        date_text: Natural language date expression
        
    Returns:
        Dictionary containing parsed date information
    """
    return date_parser.parse_date_expression(date_text)

def extract_dates_from_text(text: str) -> Dict[str, Any]:
    """
    Extract and parse date expressions from a text string.
    
    Args:
        text: Text containing date expressions
        
    Returns:
        Dictionary containing extracted dates
    """
    # Common date-related patterns in leave requests
    date_patterns = [
        r'\b(?:on|for|from|starting|beginning)\s+([^,.\n]+?)(?:\s+(?:to|until|through|and|,)|\s*$)',
        r'\b(?:next|coming|this|last)\s+\w+',
        r'\b(?:today|tomorrow|yesterday)\b',
        r'\b\w+day\b',  # Any day ending in 'day'
        r'\b\d{4}-\d{1,2}-\d{1,2}\b',  # ISO dates
        r'\b\w+\s+\d{1,2}(?:st|nd|rd|th)?\b',  # Month day
        r'\b\d{1,2}(?:st|nd|rd|th)?\s+\w+\b'   # Day month
    ]
    
    extracted_dates = []
    
    for pattern in date_patterns:
        matches = re.finditer(pattern, text, re.IGNORECASE)
        for match in matches:
            date_text = match.group(1) if match.groups() else match.group(0)
            parsed = parse_natural_date(date_text.strip())
            if parsed.get("success"):
                extracted_dates.append(parsed)
    
    return {
        "success": len(extracted_dates) > 0,
        "dates": extracted_dates,
        "count": len(extracted_dates)
    }
