"""
Strict Query Classifier for HR Agents.

This module provides strict query classification to prevent agents from handling
queries outside their domain and ensure proper routing.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple
from enum import Enum

# Set up logger
logger = logging.getLogger(__name__)


class QueryDomain(Enum):
    """Enumeration of query domains."""
    PROFILE = "profile"
    LEAVE_MANAGEMENT = "leave_management"
    ATTENDANCE = "attendance"
    POLICY = "policy"
    GENERAL_HR = "general_hr"
    UNKNOWN = "unknown"


class QueryClassifier:
    """Strict query classifier for HR domains."""
    
    def __init__(self):
        """Initialize the query classifier with domain-specific patterns."""
        
        # Define strict domain patterns with high-confidence keywords
        self.domain_patterns = {
            QueryDomain.PROFILE: {
                "primary_keywords": [
                    "profile", "my profile", "show my profile", "employee details",
                    "personal information", "my information", "employee info",
                    "work profile", "my details", "contact details"
                ],
                "manager_keywords": [
                    "reporting manager", "my manager", "manager information",
                    "who is my manager", "manager details", "supervisor"
                ],
                "team_keywords": [
                    "my team", "team members", "show my team", "team details",
                    "team structure", "direct reports", "subordinates",
                    "who are in my team", "get team information", "team list"
                ],
                "search_keywords": [
                    "employee search", "find employee", "search employee",
                    "employee directory", "staff directory"
                ]
            },
            
            QueryDomain.LEAVE_MANAGEMENT: {
                "balance_keywords": [
                    "leave balance", "available leave", "remaining leave",
                    "leave days", "how many days", "check leave", "leave quota"
                ],
                "request_keywords": [
                    "apply leave", "request leave", "submit leave", "take leave",
                    "need leave", "want leave", "leave application", "book leave",
                    "apply for leave", "apply for vacation", "request for leave"
                ],
                "history_keywords": [
                    "leave history", "past leaves", "previous leaves", "taken leaves",
                    "leave records", "leave report"
                ],
                "pending_keywords": [
                    "pending leaves", "pending approvals", "waiting approval",
                    "leave status", "approval status"
                ],
                "team_leave_keywords": [
                    "team leaves", "team leave balance", "team pending leaves",
                    "team leave overview", "team leave approvals"
                ],
                "modify_keywords": [
                    "cancel leave", "modify leave", "change leave", "update leave",
                    "withdraw leave", "edit leave"
                ]
            },
            
            QueryDomain.ATTENDANCE: {
                "report_keywords": [
                    "attendance report", "attendance summary", "attendance record",
                    "attendance history", "my attendance"
                ],
                "tracking_keywords": [
                    "record attendance", "mark attendance", "clock in", "clock out",
                    "check in", "check out", "time tracking"
                ],
                "anomaly_keywords": [
                    "attendance anomalies", "attendance issues", "missing attendance"
                ]
            },
            
            QueryDomain.POLICY: {
                "policy_keywords": [
                    "company policy", "hr policy", "policy information", "policies",
                    "policy document", "policy details", "policy guidelines"
                ],
                "procedure_keywords": [
                    "procedures", "guidelines", "rules", "regulations",
                    "process", "workflow", "standard operating procedure",
                    "procedure for", "process for", "guidelines for"
                ]
            }
        }
        
        # Define exclusion patterns - queries that should NOT be handled by specific domains
        self.exclusion_patterns = {
            QueryDomain.PROFILE: [
                "leave", "vacation", "sick", "attendance", "policy", "procedure"
            ],
            QueryDomain.LEAVE_MANAGEMENT: [
                "profile", "team members", "employee search", "attendance report", 
                "policy", "procedure", "contact details"
            ],
            QueryDomain.ATTENDANCE: [
                "profile", "leave", "vacation", "policy", "team members"
            ],
            QueryDomain.POLICY: [
                "profile", "leave", "attendance", "team members", "employee search"
            ]
        }
    
    def classify_query(self, query: str, agent_domain: QueryDomain) -> Tuple[bool, float, str]:
        """
        Classify if a query belongs to the specified agent domain.
        
        Args:
            query: The user query to classify
            agent_domain: The domain of the agent asking for classification
            
        Returns:
            Tuple of (belongs_to_domain, confidence_score, reasoning)
        """
        query_lower = query.lower().strip()
        
        # Check if query is empty or too short
        if len(query_lower) < 3:
            return False, 0.0, "Query too short to classify"
        
        # Get domain patterns
        if agent_domain not in self.domain_patterns:
            return False, 0.0, f"Unknown agent domain: {agent_domain}"
        
        domain_config = self.domain_patterns[agent_domain]
        exclusion_keywords = self.exclusion_patterns.get(agent_domain, [])
        
        # Check for exclusion keywords first
        exclusion_matches = [kw for kw in exclusion_keywords if kw in query_lower]
        if exclusion_matches:
            return False, 0.9, f"Contains exclusion keywords: {exclusion_matches}"
        
        # Calculate positive matches
        total_matches = 0
        matched_categories = []
        
        for category, keywords in domain_config.items():
            matches = [kw for kw in keywords if kw in query_lower]
            if matches:
                total_matches += len(matches)
                matched_categories.append(f"{category}: {matches}")
        
        # Determine if query belongs to domain
        if total_matches == 0:
            return False, 0.0, "No domain-specific keywords found"
        
        # Calculate confidence based on matches
        confidence = min(0.9, 0.3 + (total_matches * 0.2))
        
        reasoning = f"Matched {total_matches} keywords in categories: {matched_categories}"
        
        return True, confidence, reasoning
    
    def get_suggested_domain(self, query: str) -> Tuple[QueryDomain, float, str]:
        """
        Suggest the most appropriate domain for a query.
        
        Args:
            query: The user query to classify
            
        Returns:
            Tuple of (suggested_domain, confidence_score, reasoning)
        """
        query_lower = query.lower().strip()
        
        if len(query_lower) < 3:
            return QueryDomain.UNKNOWN, 0.0, "Query too short to classify"
        
        domain_scores = {}
        
        # Score each domain
        for domain in [QueryDomain.PROFILE, QueryDomain.LEAVE_MANAGEMENT, 
                      QueryDomain.ATTENDANCE, QueryDomain.POLICY]:
            belongs, confidence, reasoning = self.classify_query(query, domain)
            if belongs:
                domain_scores[domain] = confidence
        
        if not domain_scores:
            return QueryDomain.GENERAL_HR, 0.5, "No specific domain detected, suggesting general HR"
        
        # Return domain with highest confidence
        best_domain = max(domain_scores, key=domain_scores.get)
        best_confidence = domain_scores[best_domain]
        
        return best_domain, best_confidence, f"Best match for {best_domain.value} domain"
    
    def should_transfer_query(self, query: str, current_agent_domain: QueryDomain) -> Tuple[bool, Optional[str], str]:
        """
        Determine if a query should be transferred to another agent.
        
        Args:
            query: The user query
            current_agent_domain: The domain of the current agent
            
        Returns:
            Tuple of (should_transfer, target_agent_name, reasoning)
        """
        belongs_to_current, confidence, reasoning = self.classify_query(query, current_agent_domain)
        
        if belongs_to_current and confidence >= 0.6:
            return False, None, f"Query belongs to current domain: {reasoning}"
        
        # Get suggested domain
        suggested_domain, suggested_confidence, suggested_reasoning = self.get_suggested_domain(query)
        
        if suggested_domain == current_agent_domain:
            return False, None, "Query belongs to current domain"
        
        # Map domains to agent names
        domain_to_agent = {
            QueryDomain.PROFILE: "profile_agent",
            QueryDomain.LEAVE_MANAGEMENT: "leave_management_agent",
            QueryDomain.ATTENDANCE: "attendance_management_agent",
            QueryDomain.POLICY: "policy_agent",
            QueryDomain.GENERAL_HR: "hr_service_desk_agent"
        }
        
        target_agent = domain_to_agent.get(suggested_domain)
        if not target_agent:
            return True, "hr_service_desk_agent", f"Unknown domain, routing to general HR: {suggested_reasoning}"
        
        return True, target_agent, f"Should transfer to {suggested_domain.value}: {suggested_reasoning}"


# Global query classifier instance
query_classifier = QueryClassifier()


def check_query_domain(query: str, agent_name: str) -> Dict[str, any]:
    """
    Helper function for agents to check if they should handle a query.

    Args:
        query: The user query
        agent_name: The name of the current agent

    Returns:
        Dictionary with classification results:
        {
            "should_handle": bool,
            "should_transfer": bool,
            "target_agent": str or None,
            "confidence": float,
            "reasoning": str
        }
    """
    # Map agent names to domains
    agent_to_domain = {
        "profile_agent": QueryDomain.PROFILE,
        "leave_management_agent": QueryDomain.LEAVE_MANAGEMENT,
        "attendance_management_agent": QueryDomain.ATTENDANCE,
        "policy_agent": QueryDomain.POLICY,
        "hr_service_desk_agent": QueryDomain.GENERAL_HR
    }

    current_domain = agent_to_domain.get(agent_name)
    if not current_domain:
        logger.warning(f"Unknown agent name: {agent_name}")
        return {
            "should_handle": False,
            "should_transfer": True,
            "target_agent": "hr_service_desk_agent",
            "confidence": 0.8,
            "reasoning": f"Unknown agent {agent_name}, routing to general HR"
        }

    # Check if query belongs to current domain
    belongs_to_current, confidence, reasoning = query_classifier.classify_query(query, current_domain)

    if belongs_to_current and confidence >= 0.6:
        return {
            "should_handle": True,
            "should_transfer": False,
            "target_agent": None,
            "confidence": confidence,
            "reasoning": reasoning
        }

    # Determine where to transfer
    should_transfer, target_agent, transfer_reasoning = query_classifier.should_transfer_query(query, current_domain)

    return {
        "should_handle": False,
        "should_transfer": should_transfer,
        "target_agent": target_agent,
        "confidence": confidence,
        "reasoning": transfer_reasoning
    }
