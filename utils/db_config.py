"""
Database configuration module.

This module contains the database configuration for the ITSM solution.
"""

import os
import logging
from typing import Optional, Dict, Any
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session

# Set up logger
logger = logging.getLogger(__name__)

# Get database URL from environment variables
# Default to SQLite for local testing if no DATABASE_URL is provided
# For production, set DATABASE_URL to your PostgreSQL connection string
# Example: postgresql://username:password@hostname:port/database_name
DB_URL = os.environ.get("DATABASE_URL", "sqlite:///itsm_audit.db")

# For local PostgreSQL testing, uncomment and modify the following line:
# DB_URL = os.environ.get("DATABASE_URL", "postgresql://rahula:postgres@localhost:5432/postgres")

# Define schema name for PostgreSQL
SCHEMA_NAME = "itsm"

# Create engine
logger.info(f"Creating database engine with URL: {DB_URL}")
engine = create_engine(DB_URL, echo=False)
logger.info(f"Database engine created successfull Setting up audit trail for all agentsy")

# Create session factory
session_factory = sessionmaker(bind=engine)
SessionLocal = scoped_session(session_factory)

# Create metadata with schema
metadata = MetaData(schema=SCHEMA_NAME if DB_URL.startswith('postgresql') else None)

# Create base class for declarative models
Base = declarative_base(metadata=metadata)
logger.info(f"Base class for declarative models created successfully")


def get_db_session():
    """Get a database session.

    Returns:
        A database session.
    """
    db = SessionLocal()
    try:
        return db
    finally:
        db.close()


def init_db():
    """Initialize the database.

    Creates the schema (if PostgreSQL) and all tables if they don't exist.
    """
    try:
        # Create schema if using PostgreSQL
        logger.info(f"Initializing database with URL: {DB_URL}")
        if DB_URL.startswith('postgresql'):
            from sqlalchemy import text
            with engine.connect() as connection:
                # Check if schema exists
                logger.info(f"Checking if schema '{SCHEMA_NAME}' exists")
                result = connection.execute(text(f"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{SCHEMA_NAME}'"))
                schema_exists = result.fetchone() is not None

                if not schema_exists:
                    # Create schema if it doesn't exist
                    logger.info(f"Schema '{SCHEMA_NAME}' does not exist. Creating it now...")
                    connection.execute(text(f"CREATE SCHEMA IF NOT EXISTS {SCHEMA_NAME}"))
                    connection.commit()
                    logger.info(f"Schema '{SCHEMA_NAME}' created successfully")
                else:
                    logger.info(f"Schema '{SCHEMA_NAME}' already exists")

                # Set search path to use the schema
                logger.info(f"Setting search path to '{SCHEMA_NAME}'")
                connection.execute(text(f"SET search_path TO {SCHEMA_NAME}, public"))
                connection.commit()
                logger.info(f"Search path set successfully")

        # Create all tables
        logger.info("Creating database tables")
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")

        # Log the tables that were created
        from sqlalchemy import inspect
        inspector = inspect(engine)
        if DB_URL.startswith('postgresql'):
            tables = inspector.get_table_names(schema=SCHEMA_NAME)
            logger.info(f"Tables in schema '{SCHEMA_NAME}': {tables}")
        else:
            tables = inspector.get_table_names()
            logger.info(f"Tables in database: {tables}")

    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise
