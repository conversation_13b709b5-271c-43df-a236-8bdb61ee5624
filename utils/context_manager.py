"""
Enhanced Context Manager for HR Solution.

This module provides advanced context management capabilities for maintaining
conversation state, agent routing history, and seamless context switching
across different HR agents.
"""

import logging
import time
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, field
from enum import Enum

# Set up logger
logger = logging.getLogger(__name__)


class AgentType(Enum):
    """Enumeration of available agent types."""
    ROOT = "hr_root_agent"
    PROFILE = "profile_agent"
    LEAVE_MANAGEMENT = "leave_management_agent"
    ATTENDANCE_MANAGEMENT = "attendance_management_agent"
    POLICY = "policy_agent"
    HR_SERVICE_DESK = "hr_service_desk_agent"


class TopicType(Enum):
    """Enumeration of conversation topics."""
    PROFILE = "profile"
    LEAVE_MANAGEMENT = "leave_management"
    ATTENDANCE = "attendance"
    POLICY = "policy"
    TEAM_MANAGEMENT = "team_management"
    GENERAL_HR = "general_hr"
    UNKNOWN = "unknown"


@dataclass
class ConversationTurn:
    """Represents a single turn in the conversation."""
    timestamp: float
    user_message: str
    agent_name: str
    agent_response: str
    topic: TopicType
    tools_used: List[str] = field(default_factory=list)
    transfer_target: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None


@dataclass
class ContextState:
    """Represents the current context state."""
    current_topic: TopicType = TopicType.UNKNOWN
    active_agent: AgentType = AgentType.ROOT
    conversation_history: List[ConversationTurn] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    session_metadata: Dict[str, Any] = field(default_factory=dict)
    extracted_entities: Dict[str, Any] = field(default_factory=dict)
    pending_actions: List[str] = field(default_factory=list)


class EnhancedContextManager:
    """Enhanced context manager for HR solution."""
    
    def __init__(self):
        """Initialize the context manager."""
        self.context_states: Dict[str, ContextState] = {}
        self.topic_keywords = {
            TopicType.PROFILE: [
                "profile", "employee info", "my details", "personal information",
                "contact details", "employee id", "department", "manager",
                "reporting manager", "work profile", "job title", "position"
            ],
            TopicType.LEAVE_MANAGEMENT: [
                "leave", "vacation", "holiday", "time off", "pto", "sick leave",
                "annual leave", "leave balance", "leave request", "leave history",
                "leave approval", "leave cancellation", "comp off"
            ],
            TopicType.ATTENDANCE: [
                "attendance", "check in", "check out", "present", "absent",
                "attendance report", "attendance summary", "late", "early"
            ],
            TopicType.POLICY: [
                "policy", "policies", "hr policy", "company policy", "guidelines",
                "rules", "procedures", "handbook", "compliance"
            ],
            TopicType.TEAM_MANAGEMENT: [
                "team", "team members", "my team", "direct reports", "subordinates",
                "team structure", "team details", "team information"
            ]
        }
        
        self.agent_routing_map = {
            TopicType.PROFILE: AgentType.PROFILE,
            TopicType.LEAVE_MANAGEMENT: AgentType.LEAVE_MANAGEMENT,
            TopicType.ATTENDANCE: AgentType.ATTENDANCE_MANAGEMENT,
            TopicType.POLICY: AgentType.POLICY,
            TopicType.TEAM_MANAGEMENT: AgentType.PROFILE,
            TopicType.GENERAL_HR: AgentType.HR_SERVICE_DESK
        }
        
    
    def get_or_create_context(self, session_id: str) -> ContextState:
        """Get or create context state for a session."""
        if session_id not in self.context_states:
            self.context_states[session_id] = ContextState()
            logger.info(f"Created new context state for session {session_id}")
        return self.context_states[session_id]
    
    def detect_topic(self, message: str) -> TopicType:
        """Detect the topic of a user message."""
        message_lower = message.lower()
        
        # Score each topic based on keyword matches
        topic_scores = {}
        for topic, keywords in self.topic_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                topic_scores[topic] = score
        
        if not topic_scores:
            return TopicType.UNKNOWN
        
        # Return the topic with the highest score
        detected_topic = max(topic_scores, key=topic_scores.get)
        logger.info(f"Detected topic: {detected_topic.value} for message: {message[:50]}...")
        return detected_topic
    
    def suggest_agent(self, topic: TopicType) -> AgentType:
        """Suggest the best agent for handling a topic."""
        suggested_agent = self.agent_routing_map.get(topic, AgentType.ROOT)
        logger.info(f"Suggested agent: {suggested_agent.value} for topic: {topic.value}")
        return suggested_agent
    
    def record_conversation_turn(
        self,
        session_id: str,
        user_message: str,
        agent_name: str,
        agent_response: str,
        tools_used: Optional[List[str]] = None,
        transfer_target: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ):
        """Record a conversation turn."""
        context = self.get_or_create_context(session_id)
        
        # Detect topic from user message
        topic = self.detect_topic(user_message)
        
        # Create conversation turn
        turn = ConversationTurn(
            timestamp=time.time(),
            user_message=user_message,
            agent_name=agent_name,
            agent_response=agent_response,
            topic=topic,
            tools_used=tools_used or [],
            transfer_target=transfer_target,
            success=success,
            error_message=error_message
        )
        
        # Add to conversation history
        context.conversation_history.append(turn)
        
        # Update current topic and active agent
        if topic != TopicType.UNKNOWN:
            context.current_topic = topic
        
        try:
            context.active_agent = AgentType(agent_name)
        except ValueError:
            logger.warning(f"Unknown agent name: {agent_name}")
        
        # Keep only last 20 turns to manage memory
        if len(context.conversation_history) > 20:
            context.conversation_history = context.conversation_history[-20:]
        
        logger.info(f"Recorded conversation turn for session {session_id}")
    
    def get_conversation_context(self, session_id: str, last_n_turns: int = 5) -> str:
        """Get formatted conversation context for the last N turns."""
        context = self.get_or_create_context(session_id)
        
        if not context.conversation_history:
            return "No previous conversation history."
        
        recent_turns = context.conversation_history[-last_n_turns:]
        
        context_lines = ["## Recent Conversation Context:"]
        for i, turn in enumerate(recent_turns, 1):
            context_lines.append(f"\n**Turn {i}:**")
            context_lines.append(f"- User: {turn.user_message}")
            context_lines.append(f"- Agent ({turn.agent_name}): {turn.agent_response[:100]}...")
            context_lines.append(f"- Topic: {turn.topic.value}")
            if turn.tools_used:
                context_lines.append(f"- Tools Used: {', '.join(turn.tools_used)}")
            if turn.transfer_target:
                context_lines.append(f"- Transferred to: {turn.transfer_target}")
        
        return "\n".join(context_lines)
    
    def detect_context_switch(self, session_id: str, new_message: str) -> bool:
        """Detect if there's a context switch in the conversation."""
        context = self.get_or_create_context(session_id)
        
        if not context.conversation_history:
            return False
        
        new_topic = self.detect_topic(new_message)
        current_topic = context.current_topic
        
        # Context switch detected if topic changes significantly
        context_switch = (
            new_topic != TopicType.UNKNOWN and 
            current_topic != TopicType.UNKNOWN and 
            new_topic != current_topic
        )
        
        if context_switch:
            logger.info(f"Context switch detected: {current_topic.value} -> {new_topic.value}")
        
        return context_switch


    def get_routing_recommendation(self, session_id: str, message: str) -> Dict[str, Any]:
        """Get routing recommendation based on context and message."""
        context = self.get_or_create_context(session_id)

        # Detect topic and suggest agent
        topic = self.detect_topic(message)
        suggested_agent = self.suggest_agent(topic)

        # Check for context switch
        context_switch = self.detect_context_switch(session_id, message)

        # Analyze conversation patterns
        recent_agents = [turn.agent_name for turn in context.conversation_history[-3:]]
        agent_switching_frequency = len(set(recent_agents)) if recent_agents else 0

        recommendation = {
            "suggested_agent": suggested_agent.value,
            "detected_topic": topic.value,
            "context_switch": context_switch,
            "current_topic": context.current_topic.value,
            "active_agent": context.active_agent.value,
            "agent_switching_frequency": agent_switching_frequency,
            "confidence": self._calculate_confidence(topic, context),
            "reasoning": self._generate_reasoning(topic, suggested_agent, context_switch)
        }

        return recommendation

    def _calculate_confidence(self, topic: TopicType, context: ContextState) -> float:
        """Calculate confidence score for routing recommendation."""
        base_confidence = 0.7

        # Increase confidence if topic is clearly detected
        if topic != TopicType.UNKNOWN:
            base_confidence += 0.2

        # Increase confidence if consistent with recent conversation
        if context.conversation_history:
            recent_topics = [turn.topic for turn in context.conversation_history[-3:]]
            if topic in recent_topics:
                base_confidence += 0.1

        return min(base_confidence, 1.0)

    def _generate_reasoning(
        self,
        topic: TopicType,
        suggested_agent: AgentType,
        context_switch: bool
    ) -> str:
        """Generate reasoning for the routing recommendation."""
        reasoning_parts = []

        if topic != TopicType.UNKNOWN:
            reasoning_parts.append(f"Detected topic: {topic.value}")

        reasoning_parts.append(f"Recommended agent: {suggested_agent.value}")

        if context_switch:
            reasoning_parts.append("Context switch detected - user changed topic")

        return "; ".join(reasoning_parts)

    def extract_entities(self, session_id: str, message: str) -> Dict[str, Any]:
        """Extract entities from user message."""
        context = self.get_or_create_context(session_id)

        entities = {}

        # Extract dates (simple pattern matching)
        import re
        date_patterns = [
            r'\b\d{4}-\d{2}-\d{2}\b',  # YYYY-MM-DD
            r'\b\d{1,2}/\d{1,2}/\d{4}\b',  # MM/DD/YYYY
            r'\btomorrow\b',
            r'\byesterday\b',
            r'\btoday\b',
            r'\bnext week\b',
            r'\blast week\b'
        ]

        for pattern in date_patterns:
            matches = re.findall(pattern, message, re.IGNORECASE)
            if matches:
                entities['dates'] = matches

        # Extract employee references
        employee_patterns = [
            r'\bemployee\s+(\w+)\b',
            r'\b(\w+@\w+\.\w+)\b',  # Email addresses
            r'\bID\s*:?\s*(\w+)\b'
        ]

        for pattern in employee_patterns:
            matches = re.findall(pattern, message, re.IGNORECASE)
            if matches:
                entities['employee_references'] = matches

        # Store extracted entities in context
        context.extracted_entities.update(entities)

        return entities

    def clear_context(self, session_id: str):
        """Clear context for a session."""
        if session_id in self.context_states:
            del self.context_states[session_id]
            logger.info(f"Cleared context for session {session_id}")

    def get_context_summary(self, session_id: str) -> Dict[str, Any]:
        """Get a summary of the current context."""
        context = self.get_or_create_context(session_id)

        return {
            "session_id": session_id,
            "current_topic": context.current_topic.value,
            "active_agent": context.active_agent.value,
            "conversation_turns": len(context.conversation_history),
            "extracted_entities": context.extracted_entities,
            "pending_actions": context.pending_actions,
            "last_activity": context.conversation_history[-1].timestamp if context.conversation_history else None
        }


# Global context manager instance
context_manager = EnhancedContextManager()
