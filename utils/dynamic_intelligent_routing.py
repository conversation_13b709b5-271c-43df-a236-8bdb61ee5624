"""
Dynamic Intelligent Routing System - No Hardcoded Mappings.

This system uses LLM to dynamically determine both intent classification
and agent selection without any hardcoded categories or mappings.
"""

import logging
from typing import Dict, Any, Optional, List
import json

logger = logging.getLogger(__name__)

class DynamicRouter:
    """Fully dynamic router that uses LLM for both intent and agent selection."""
    
    def __init__(self, model_name: str = "gemini-2.5-flash"):
        """Initialize the dynamic router."""
        self.model_name = model_name
        
    def route_dynamically(self, user_message: str) -> Dict[str, Any]:
        """
        Use LLM to dynamically determine routing without hardcoded mappings.
        
        Args:
            user_message: The user's natural language message
            
        Returns:
            Dict containing routing decision and explanation
        """
        try:
            # Create dynamic routing prompt
            routing_prompt = self._create_dynamic_routing_prompt(user_message)
            
            # Get LLM response
            response = self._make_llm_request(routing_prompt)
            
            # Parse dynamic response
            routing_decision = self._parse_dynamic_response(response)
            
            logger.info(f"Dynamic routing for '{user_message}': {routing_decision}")
            return routing_decision
            
        except Exception as e:
            logger.error(f"Error in dynamic routing: {e}")
            return self._get_fallback_decision(user_message)
    
    def _create_dynamic_routing_prompt(self, user_message: str) -> str:
        """Create a prompt that lets LLM determine everything dynamically."""
        return f"""
You are an intelligent HR assistant that understands user intent and can route to appropriate agents.

**User Message:** "{user_message}"

**Available Agents:**
- leave_management_agent: Handles leave requests, balance, history, approvals
- attendance_management_agent: Handles attendance tracking, reports, time management
- profile_agent: Handles employee profiles, team information, manager details
- policy_agent: Handles company policies, procedures, guidelines, HR processes
- hr_service_desk_agent: Handles general HR inquiries and complex queries

**Instructions:**
1. Analyze the user's intent and context
2. Determine which agent would be most appropriate
3. Explain your reasoning
4. Consider natural language variations and synonyms

**Response Format (JSON):**
{{
    "agent_name": "selected_agent_name",
    "intent": "brief_description_of_intent",
    "reasoning": "explanation_of_why_this_agent_was_chosen",
    "confidence": "high/medium/low",
    "alternative_agents": ["other_agents_that_could_work"]
}}

**Classification:**
"""
    
    def _make_llm_request(self, prompt: str) -> str:
        """Make LLM request and return response."""
        # This would integrate with your existing LLM infrastructure
        # For demonstration, returning a sample response
        return '''
{
    "agent_name": "policy_agent",
    "intent": "policy_inquiry",
    "reasoning": "The user is asking about exit process, which is a company policy and procedure question",
    "confidence": "high",
    "alternative_agents": ["hr_service_desk_agent"]
}
'''
    
    def _parse_dynamic_response(self, response: str) -> Dict[str, Any]:
        """Parse the LLM response to extract routing decision."""
        try:
            # Clean the response and parse JSON
            response_clean = response.strip()
            if response_clean.startswith('```json'):
                response_clean = response_clean[7:]
            if response_clean.endswith('```'):
                response_clean = response_clean[:-3]
            
            decision = json.loads(response_clean)
            
            return {
                "user_message": "user_message",  # Would be passed in
                "agent_name": decision.get("agent_name", "hr_service_desk_agent"),
                "intent": decision.get("intent", "unknown"),
                "reasoning": decision.get("reasoning", "No reasoning provided"),
                "confidence": decision.get("confidence", "low"),
                "alternative_agents": decision.get("alternative_agents", []),
                "method": "dynamic_llm_routing"
            }
            
        except Exception as e:
            logger.error(f"Error parsing dynamic response: {e}")
            return self._get_fallback_decision("user_message")
    
    def _get_fallback_decision(self, user_message: str) -> Dict[str, Any]:
        """Fallback decision if dynamic routing fails."""
        return {
            "user_message": user_message,
            "agent_name": "hr_service_desk_agent",
            "intent": "unknown",
            "reasoning": "Dynamic routing failed, using fallback",
            "confidence": "low",
            "alternative_agents": [],
            "method": "fallback"
        }

class AdaptiveRouter:
    """Router that learns and adapts from user interactions."""
    
    def __init__(self):
        """Initialize the adaptive router."""
        self.interaction_history = []
        self.successful_routes = {}
        
    def route_with_learning(self, user_message: str, user_feedback: Optional[str] = None) -> Dict[str, Any]:
        """
        Route with learning from previous interactions and user feedback.
        
        Args:
            user_message: The user's message
            user_feedback: Optional feedback about routing success
            
        Returns:
            Dict containing routing decision and learning data
        """
        # Use dynamic router for initial decision
        dynamic_router = DynamicRouter()
        decision = dynamic_router.route_dynamically(user_message)
        
        # Learn from feedback if provided
        if user_feedback:
            self._learn_from_feedback(user_message, decision, user_feedback)
        
        # Store interaction for learning
        self.interaction_history.append({
            "message": user_message,
            "decision": decision,
            "timestamp": "current_timestamp"
        })
        
        return decision
    
    def _learn_from_feedback(self, user_message: str, decision: Dict[str, Any], feedback: str):
        """Learn from user feedback to improve future routing."""
        # Store successful routes for pattern learning
        if "good" in feedback.lower() or "correct" in feedback.lower():
            self.successful_routes[user_message] = decision
            logger.info(f"Learned successful route: {user_message} -> {decision['agent_name']}")
    
    def get_learning_insights(self) -> Dict[str, Any]:
        """Get insights from learning data."""
        return {
            "total_interactions": len(self.interaction_history),
            "successful_routes": len(self.successful_routes),
            "common_patterns": self._analyze_patterns()
        }
    
    def _analyze_patterns(self) -> List[Dict[str, Any]]:
        """Analyze patterns in successful routes."""
        # This would use ML to find patterns
        return []

# Global instances
dynamic_router = DynamicRouter()
adaptive_router = AdaptiveRouter()

def route_without_hardcoding(user_message: str) -> str:
    """
    Route user message without any hardcoded mappings.
    
    Args:
        user_message: The user's message
        
    Returns:
        str: Agent name to route to
    """
    decision = dynamic_router.route_dynamically(user_message)
    return decision.get("agent_name", "hr_service_desk_agent")

def route_with_learning(user_message: str, feedback: Optional[str] = None) -> Dict[str, Any]:
    """
    Route with learning capabilities.
    
    Args:
        user_message: The user's message
        feedback: Optional feedback about routing success
        
    Returns:
        Dict: Routing decision with learning data
    """
    return adaptive_router.route_with_learning(user_message, feedback)

def get_learning_insights() -> Dict[str, Any]:
    """Get insights from the learning system."""
    return adaptive_router.get_learning_insights() 