"""
Truly Dynamic Agent - Zero Hardcoding.

This agent has NO hardcoded categories, mappings, capabilities, or rules.
Everything is determined dynamically by the LLM.
"""

import logging
from typing import Dict, Any, Optional, List
import json

logger = logging.getLogger(__name__)

class TrulyDynamicAgent:
    """Agent with zero hardcoding - everything determined by LLM."""
    
    def __init__(self, model_name: str = "gemini-2.5-flash"):
        """Initialize the truly dynamic agent."""
        self.model_name = model_name
        self.conversation_history = []
        
    def handle_query(self, user_message: str) -> Dict[str, Any]:
        """
        Handle any query with zero hardcoding.
        
        Args:
            user_message: The user's natural language message
            
        Returns:
            Dict containing response and metadata
        """
        try:
            # Create completely dynamic prompt - no hardcoded categories or capabilities
            dynamic_prompt = self._create_truly_dynamic_prompt(user_message)
            
            # Get LLM response
            response = self._make_llm_request(dynamic_prompt)
            
            # Parse response
            result = self._parse_dynamic_response(response, user_message)
            
            # Store conversation
            self.conversation_history.append({
                "user_message": user_message,
                "response": result,
                "timestamp": "current_timestamp"
            })
            
            logger.info(f"Truly dynamic agent handled: '{user_message}'")
            return result
            
        except Exception as e:
            logger.error(f"Error in truly dynamic agent: {e}")
            return self._get_error_response(user_message, str(e))
    
    def _create_truly_dynamic_prompt(self, user_message: str) -> str:
        """Create a prompt with zero hardcoding."""
        
        # Build context from conversation history (no hardcoded categories)
        context_str = ""
        if self.conversation_history:
            recent_context = self.conversation_history[-3:]
            context_str = "\n\n**Recent Conversation:**\n"
            for exchange in recent_context:
                context_str += f"User: {exchange['user_message']}\n"
                context_str += f"Assistant: {exchange['response'].get('response', '')}\n"
        
        return f"""
You are an intelligent assistant that can help with any query. You have access to various tools and information sources.

**User Query:** "{user_message}"

**Instructions:**
1. Understand what the user is asking for
2. Determine what tools or information you need
3. Provide a helpful, comprehensive response
4. If you need more information, ask clarifying questions
5. Be professional and helpful

**Response Format (JSON):**
{{
    "response": "your_helpful_response_to_the_user",
    "what_user_wants": "your_understanding_of_what_they_need",
    "tools_needed": ["any_tools_or_actions_required"],
    "confidence": "high/medium/low",
    "questions": ["any_clarifying_questions"],
    "suggestions": ["related_topics_or_next_steps"]
}}

**Context:**{context_str}

**Response:**
"""
    
    def _make_llm_request(self, prompt: str) -> str:
        """Make LLM request and return response."""
        # This would integrate with your existing LLM infrastructure
        # For demonstration, returning a sample response
        return '''
{
    "response": "I can help you with information about the exit process. This typically involves submitting a formal resignation letter, providing notice to your manager, completing an exit interview, returning company property, and finalizing paperwork. The specific process may vary by company, but generally includes these steps. Would you like me to provide more details about any particular aspect of the exit process?",
    "what_user_wants": "information_about_exit_process",
    "tools_needed": ["information_search"],
    "confidence": "high",
    "questions": ["Are you planning to resign soon?", "Do you need specific information about notice periods?"],
    "suggestions": ["resignation_letter_template", "exit_interview_process", "final_payroll_information"]
}
'''
    
    def _parse_dynamic_response(self, response: str, user_message: str) -> Dict[str, Any]:
        """Parse the LLM response."""
        try:
            # Clean and parse JSON response
            response_clean = response.strip()
            if response_clean.startswith('```json'):
                response_clean = response_clean[7:]
            if response_clean.endswith('```'):
                response_clean = response_clean[:-3]
            
            parsed = json.loads(response_clean)
            
            return {
                "user_message": user_message,
                "response": parsed.get("response", "I'm sorry, I couldn't process your request."),
                "what_user_wants": parsed.get("what_user_wants", "unknown"),
                "tools_needed": parsed.get("tools_needed", []),
                "confidence": parsed.get("confidence", "low"),
                "questions": parsed.get("questions", []),
                "suggestions": parsed.get("suggestions", []),
                "method": "truly_dynamic_agent",
                "timestamp": "current_timestamp"
            }
            
        except Exception as e:
            logger.error(f"Error parsing dynamic response: {e}")
            return self._get_error_response(user_message, str(e))
    
    def _get_error_response(self, user_message: str, error: str) -> Dict[str, Any]:
        """Get error response when processing fails."""
        return {
            "user_message": user_message,
            "response": "I apologize, but I encountered an issue processing your request. Please try again or contact support if the problem persists.",
            "what_user_wants": "unknown",
            "tools_needed": [],
            "confidence": "low",
            "questions": [],
            "suggestions": ["contact_support"],
            "method": "error_fallback",
            "error": error,
            "timestamp": "current_timestamp"
        }
    
    def get_insights(self) -> Dict[str, Any]:
        """Get insights from conversation history."""
        if not self.conversation_history:
            return {"total_exchanges": 0, "patterns": []}
        
        # Analyze what users want (no hardcoded categories)
        user_needs = [exchange['response'].get('what_user_wants', 'unknown') 
                     for exchange in self.conversation_history]
        
        return {
            "total_exchanges": len(self.conversation_history),
            "user_needs": self._get_common_needs(user_needs),
            "average_confidence": self._get_average_confidence(),
            "recent_queries": [exchange['user_message'] for exchange in self.conversation_history[-5:]]
        }
    
    def _get_common_needs(self, needs: List[str]) -> List[Dict[str, Any]]:
        """Get most common user needs (no hardcoded categories)."""
        from collections import Counter
        need_counts = Counter(needs)
        return [{"need": need, "count": count} 
                for need, count in need_counts.most_common(5)]
    
    def _get_average_confidence(self) -> str:
        """Calculate average confidence from recent exchanges."""
        if not self.conversation_history:
            return "unknown"
        
        confidences = [exchange['response'].get('confidence', 'low') 
                      for exchange in self.conversation_history[-10:]]
        
        high_count = confidences.count('high')
        medium_count = confidences.count('medium')
        low_count = confidences.count('low')
        
        if high_count > medium_count and high_count > low_count:
            return "high"
        elif medium_count > low_count:
            return "medium"
        else:
            return "low"

# Global truly dynamic agent instance
truly_dynamic_agent = TrulyDynamicAgent()

def handle_query_truly_dynamic(user_message: str) -> Dict[str, Any]:
    """
    Handle any query with zero hardcoding.
    
    Args:
        user_message: The user's message
        
    Returns:
        Dict: Response with metadata
    """
    return truly_dynamic_agent.handle_query(user_message)

def get_truly_dynamic_insights() -> Dict[str, Any]:
    """Get insights from the truly dynamic agent."""
    return truly_dynamic_agent.get_insights() 