"""
Audit setup module.

This module provides functions to set up audit trail for agents.
"""

import logging
from typing import Optional, Dict, Any

from google.adk.agents import Agent

from utils.audit_callbacks import (
    audit_input_callback,
    audit_tool_callback,
    audit_response_callback,
    register_agent as register_audit_agent
)

# Set up logger
logger = logging.getLogger(__name__)


def setup_agent_audit(agent: Agent) -> None:
    """Set up audit trail for an agent.

    This function registers the audit callbacks with the agent and
    registers the agent in the audit system.

    Args:
        agent: The agent to set up audit for.
    """
    logger.info(f"Setting up audit trail for agent {agent.name}")

    # Register agent in audit system
    agent_id = register_audit_agent(
        agent_name=agent.name,
        agent_description=agent.description,
        agent_config={
            "model": agent.model,
            "instruction": agent.instruction[:100] + "..." if agent.instruction and len(agent.instruction) > 100 else agent.instruction
        }
    )

    # Register agent prompt if available
    if hasattr(agent, 'instruction') and agent.instruction:
        from services.audit_service import AuditService
        logger.info(f"Registering system prompt for agent {agent.name}")
        prompt_id = AuditService.register_agent_prompt(
            agent_id=agent_id,
            prompt_type="system",
            prompt_text=agent.instruction
        )
        logger.info(f"System prompt registered with ID {prompt_id}")

    # Register callbacks with agent
    # Since we can't directly set the callbacks on the agent, we'll integrate with the existing callback system
    # by modifying the combined_post_processing_callback in utils/callbacks.py
    from utils.callbacks import register_audit_callbacks

    # Register this agent for audit callbacks
    register_audit_callbacks(agent.name)

    logger.info(f"Registered agent {agent.name} for audit callbacks")

    logger.info(f"Audit trail set up for agent {agent.name}")


def setup_agents_audit(agents: list[Agent]) -> None:
    """Set up audit trail for multiple agents.

    Args:
        agents: List of agents to set up audit for.
    """
    for agent in agents:
        setup_agent_audit(agent)
