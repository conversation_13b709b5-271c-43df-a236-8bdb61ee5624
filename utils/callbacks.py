"""
Callback functions for agent safety and monitoring.

This module contains callback functions used for agent safety guardrails
and monitoring.
"""

import logging
import time
import uuid
from typing import Optional, Dict, Any, List, Set
import re

from google.adk.agents.callback_context import CallbackContext
from google.adk.models.llm_request import LlmRequest
from google.adk.models.llm_response import LlmResponse
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.tool_context import Tool<PERSON>ontext
from google.genai import types  # For creating response content

from utils.session_helpers import set_session_value, get_session_value

# Import audit functionality
from utils.audit_callbacks import (
    audit_input_callback as _audit_input_callback,
    audit_response_callback as _audit_response_callback,
    audit_tool_callback as _audit_tool_callback
)

# Import enhanced context management
try:
    from utils.context_manager import context_manager
    from utils.enhanced_routing import routing_service
    ENHANCED_CONTEXT_AVAILABLE = True
except ImportError:
    ENHANCED_CONTEXT_AVAILABLE = False
    logger.warning("Enhanced context management not available")

# Set up logger
logger = logging.getLogger(__name__)

# Set of agents registered for audit callbacks
_audit_enabled_agents: Set[str] = set()

def register_audit_callbacks(agent_name: str) -> None:
    """Register an agent for audit callbacks.

    Args:
        agent_name: The name of the agent to register.
    """
    _audit_enabled_agents.add(agent_name)
    logger.info(f"Agent {agent_name} registered for audit callbacks")


def input_safety_guardrail(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    """Inspects incoming user messages for prohibited content and manages conversation context.

    This callback checks for sensitive patterns like PII or prohibited terms,
    blocks the request if found, and maintains conversation context.

    Args:
        callback_context: The callback context.
        llm_request: The LLM request to inspect.

    Returns:
        An LlmResponse if the request should be blocked, None to proceed.
    """
    agent_name = callback_context.agent_name
    logger.debug(f"Input safety guardrail running for agent: {agent_name}")

    # Initialize or get conversation context
    if "conversation_context" not in callback_context.state:
        callback_context.state["conversation_context"] = {
            "history": [],
            "current_topic": None,
            "last_intent": None,
            "session_start": time.time(),
            "user_preferences": {},
            "context_window": []  # Sliding window of recent context
        }

    # Extract the latest user message
    last_user_message_text = ""
    if llm_request.contents:
        for content in reversed(llm_request.contents):
            if content.role == 'user' and content.parts:
                if content.parts[0].text:
                    last_user_message_text = content.parts[0].text
                    break

    if not last_user_message_text:
        logger.warning("No user message found in request")
        return None

    # Check for HR agent context
    agent_name = callback_context.agent_name
    hr_agents = ["hr_service_desk_agent", "leave_management_agent", "attendance_management_agent"]

    if agent_name in hr_agents:
        logger.info(f"HR agent context detected for agent: {agent_name}")
        callback_context.state["hr_agent_flow_active"] = True

    # Update conversation history with enhanced context
    conversation_context = callback_context.state["conversation_context"]
    
    # Add new message to history with metadata
    conversation_context["history"].append({
        "timestamp": time.time(),
        "sender": "user",
        "message": last_user_message_text,
        "agent": agent_name,
        "message_type": "query" if "?" in last_user_message_text else "statement"
    })

    # Maintain a sliding window of recent context (last 10 messages)
    conversation_context["context_window"] = conversation_context["history"][-10:]

    # Update current topic based on message content
    # This is a simple implementation - you might want to use more sophisticated topic detection
    if "leave" in last_user_message_text.lower():
        conversation_context["current_topic"] = "leave_management"
    elif "attendance" in last_user_message_text.lower():
        conversation_context["current_topic"] = "attendance"
    elif "team" in last_user_message_text.lower():
        conversation_context["current_topic"] = "team_management"
    elif "profile" in last_user_message_text.lower():
        conversation_context["current_topic"] = "profile"

    # Store the updated context
    callback_context.state["conversation_context"] = conversation_context

    # Get prohibited terms (could be loaded from config or session)
    prohibited_terms = ["internal_ip", "password123", "NEVER_LOG_THIS", "ssn:", "credit card:"]

    # Check for prohibited terms
    for term in prohibited_terms:
        if term.lower() in last_user_message_text.lower():
            logger.warning(f"Prohibited term '{term}' detected in user message for agent {agent_name}")

            # Record the event in session state
            callback_context.state["safety_guardrail_triggered"] = True

            # Add to safety events list if it exists, otherwise create it
            safety_events = callback_context.state.get("safety_events", [])
            safety_events.append({
                "timestamp": time.time(),
                "agent": agent_name,
                "trigger": f"prohibited_term:{term}",
                "action": "blocked"
            })
            callback_context.state["safety_events"] = safety_events

            # Return a response to block the request
            return LlmResponse(
                content=types.Content(
                    role="model",
                    parts=[types.Part(text="I cannot process this request because it appears to contain sensitive information. Please remove any personal identifiable information or credentials and try again.")]
                )
            )

    # Check for sensitive information patterns (simplified example)
    patterns = [
        (r"\b\d{3}-\d{2}-\d{4}\b", "SSN"),
        (r"\b\d{16}\b", "possible credit card"),
        (r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", "email")
    ]

    for pattern, pattern_name in patterns:
        if re.search(pattern, last_user_message_text):
            # Special case: Allow emails in HR agent context or email collection context
            if pattern_name == "email" and is_email_allowed_context(callback_context, last_user_message_text):
                logger.info(f"Email detected in allowed context - allowing it")
                continue

            logger.warning(f"Possible {pattern_name} pattern detected in user message")

            # Record the event in session state
            callback_context.state["safety_guardrail_triggered"] = True

            # Add to safety events list if it exists, otherwise create it
            safety_events = callback_context.state.get("safety_events", [])
            safety_events.append({
                "timestamp": time.time(),
                "agent": agent_name,
                "trigger": f"pattern:{pattern_name}",
                "action": "blocked"
            })
            callback_context.state["safety_events"] = safety_events

            # Custom message for email pattern
            message = ""
            if pattern_name == "email":
                # For HR agent, emails are expected and should be allowed
                if agent_name == "hr_service_desk_agent" or agent_name == "leave_management_agent" or agent_name == "attendance_management_agent":
                    # Allow emails for HR-related agents
                    logger.info(f"Email detected in HR agent context - allowing it")
                    return None
                else:
                    message = f"I've detected what appears to be sensitive information ({pattern_name}) in your message. Please remove it and try again."
            else:
                message = f"I've detected what appears to be sensitive information ({pattern_name}) in your message. Please remove it and try again."

            # Return a response to block the request
            return LlmResponse(
                content=types.Content(
                    role="model",
                    parts=[types.Part(text=message)]
                )
            )

    # If we get here, the message is clean
    logger.debug(f"Message passed safety checks for agent {agent_name}")

    # Run audit input callback if agent is registered for audit
    if agent_name in _audit_enabled_agents:
        logger.debug(f"Running audit input callback for agent {agent_name}")

        # Ensure session_id is set
        if "session_id" not in callback_context.state:
            callback_context.state["session_id"] = str(uuid.uuid4())
            logger.info(f"Generated new session ID for audit: {callback_context.state['session_id']}")

        # Ensure user_id is set
        if "user_id" not in callback_context.state:
            # Use user_email if available, otherwise use default
            callback_context.state["user_id"] = callback_context.state.get("user_email", "unknown_user")
            logger.info(f"Using {callback_context.state['user_id']} as user ID for audit")

        # Call the audit input callback
        _audit_input_callback(callback_context, llm_request)

    return None


def is_email_allowed_context(callback_context: CallbackContext, message: str) -> bool:
    """Determines if the current conversation is in a context where emails are allowed.

    Args:
        callback_context: The callback context containing conversation history.
        message: The current message to check.

    Returns:
        True if emails are allowed in this context, False otherwise.
    """
    # For HR agents, we should generally allow emails as they're needed for HR operations
    agent_name = callback_context.agent_name
    hr_agents = ["hr_service_desk_agent", "leave_management_agent", "attendance_management_agent"]

    if agent_name in hr_agents:
        # HR agents need to work with email addresses for leave requests, attendance, etc.
        logger.info(f"Email allowed for HR agent: {agent_name}")
        return True

    # For non-HR agents, check if we're in an email collection context
    # Check if this is a direct email response to a request for email
    conversation_history = callback_context.state.get("conversation_history", [])

    # Check current conversation context
    if callback_context.state.get("email_collection_active", False):
        return True

    # Look at previous messages to determine context
    agent_responses = callback_context.state.get("agent_responses", [])
    for response in agent_responses:
        response_text = response.get("response", "").lower()
        if ("what is your corporate email" in response_text or
            "what is your email" in response_text or
            "provide your email" in response_text):
            # Set a flag in the session state for future messages
            callback_context.state["email_collection_active"] = True
            return True

    return False


def tool_safety_guardrail(
    tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext
) -> Optional[Dict[str, Any]]:
    """Validates tool arguments for safety and policy compliance.

    This callback ensures tools are only used with appropriate arguments.

    Args:
        tool: The tool being called.
        args: The arguments provided to the tool.
        tool_context: The tool context.

    Returns:
        A dictionary to override the tool result if blocked, None to proceed.
    """
    tool_name = tool.name
    agent_name = tool_context.agent_name
    logger.debug(f"Tool safety guardrail running for tool '{tool_name}' in agent '{agent_name}'")

    # Record tool usage in session state
    tool_usage = tool_context.state.get("tool_usage", [])
    tool_usage.append({
        "timestamp": time.time(),
        "agent": agent_name,
        "tool": tool_name,
        "args": args
    })
    tool_context.state["tool_usage"] = tool_usage

    # Tool-specific validations
    # HR-specific tool validations could be added here if needed
    hr_tools = ["request_leave", "approve_leave", "reject_leave", "get_attendance_report"]
    if tool_name in hr_tools:
        # For HR tools, we might want to validate employee emails or other parameters
        employee_email = args.get("employee_email", "")

        # Log HR tool usage
        logger.info(f"HR tool {tool_name} called with employee_email: {employee_email}")

    elif tool_name == "restart_service":
        # Validate service_name for critical services
        service_name = args.get("service_name", "")

        critical_services = ["authentication-service", "database", "firewall"]

        for critical_service in critical_services:
            if critical_service in service_name.lower():
                logger.warning(f"Attempted restart of critical service: {service_name}")

                safety_events = tool_context.state.get("safety_events", [])
                safety_events.append({
                    "timestamp": time.time(),
                    "agent": agent_name,
                    "tool": tool_name,
                    "trigger": f"critical_service:{service_name}",
                    "action": "blocked"
                })
                tool_context.state["safety_events"] = safety_events

                return {
                    "status": "error",
                    "message": f"Restarting critical service '{service_name}' requires elevated permissions. Please escalate this ticket to L2 support."
                }

    # If we reach here, the tool call is allowed
    logger.debug(f"Tool '{tool_name}' passed safety checks")

    # Run audit tool callback if agent is registered for audit
    if agent_name in _audit_enabled_agents:
        logger.debug(f"Running audit tool callback for agent {agent_name}")

        # Ensure session_id is set
        if "session_id" not in tool_context.state:
            tool_context.state["session_id"] = str(uuid.uuid4())
            logger.info(f"Generated new session ID for audit: {tool_context.state['session_id']}")

        # Ensure user_id is set
        if "user_id" not in tool_context.state:
            # Use user_email if available, otherwise use default
            tool_context.state["user_id"] = tool_context.state.get("user_email", "unknown_user")
            logger.info(f"Using {tool_context.state['user_id']} as user ID for audit")

        # Call the audit tool callback
        _audit_tool_callback(tool, args, tool_context)

    return None


def post_processing_callback(
    callback_context: CallbackContext, llm_response: LlmResponse
) -> Optional[LlmResponse]:
    """Processes LLM responses after generation.

    This callback can modify or log the LLM's response before it's returned to the user.

    Args:
        callback_context: The callback context.
        llm_response: The LLM response to process.

    Returns:
        A modified LlmResponse or None to use the original.
    """
    agent_name = callback_context.agent_name
    logger.debug(f"Post-processing response from agent: {agent_name}")

    # Extract response text
    if not llm_response.content or not llm_response.content.parts:
        logger.warning(f"Empty response from agent {agent_name}")
        return None

    response_text = llm_response.content.parts[0].text if llm_response.content.parts[0].text else ""

    # Check if agent is asking for email address
    email_request_patterns = [
        "what is your email",
        "your email address",
        "corporate email",
        "what email",
        "provide your email"
    ]

    # Only set password reset flag for non-HR agents
    if any(pattern in response_text.lower() for pattern in email_request_patterns) and agent_name not in ["hr_service_desk_agent", "leave_management_agent", "attendance_management_agent"]:
        logger.info(f"Agent is requesting email address - setting password_reset_flow_active flag")
        callback_context.state["password_reset_flow_active"] = True

    # Record response in session state if needed
    agent_responses = callback_context.state.get("agent_responses", [])
    agent_responses.append({
        "timestamp": time.time(),
        "agent": agent_name,
        "response": response_text[:100] + ("..." if len(response_text) > 100 else "")
    })
    callback_context.state["agent_responses"] = agent_responses

    # Log but don't modify for this example
    logger.debug(f"Processed response from agent {agent_name}")
    return None


def email_extraction_callback(session, event, response_text, *args, **kwargs):
    """Process responses to extract and store email information in session state.

    This callback is triggered after model responses to capture email information
    and ensure it's properly stored in the session state for access by parent agents.

    Args:
        session: The current session
        event: The event that triggered this callback
        response_text: The response text from the model

    Returns:
        The possibly modified response text
    """
    # Check if this is from an HR agent
    agent_name = getattr(event, "agent_name", None)
    hr_agents = ["hr_service_desk_agent", "leave_management_agent", "attendance_management_agent"]

    # Extract email if present in the response
    email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', response_text)
    if email_match:
        email = email_match.group(0)
        set_session_value(session, "user_email", email)
        if agent_name in hr_agents:
            logger.info(f"Extracted email from HR agent response: {email}")

    # Handle function call responses for HR operations
    if isinstance(event.response, google.adk.function_responses.FunctionResponse):
        tool_name = getattr(event.response, "name", None)
        if tool_name in ["request_leave", "get_employee_info"]:
            response_data = getattr(event.response, "response", {})
            if "email" in response_data:
                logger.info(f"Extracted email from {tool_name} tool response")
                set_session_value(session, "user_email", response_data["email"])

    return response_text


def combined_post_processing_callback(
    callback_context: CallbackContext, llm_response: LlmResponse
) -> Optional[LlmResponse]:
    """Combined callback for post-processing, email extraction, and audit.

    This callback combines the functionality of post_processing_callback,
    email_extraction_callback, and audit_response_callback to ensure all
    operations are performed.

    Args:
        callback_context: The callback context.
        llm_response: The LLM response to process.

    Returns:
        A modified LlmResponse or None to use the original.
    """
    agent_name = callback_context.agent_name
    logger.info(f"Starting combined post-processing for agent: {agent_name}")

    # Extract response text
    if not llm_response.content or not llm_response.content.parts:
        logger.warning(f"Empty response from agent {agent_name}")
        return None

    response_text = llm_response.content.parts[0].text if llm_response.content.parts[0].text else ""

    logger.debug(f"Processed response text length: {len(response_text)}")

    # Run original post_processing logic
    # Check if agent is asking for email address
    email_request_patterns = [
        "what is your email",
        "your email address",
        "corporate email",
        "what email",
        "provide your email"
    ]

    # Only set email collection flag for non-HR agents
    if any(pattern in response_text.lower() for pattern in email_request_patterns) and agent_name not in ["hr_service_desk_agent", "leave_management_agent", "attendance_management_agent"]:
        logger.info(f"Agent is requesting email address - setting email_collection_active flag")
        callback_context.state["email_collection_active"] = True

    # Record response in session state if needed
    agent_responses = callback_context.state.get("agent_responses", [])
    agent_responses.append({
        "timestamp": time.time(),
        "agent": agent_name,
        "response": response_text[:100] + ("..." if len(response_text) > 100 else "")
    })
    callback_context.state["agent_responses"] = agent_responses
    logger.info(f"Recorded response in session state for agent: {agent_name}")

    # Enhanced context management integration
    if ENHANCED_CONTEXT_AVAILABLE:
        try:
            # Get session ID from callback context
            session_id = callback_context.state.get("session_id", "unknown")

            # Get the original user message from conversation context
            conversation_context = callback_context.state.get("conversation_context", {})
            history = conversation_context.get("history", [])

            # Find the most recent user message
            user_message = ""
            for entry in reversed(history):
                if entry.get("sender") == "user":
                    user_message = entry.get("message", "")
                    break

            # Record the conversation turn in context manager
            if user_message and session_id != "unknown":
                # Extract tools used from function calls in response
                tools_used = []
                if hasattr(llm_response, 'function_calls') and llm_response.function_calls:
                    tools_used = [fc.name for fc in llm_response.function_calls]

                # Check if this was a transfer
                transfer_target = None
                if "transfer_to_agent" in tools_used:
                    # Extract transfer target from function call args
                    for fc in llm_response.function_calls:
                        if fc.name == "transfer_to_agent" and hasattr(fc, 'args'):
                            transfer_target = fc.args.get("agent_name")
                            break

                # Record the conversation turn
                context_manager.record_conversation_turn(
                    session_id=session_id,
                    user_message=user_message,
                    agent_name=agent_name,
                    agent_response=response_text,
                    tools_used=tools_used,
                    transfer_target=transfer_target,
                    success=True
                )

                logger.info(f"Recorded conversation turn in context manager for session {session_id}")

        except Exception as e:
            logger.warning(f"Failed to update context manager: {e}")

    # Now run email extraction logic
    # Check if this is from an HR agent
    hr_agents = ["hr_service_desk_agent", "leave_management_agent", "attendance_management_agent"]
    if agent_name in hr_agents:
        logger.info(f"Processing response from HR agent: {agent_name}")

    # Extract email if present in the response
    email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', response_text)
    if email_match:
        email = email_match.group(0)
        callback_context.state["user_email"] = email
        logger.info(f"Extracted email from response: {email}")

    # Run audit response callback if agent is registered for audit
    if agent_name in _audit_enabled_agents:
        logger.info(f"Running audit response callback for agent {agent_name}")

        # Ensure session_id is set
        if "session_id" not in callback_context.state:
            callback_context.state["session_id"] = str(uuid.uuid4())
            logger.info(f"Generated new session ID for audit: {callback_context.state['session_id']}")

        # Ensure user_id is set
        if "user_id" not in callback_context.state:
            callback_context.state["user_id"] = callback_context.state.get("user_email", "unknown_user")
            logger.info(f"Using {callback_context.state['user_id']} as user ID for audit")

        # Call the audit response callback
        _audit_response_callback(callback_context, llm_response)

    logger.info(f"Completed combined post-processing for agent {agent_name}")
    logger.info(f"Returning response: {response_text}")
    return llm_response