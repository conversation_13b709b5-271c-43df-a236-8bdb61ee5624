"""
AWS utilities for the HR solution.

This module provides utilities for interacting with AWS services.
"""

import logging
import os
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

try:
    import boto3
    from botocore.exceptions import ClientError
    AWS_AVAILABLE = True
except ImportError:
    logger.warning("AWS SDK (boto3) not installed. AWS functionality will be limited.")
    AWS_AVAILABLE = False


def get_aws_region() -> str:
    """Get the AWS region from environment variables.

    Returns:
        The AWS region.
    """
    return os.environ.get("AWS_REGION", "us-east-1")


def get_secret(secret_name: str) -> Optional[Dict[str, Any]]:
    """Get a secret from AWS Secrets Manager.

    Args:
        secret_name: The name of the secret.

    Returns:
        The secret value as a dictionary, or None if the secret could not be retrieved.
    """
    if not AWS_AVAILABLE:
        logger.error("AWS SDK (boto3) not installed. Cannot retrieve secrets.")
        return None

    region_name = get_aws_region()

    # Create a Secrets Manager client
    try:
        session = boto3.session.Session()
        client = session.client(
            service_name='secretsmanager',
            region_name=region_name
        )
    except Exception as e:
        logger.error(f"Failed to create AWS Secrets Manager client: {str(e)}")
        return None

    try:
        response = client.get_secret_value(SecretId=secret_name)
        if 'SecretString' in response:
            import json
            return json.loads(response['SecretString'])
        else:
            logger.warning(f"Secret {secret_name} does not contain a SecretString")
            return None
    except ClientError as e:
        logger.error(f"Failed to get secret {secret_name}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error getting secret {secret_name}: {str(e)}")
        return None


def put_cloudwatch_log(log_group: str, log_stream: str, message: str, log_level: str = "INFO") -> bool:
    """Put a log message to AWS CloudWatch Logs.

    Args:
        log_group: The name of the log group.
        log_stream: The name of the log stream.
        message: The log message.
        log_level: The log level (INFO, WARNING, ERROR, etc.).

    Returns:
        True if the log was successfully sent, False otherwise.
    """
    if not AWS_AVAILABLE:
        logger.error("AWS SDK (boto3) not installed. Cannot send logs to CloudWatch.")
        return False

    try:
        client = boto3.client('logs', region_name=get_aws_region())
        
        # Create log group and stream if they don't exist
        try:
            client.create_log_group(logGroupName=log_group)
        except ClientError:
            # Log group already exists
            pass
            
        try:
            client.create_log_stream(logGroupName=log_group, logStreamName=log_stream)
        except ClientError:
            # Log stream already exists
            pass
            
        # Get the sequence token if the stream exists
        try:
            response = client.describe_log_streams(
                logGroupName=log_group,
                logStreamNamePrefix=log_stream
            )
            
            sequence_token = None
            if 'logStreams' in response and response['logStreams']:
                if 'uploadSequenceToken' in response['logStreams'][0]:
                    sequence_token = response['logStreams'][0]['uploadSequenceToken']
                    
            # Put log event
            import time
            timestamp = int(time.time() * 1000)
            
            log_event = {
                'logGroupName': log_group,
                'logStreamName': log_stream,
                'logEvents': [
                    {
                        'timestamp': timestamp,
                        'message': f"[{log_level}] {message}"
                    }
                ]
            }
            
            if sequence_token:
                log_event['sequenceToken'] = sequence_token
                
            client.put_log_events(**log_event)
            return True
            
        except ClientError as e:
            logger.error(f"Failed to put log event: {str(e)}")
            return False
            
    except Exception as e:
        logger.error(f"Unexpected error putting log event: {str(e)}")
        return False


def get_parameter(parameter_name: str) -> Optional[str]:
    """Get a parameter from AWS Systems Manager Parameter Store.

    Args:
        parameter_name: The name of the parameter.

    Returns:
        The parameter value, or None if the parameter could not be retrieved.
    """
    if not AWS_AVAILABLE:
        logger.error("AWS SDK (boto3) not installed. Cannot retrieve parameters.")
        return None

    try:
        client = boto3.client('ssm', region_name=get_aws_region())
        response = client.get_parameter(
            Name=parameter_name,
            WithDecryption=True
        )
        return response['Parameter']['Value']
    except ClientError as e:
        logger.error(f"Failed to get parameter {parameter_name}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error getting parameter {parameter_name}: {str(e)}")
        return None
