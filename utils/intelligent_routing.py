"""
Intelligent Routing System using LLM for Intent Classification.

This module replaces hardcoded keyword matching with LLM-powered intent recognition
to intelligently route user queries to appropriate agents.
"""

import logging
from typing import Dict, Any, Optional, List
from enum import Enum

logger = logging.getLogger(__name__)

class IntentType(Enum):
    """Enumeration of possible user intents."""
    LEAVE_MANAGEMENT = "leave_management"
    ATTENDANCE = "attendance"
    PROFILE = "profile"
    POLICY = "policy"
    GENERAL_HR = "general_hr"
    UNKNOWN = "unknown"

class IntentClassifier:
    """LLM-powered intent classifier for intelligent routing."""
    
    def __init__(self, model_name: str = "gemini-2.5-flash"):
        """Initialize the intent classifier."""
        self.model_name = model_name
        
    def classify_intent(self, user_message: str) -> IntentType:
        """
        Use LLM to intelligently classify user intent.
        
        Args:
            user_message: The user's natural language message
            
        Returns:
            IntentType: The classified intent
        """
        try:
            # Import LLM components
            from google.adk.llm_request import LlmRequest
            from google.adk.llm_response import LlmResponse
            from google.adk.sessions import Session
            
            # Create classification prompt
            classification_prompt = self._create_classification_prompt(user_message)
            
            # Make LLM request for intent classification
            request = LlmRequest(
                model=self.model_name,
                content=classification_prompt,
                temperature=0.1  # Low temperature for consistent classification
            )
            
            # Get response from LLM
            response = self._make_llm_request(request)
            
            # Parse the intent from response
            intent = self._parse_intent_response(response)
            
            logger.info(f"Classified intent for '{user_message}': {intent}")
            return intent
            
        except Exception as e:
            logger.error(f"Error classifying intent: {e}")
            return IntentType.UNKNOWN
    
    def _create_classification_prompt(self, user_message: str) -> str:
        """Create a prompt for intent classification."""
        return f"""
You are an intelligent HR assistant that understands user intent. Classify the following user message into one of these categories:

**Available Intent Categories:**
1. LEAVE_MANAGEMENT - Queries about leave, vacation, sick days, time off, leave balance, leave requests, leave history
2. ATTENDANCE - Queries about attendance, time tracking, clock in/out, attendance reports, work hours
3. PROFILE - Queries about employee profiles, team members, managers, employee information, work details
4. POLICY - Queries about company policies, procedures, guidelines, rules, HR processes, exit processes
5. GENERAL_HR - General HR inquiries that don't fit specific categories, general questions about HR services

**User Message:** "{user_message}"

**Instructions:**
- Analyze the user's intent carefully
- Consider context and natural language variations
- Choose the most appropriate category
- If unclear, default to GENERAL_HR

**Response Format:** Respond with ONLY the category name (e.g., "LEAVE_MANAGEMENT")

**Classification:**
"""
    
    def _make_llm_request(self, request) -> str:
        """Make LLM request and return response text."""
        # This would integrate with your existing LLM infrastructure
        # For now, returning a placeholder
        return "GENERAL_HR"
    
    def _parse_intent_response(self, response: str) -> IntentType:
        """Parse the LLM response to determine intent."""
        response_clean = response.strip().upper()
        
        intent_mapping = {
            "LEAVE_MANAGEMENT": IntentType.LEAVE_MANAGEMENT,
            "ATTENDANCE": IntentType.ATTENDANCE,
            "PROFILE": IntentType.PROFILE,
            "POLICY": IntentType.POLICY,
            "GENERAL_HR": IntentType.GENERAL_HR
        }
        
        return intent_mapping.get(response_clean, IntentType.UNKNOWN)

class IntelligentRouter:
    """Intelligent router that uses LLM for intent classification."""
    
    def __init__(self):
        """Initialize the intelligent router."""
        self.intent_classifier = IntentClassifier()
        self.agent_mapping = {
            IntentType.LEAVE_MANAGEMENT: "leave_management_agent",
            IntentType.ATTENDANCE: "attendance_management_agent",
            IntentType.PROFILE: "profile_agent",
            IntentType.POLICY: "policy_agent",
            IntentType.GENERAL_HR: "hr_service_desk_agent",
            IntentType.UNKNOWN: "hr_service_desk_agent"
        }
    
    def route_intelligently(self, user_message: str) -> str:
        """
        Intelligently route user message to appropriate agent.
        
        Args:
            user_message: The user's natural language message
            
        Returns:
            str: The name of the agent to route to
        """
        # Use LLM to classify intent
        intent = self.intent_classifier.classify_intent(user_message)
        
        # Map intent to agent
        agent_name = self.agent_mapping.get(intent, "hr_service_desk_agent")
        
        logger.info(f"Intelligent routing: '{user_message}' -> {intent.value} -> {agent_name}")
        return agent_name
    
    def get_routing_explanation(self, user_message: str) -> Dict[str, Any]:
        """
        Get detailed explanation of routing decision.
        
        Args:
            user_message: The user's message
            
        Returns:
            Dict containing routing decision and explanation
        """
        intent = self.intent_classifier.classify_intent(user_message)
        agent_name = self.agent_mapping.get(intent, "hr_service_desk_agent")
        
        return {
            "user_message": user_message,
            "classified_intent": intent.value,
            "selected_agent": agent_name,
            "confidence": "high" if intent != IntentType.UNKNOWN else "low",
            "reasoning": f"LLM classified this as {intent.value} intent"
        }

# Global router instance
intelligent_router = IntelligentRouter()

def route_message_intelligently(user_message: str) -> str:
    """
    Route a user message intelligently using LLM intent classification.
    
    Args:
        user_message: The user's message
        
    Returns:
        str: Agent name to route to
    """
    return intelligent_router.route_intelligently(user_message)

def get_routing_decision(user_message: str) -> Dict[str, Any]:
    """
    Get detailed routing decision for a user message.
    
    Args:
        user_message: The user's message
        
    Returns:
        Dict: Detailed routing information
    """
    return intelligent_router.get_routing_explanation(user_message) 