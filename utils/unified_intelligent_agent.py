"""
Unified Intelligent Agent - No Routing, No Hardcoding.

This approach uses a single intelligent agent that can handle all HR queries
without any routing logic or hardcoded categories.
"""

import logging
from typing import Dict, Any, Optional, List
import json

logger = logging.getLogger(__name__)

class UnifiedIntelligentAgent:
    """Single intelligent agent that handles all HR queries dynamically."""
    
    def __init__(self, model_name: str = "gemini-2.5-flash"):
        """Initialize the unified intelligent agent."""
        self.model_name = model_name
        self.conversation_history = []
        
    def handle_query(self, user_message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle any HR query intelligently without routing or hardcoded categories.
        
        Args:
            user_message: The user's natural language message
            context: Optional conversation context
            
        Returns:
            Dict containing response and metadata
        """
        try:
            # Create unified prompt that handles everything
            unified_prompt = self._create_unified_prompt(user_message, context)
            
            # Get LLM response
            response = self._make_llm_request(unified_prompt)
            
            # Parse and enhance response
            result = self._parse_unified_response(response, user_message)
            
            # Store conversation history
            self.conversation_history.append({
                "user_message": user_message,
                "response": result,
                "timestamp": "current_timestamp"
            })
            
            logger.info(f"Unified agent handled: '{user_message}'")
            return result
            
        except Exception as e:
            logger.error(f"Error in unified agent: {e}")
            return self._get_error_response(user_message, str(e))
    
    def _create_unified_prompt(self, user_message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Create a unified prompt that handles all HR queries."""
        
        # Build context from conversation history
        context_str = ""
        if self.conversation_history:
            recent_context = self.conversation_history[-3:]  # Last 3 exchanges
            context_str = "\n\n**Recent Conversation:**\n"
            for exchange in recent_context:
                context_str += f"User: {exchange['user_message']}\n"
                context_str += f"Assistant: {exchange['response'].get('response', '')}\n"
        
        return f"""
You are an intelligent HR assistant that can handle any HR-related query. You have access to all HR tools and can provide comprehensive assistance.

**User Query:** "{user_message}"

**Available Capabilities:**
- Leave Management: Check balance, apply leave, view history, manage approvals
- Attendance: Record attendance, view reports, track time
- Employee Profiles: View profiles, team information, manager details
- HR Policies: Search policies, explain procedures, provide guidance
- General HR: Answer questions, provide information, assist with processes

**Instructions:**
1. Understand the user's intent and context
2. Use appropriate tools and information sources
3. Provide a comprehensive, helpful response
4. If you need more information, ask clarifying questions
5. Always be professional and helpful

**Response Format (JSON):**
{{
    "response": "your_helpful_response_to_the_user",
    "intent_understood": "brief_description_of_what_user_wants",
    "tools_used": ["list_of_tools_or_actions_taken"],
    "confidence": "high/medium/low",
    "follow_up_questions": ["any_clarifying_questions_needed"],
    "suggestions": ["related_topics_or_next_steps"]
}}

**Context:**{context_str}

**Response:**
"""
    
    def _make_llm_request(self, prompt: str) -> str:
        """Make LLM request and return response."""
        # This would integrate with your existing LLM infrastructure
        # For demonstration, returning a sample response for exit process query
        return '''
{
    "response": "I can help you with information about the exit process. Let me search our HR policies for the most current information about resignation procedures, notice periods, and exit formalities. This typically includes submitting a formal resignation letter, completing an exit interview, returning company property, and finalizing paperwork. Would you like me to provide specific details about any part of this process?",
    "intent_understood": "user_wants_information_about_exit_process",
    "tools_used": ["policy_search", "hr_information"],
    "confidence": "high",
    "follow_up_questions": ["Are you planning to resign soon?", "Do you need specific information about notice periods?"],
    "suggestions": ["resignation_letter_template", "exit_interview_process", "final_payroll_information"]
}
'''
    
    def _parse_unified_response(self, response: str, user_message: str) -> Dict[str, Any]:
        """Parse the LLM response and enhance with metadata."""
        try:
            # Clean and parse JSON response
            response_clean = response.strip()
            if response_clean.startswith('```json'):
                response_clean = response_clean[7:]
            if response_clean.endswith('```'):
                response_clean = response_clean[:-3]
            
            parsed = json.loads(response_clean)
            
            return {
                "user_message": user_message,
                "response": parsed.get("response", "I'm sorry, I couldn't process your request."),
                "intent_understood": parsed.get("intent_understood", "unknown"),
                "tools_used": parsed.get("tools_used", []),
                "confidence": parsed.get("confidence", "low"),
                "follow_up_questions": parsed.get("follow_up_questions", []),
                "suggestions": parsed.get("suggestions", []),
                "method": "unified_intelligent_agent",
                "timestamp": "current_timestamp"
            }
            
        except Exception as e:
            logger.error(f"Error parsing unified response: {e}")
            return self._get_error_response(user_message, str(e))
    
    def _get_error_response(self, user_message: str, error: str) -> Dict[str, Any]:
        """Get error response when processing fails."""
        return {
            "user_message": user_message,
            "response": "I apologize, but I encountered an issue processing your request. Please try again or contact HR support if the problem persists.",
            "intent_understood": "unknown",
            "tools_used": [],
            "confidence": "low",
            "follow_up_questions": [],
            "suggestions": ["contact_hr_support"],
            "method": "error_fallback",
            "error": error,
            "timestamp": "current_timestamp"
        }
    
    def get_conversation_insights(self) -> Dict[str, Any]:
        """Get insights from conversation history."""
        if not self.conversation_history:
            return {"total_exchanges": 0, "common_intents": []}
        
        # Analyze conversation patterns
        intents = [exchange['response'].get('intent_understood', 'unknown') 
                  for exchange in self.conversation_history]
        
        return {
            "total_exchanges": len(self.conversation_history),
            "common_intents": self._get_common_intents(intents),
            "average_confidence": self._get_average_confidence(),
            "recent_queries": [exchange['user_message'] for exchange in self.conversation_history[-5:]]
        }
    
    def _get_common_intents(self, intents: List[str]) -> List[Dict[str, Any]]:
        """Get most common intents from conversation history."""
        from collections import Counter
        intent_counts = Counter(intents)
        return [{"intent": intent, "count": count} 
                for intent, count in intent_counts.most_common(5)]
    
    def _get_average_confidence(self) -> str:
        """Calculate average confidence from recent exchanges."""
        if not self.conversation_history:
            return "unknown"
        
        confidences = [exchange['response'].get('confidence', 'low') 
                      for exchange in self.conversation_history[-10:]]
        
        high_count = confidences.count('high')
        medium_count = confidences.count('medium')
        low_count = confidences.count('low')
        
        if high_count > medium_count and high_count > low_count:
            return "high"
        elif medium_count > low_count:
            return "medium"
        else:
            return "low"

# Global unified agent instance
unified_agent = UnifiedIntelligentAgent()

def handle_query_unified(user_message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Handle any HR query using the unified intelligent agent.
    
    Args:
        user_message: The user's message
        context: Optional conversation context
        
    Returns:
        Dict: Comprehensive response with metadata
    """
    return unified_agent.handle_query(user_message, context)

def get_agent_insights() -> Dict[str, Any]:
    """Get insights from the unified agent."""
    return unified_agent.get_conversation_insights() 