"""
Audit callbacks module.

This module contains callback functions for recording audit events.
"""

import logging
import time
import uuid
from typing import Optional, Dict, Any, List

from google.adk.agents.callback_context import CallbackContext
from google.adk.models.llm_request import LlmRequest
from google.adk.models.llm_response import LlmResponse
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.tool_context import ToolContext

from services.audit_service import AuditService
from utils.db_config import init_db

# Set up logger
logger = logging.getLogger(__name__)

# Initialize database
try:
    init_db()
except Exception as e:
    logger.error(f"Error initializing audit database: {str(e)}")

# Global registry of agents
_agent_registry = {}


def register_agent(agent_name: str, agent_description: Optional[str] = None, agent_config: Optional[Dict[str, Any]] = None) -> int:
    """Register an agent in the audit system.

    Args:
        agent_name: The name of the agent.
        agent_description: Optional description of the agent.
        agent_config: Optional configuration for the agent.

    Returns:
        The ID of the registered agent.
    """
    # Check if agent is already registered in memory
    if agent_name in _agent_registry:
        return _agent_registry[agent_name]

    # Register agent in database
    agent_id = AuditService.register_agent(
        name=agent_name,
        description=agent_description,
        config=agent_config
    )

    if agent_id:
        # Cache agent ID in memory
        _agent_registry[agent_name] = agent_id
        logger.info(f"Agent {agent_name} registered with ID {agent_id}")
    else:
        # Use a temporary ID if registration failed
        temp_id = abs(hash(agent_name)) % 10000
        _agent_registry[agent_name] = temp_id
        logger.warning(f"Failed to register agent {agent_name} in database, using temporary ID {temp_id}")
        agent_id = temp_id

    return agent_id


def get_agent_id(agent_name: str) -> int:
    """Get the ID of a registered agent.

    Args:
        agent_name: The name of the agent.

    Returns:
        The ID of the agent.
    """
    # Check if agent is already registered
    if agent_name in _agent_registry:
        return _agent_registry[agent_name]

    # Try to register agent
    return register_agent(agent_name)


def audit_input_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    """Records user inputs as audit events.

    Args:
        callback_context: The callback context.
        llm_request: The LLM request to inspect.

    Returns:
        None to proceed with the request.
    """
    agent_name = callback_context.agent_name
    agent_id = get_agent_id(agent_name)

    # Extract session ID from context
    session_id = callback_context.state.get("session_id")
    if not session_id:
        # Generate a new session ID if not present
        session_id = str(uuid.uuid4())
        callback_context.state["session_id"] = session_id

    # Extract user ID from context
    user_id = callback_context.state.get("user_id", "unknown_user")

    # Extract the latest user message
    last_user_message_text = ""
    if llm_request.contents:
        for content in reversed(llm_request.contents):
            if content.role == 'user' and content.parts:
                if content.parts[0].text:
                    last_user_message_text = content.parts[0].text
                    break

    # Check if the user message contains an email address that might be used for password reset
    # This is a simple check to improve the user identifier in audit events
    if last_user_message_text and '@' in last_user_message_text:
        # Extract potential email addresses from the message
        import re
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, last_user_message_text)
        if emails:
            # Use the first email found as the user identifier
            user_id = emails[0]
            # Store it in the context for future use
            callback_context.state["user_id"] = user_id
            logger.info(f"Extracted email from user message: {user_id}")

    # Create session if it doesn't exist
    AuditService.create_session(
        session_id=session_id,
        user_identifier=user_id,
        agent_id=agent_id
    )

    if not last_user_message_text:
        logger.warning("No user message found in request")
        return None

    # Record user message
    message_id = AuditService.add_session_message(
        session_id=session_id,
        actor_type="user",
        message=last_user_message_text
    )

    # Record audit event for user input
    AuditService.record_audit_event(
        session_id=session_id,
        agent_id=agent_id,
        user_identifier=user_id,
        actor_type="user",
        action_type="user_input",
        status="success",
        context_data={"message": last_user_message_text[:100] + ("..." if len(last_user_message_text) > 100 else "")}
    )

    return None


def audit_tool_callback(
    tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext
) -> Optional[Dict[str, Any]]:
    """Records tool calls as audit events.

    Args:
        tool: The tool being called.
        args: The arguments provided to the tool.
        tool_context: The tool context.

    Returns:
        None to proceed with the tool call.
    """
    tool_name = tool.name
    agent_name = tool_context.agent_name
    agent_id = get_agent_id(agent_name)

    # Extract session ID from context
    session_id = tool_context.state.get("session_id")
    if not session_id:
        # Generate a new session ID if not present
        session_id = str(uuid.uuid4())
        tool_context.state["session_id"] = session_id

    # Extract user ID from context
    user_id = tool_context.state.get("user_id", "unknown_user")

    # For password reset operations, use the user_identifier from the arguments
    if tool_name == "reset_password" and "user_identifier" in args:
        user_id = args["user_identifier"]
        # Also store it in the tool context for future reference
        tool_context.state["user_id"] = user_id
        logger.info(f"Using user_identifier from reset_password args: {user_id}")

    # Create session if it doesn't exist
    AuditService.create_session(
        session_id=session_id,
        user_identifier=user_id,
        agent_id=agent_id
    )

    # Record audit event for tool call
    AuditService.record_audit_event(
        session_id=session_id,
        agent_id=agent_id,
        user_identifier=user_id,
        actor_type="tool",
        actor_id=tool_name,
        action_type="tool_call",
        status="pending",
        context_data={"args": args}
    )

    # Register tool if not already registered
    tool_id = AuditService.register_agent_tool(
        agent_id=agent_id,
        tool_name=tool_name,
        tool_description=getattr(tool, "description", None),
        tool_config={"registered_from": "audit_tool_callback"}  # Basic config for tracking
    )

    return None


def audit_response_callback(
    callback_context: CallbackContext, llm_response: LlmResponse
) -> Optional[LlmResponse]:
    """Records agent responses as audit events.

    Args:
        callback_context: The callback context.
        llm_response: The LLM response to process.

    Returns:
        None to use the original response.
    """
    agent_name = callback_context.agent_name
    agent_id = get_agent_id(agent_name)

    # Extract session ID from context
    session_id = callback_context.state.get("session_id")
    if not session_id:
        # Generate a new session ID if not present
        session_id = str(uuid.uuid4())
        callback_context.state["session_id"] = session_id

    # Extract user ID from context
    user_id = callback_context.state.get("user_id", "unknown_user")

    # If we have a user_email in the state (set during password reset), use that
    if callback_context.state.get("user_email"):
        user_id = callback_context.state.get("user_email")
        logger.info(f"Using user_email from state for audit: {user_id}")

    # Create session if it doesn't exist
    AuditService.create_session(
        session_id=session_id,
        user_identifier=user_id,
        agent_id=agent_id
    )

    # Extract response text
    if not llm_response.content or not llm_response.content.parts:
        logger.warning(f"Empty response from agent {agent_name}")
        return None

    response_text = llm_response.content.parts[0].text if llm_response.content.parts[0].text else ""

    # Record agent message
    message_id = AuditService.add_session_message(
        session_id=session_id,
        actor_type="agent",
        message=response_text
    )

    # Record audit event for agent response
    AuditService.record_audit_event(
        session_id=session_id,
        agent_id=agent_id,
        user_identifier=user_id,
        actor_type="agent",
        actor_id=agent_name,
        action_type="agent_response",
        status="success",
        context_data={"message": response_text[:100] + ("..." if len(response_text) > 100 else "")}
    )

    return None


def record_tool_result(
    tool_name: str,
    agent_name: str,
    session_id: str,
    user_id: str,
    args: Dict[str, Any],
    result: Dict[str, Any],
    status: str = "success",
    error_message: Optional[str] = None,
    entity_type: Optional[str] = None,
    entity_id: Optional[str] = None
) -> None:
    """Records the result of a tool call as an audit event.

    Args:
        tool_name: The name of the tool.
        agent_name: The name of the agent.
        session_id: The ID of the session.
        user_id: The ID of the user.
        args: The arguments provided to the tool.
        result: The result of the tool call.
        status: The status of the tool call.
        error_message: Optional error message.
        entity_type: Optional type of entity affected.
        entity_id: Optional ID of entity affected.
    """

    try:
        # Get agent ID
        agent_id = get_agent_id(agent_name)

        # Create session if it doesn't exist
        session_created = AuditService.create_session(
            session_id=session_id,
            user_identifier=user_id,
            agent_id=agent_id
        )

        # Determine entity type and ID if not provided
        if not entity_type and "entity_type" in result:
            entity_type = result["entity_type"]
        if not entity_id:
            if "entity_id" in result:
                entity_id = result["entity_id"]
            elif "id" in result:
                entity_id = str(result["id"])


        # Prepare context data
        context_data = {
            "args": args,
            "result": result
        }

        # Record audit event for tool result
        event_id = AuditService.record_audit_event(
            session_id=session_id,
            agent_id=agent_id,
            user_identifier=user_id,
            actor_type="tool",
            actor_id=tool_name,
            action_type="tool_result",
            entity_type=entity_type,
            entity_id=entity_id,
            status=status,
            error_message=error_message,
            context_data=context_data
        )

    except Exception as e:
        logger.error(f"Error recording tool result: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")