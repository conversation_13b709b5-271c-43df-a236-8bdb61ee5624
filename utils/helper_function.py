from vertexai.language_models import TextEmbeddingModel
from opensearchpy import OpenSearch, AWSV4SignerAuth
import boto3
import os
import traceback
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


def generate_embedding(text: str, model_name: str = "textembedding-gecko@001") -> list:
    """
    Generate a single embedding vector using Google's textembedding-gecko model.

    Args:
        text (str): The input text to embed.
        model_name (str): The Vertex AI embedding model name.

    Returns:
        list: A list of floats representing the embedding vector.
    """
    print("model_name: ", model_name)
   # print("google embedding model: ", os.environ.get('GOOGLE_EMBEDDING_MODEL'))
    try: 
        model = TextEmbeddingModel.from_pretrained(model_name)
        embedding = model.get_embeddings([text])[0]
        return embedding.values
    except Exception as e:
        print("Error generating embedding: ", e)
        print("Error type: ", type(e))
        print("Error message: ", str(e))
        print("Error traceback: ", traceback.format_exc())
        return None


def create_opensearch_client():

    print("creating opensearch client")

    # Step 1: Connect to OpenSearch

    OPENSEARCH_HOST = os.environ.get("OPENSEARCH_HOST")
    OPENSEARCH_PASS = os.environ.get("OPENSEARCH_PASS")
    OPENSEARCH_USER = os.environ.get("OPENSEARCH_USER")
    search_client = OpenSearch(
        hosts=[{'host': OPENSEARCH_HOST, 'port': 443}],
        http_auth=(OPENSEARCH_USER, OPENSEARCH_PASS),
        use_ssl=True,
        verify_certs=False
    )
    print("search client: ", search_client)
    return search_client