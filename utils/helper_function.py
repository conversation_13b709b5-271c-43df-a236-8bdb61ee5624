from opensearchpy import OpenSearch, AWSV4SignerAuth
import boto3
import os
import traceback
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


def generate_embedding(text: str, model_name: str = None) -> list:
    """
    Generate a single embedding vector using Google's embedding models.
    Supports both Google AI API and Vertex AI.

    Args:
        text (str): The input text to embed.
        model_name (str): The embedding model name.

    Returns:
        list: A list of floats representing the embedding vector.
    """
    # Use environment variable for model name if not provided
    if model_name is None:
        model_name = os.environ.get('GOOGLE_EMBEDDING_MODEL', 'text-embedding-005')
    
    print("model_name: ", model_name)
    print("google embedding model: ", os.environ.get('GOOGLE_EMBEDDING_MODEL'))
    
    # Check if we have the required credentials
    google_api_key = os.environ.get('GOOGLE_API_KEY')
    google_credentials = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS')
    use_vertex_ai = os.environ.get('GOOGLE_GENAI_USE_VERTEXAI', 'FALSE').upper() == 'TRUE'
    
    if not google_api_key and not google_credentials:
        print("Warning: No Google API credentials found, using fallback vector")
        return [0.0] * 768  # Fallback vector to prevent OpenSearch errors
    
    try:
        if use_vertex_ai and google_credentials:
            # Use Vertex AI
            print("Using Vertex AI for embedding generation")
            from vertexai.language_models import TextEmbeddingModel
            model = TextEmbeddingModel.from_pretrained(model_name)
            embedding = model.get_embeddings([text])[0]
            return embedding.values
        elif google_api_key:
            # Use Google AI API
            print("Using Google AI API for embedding generation")
            import google.generativeai as genai
            genai.configure(api_key=google_api_key)
            
            # Use the embedding model from environment or default
            embedding_model = os.environ.get('GOOGLE_EMBEDDING_MODEL', 'text-embedding-005')
            
            # Generate embedding using Google AI API
            response = genai.embed_content(
                model=embedding_model,
                content=text,
                task_type="retrieval_document"
            )
            return response['embedding']
        else:
            print("Warning: No valid Google API credentials found, using fallback vector")
            return [0.0] * 768
            
    except Exception as e:
        print("Error generating embedding: ", e)
        print("Error type: ", type(e))
        print("Error message: ", str(e))
        print("Error traceback: ", traceback.format_exc())
        print("Using fallback vector to prevent OpenSearch errors")
        return [0.0] * 768  # Fallback vector instead of None


def create_opensearch_client():

    print("creating opensearch client")

    # Step 1: Connect to OpenSearch

    OPENSEARCH_HOST = os.environ.get("OPENSEARCH_HOST")
    OPENSEARCH_PASS = os.environ.get("OPENSEARCH_PASS")
    OPENSEARCH_USER = os.environ.get("OPENSEARCH_USER")
    search_client = OpenSearch(
        hosts=[{'host': OPENSEARCH_HOST, 'port': 443}],
        http_auth=(OPENSEARCH_USER, OPENSEARCH_PASS),
        use_ssl=True,
        verify_certs=False
    )
    print("search client: ", search_client)
    return search_client