"""
Mock implementation of the Google ADK CallbackContext class.
"""

from typing import Dict, Any, Optional

class CallbackContext:
    """Mock implementation of the Google ADK CallbackContext class."""
    
    def __init__(self, agent_name: str = "", state: Optional[Dict[str, Any]] = None):
        """Initialize the CallbackContext.
        
        Args:
            agent_name: Name of the agent
            state: State dictionary
        """
        self.agent_name = agent_name
        self.state = state or {}
