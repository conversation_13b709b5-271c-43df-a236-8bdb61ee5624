"""
Mock implementation of the Google ADK Runner class.
"""

import logging
from typing import Dict, Any, Optional, AsyncGenerator
from .agents import Agent

logger = logging.getLogger(__name__)

class Runner:
    """Mock implementation of the Google ADK Runner class."""

    def __init__(self, agent: Agent, app_name: str = "", session_service=None, memory_service=None, **kwargs):
        """Initialize the Runner.

        Args:
            agent: The agent to run
            app_name: Application name
            session_service: Session service
            memory_service: Memory service
        """
        self.agent = agent
        self.app_name = app_name
        self.session_service = session_service
        self.memory_service = memory_service
        self.artifact_service = None  # Mock artifact service
        logger.info(f"Runner initialized for agent {agent.name} with app_name={app_name}")
    
    async def run_async(self, user_id: str, session_id: str, new_message, **kwargs) -> AsyncGenerator[Any, None]:
        """Run the agent asynchronously.

        Args:
            user_id: User ID
            session_id: Session ID
            new_message: Message content object

        Yields:
            Response events
        """
        try:
            # Extract text from the message content
            message_text = ""
            if hasattr(new_message, 'parts') and new_message.parts:
                for part in new_message.parts:
                    if hasattr(part, 'text') and part.text:
                        message_text += part.text

            logger.info(f"Processing message: {message_text}")

            # Create a mock event that looks like the real Google ADK event
            class MockEvent:
                def __init__(self, content_text: str, is_final: bool = False):
                    self.content = MockContent(content_text)
                    self.author = "assistant"
                    self._is_final = is_final

                def is_final_response(self):
                    return self._is_final

            class MockContent:
                def __init__(self, text: str):
                    self.parts = [MockPart(text)]

            class MockPart:
                def __init__(self, text: str):
                    self.text = text

            # Process the message and yield intermediate and final events
            if not message_text.strip():
                yield MockEvent("I didn't receive any message. Could you please try again?", True)
                return

            # Check if this is a tool execution context exposure
            if "called tool" in message_text and "with parameters:" in message_text:
                logger.warning("Detected tool execution context in message - filtering")
                yield MockEvent("I'm processing your request. Please wait a moment while I retrieve the information.", True)
                return

            # Process the message through the agent
            try:
                response = self.agent.process_message(message_text, {"user_id": user_id, "session_id": session_id})

                # Filter out any tool execution details
                if "called tool" in response and "with parameters:" in response:
                    logger.warning("Detected tool execution details in response - providing generic response")
                    response = "I've processed your request. How else can I help you today?"

                # Yield the final response
                yield MockEvent(response, True)

            except Exception as e:
                logger.error(f"Error processing message: {e}")
                yield MockEvent("I apologize, but I encountered an error processing your request. Please try again.", True)

        except Exception as e:
            logger.error(f"Error in runner: {e}")
            yield MockEvent(f"I apologize, but I encountered an error processing your request: {str(e)}", True)
    
    def run(self, user_id: str, session_id: str, new_message, **kwargs) -> str:
        """Run the agent synchronously.

        Args:
            user_id: User ID
            session_id: Session ID
            new_message: Message content object

        Returns:
            Response string
        """
        try:
            # Extract text from the message content
            message_text = ""
            if hasattr(new_message, 'parts') and new_message.parts:
                for part in new_message.parts:
                    if hasattr(part, 'text') and part.text:
                        message_text += part.text

            return self.agent.process_message(message_text, {"user_id": user_id, "session_id": session_id})
        except Exception as e:
            logger.error(f"Error in runner: {e}")
            return f"I apologize, but I encountered an error processing your request: {str(e)}"

    async def run_live(self, user_id: str, session_id: str, **kwargs):
        """Run the agent in live mode (mock implementation)."""
        logger.info(f"Live mode not implemented in mock runner for user {user_id}, session {session_id}")
        return None

    def close(self):
        """Close the runner (mock implementation)."""
        logger.info("Closing mock runner")
        pass
