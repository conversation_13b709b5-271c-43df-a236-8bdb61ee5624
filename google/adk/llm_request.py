"""
Mock implementation of the Google ADK LlmRequest class.
"""

from typing import List, Optional, Dict, Any

class LlmRequestMessage:
    """Mock implementation of request message."""
    
    def __init__(self, content: str, role: str = "user"):
        self.content = content
        self.role = role

class LlmRequest:
    """Mock implementation of the Google ADK LlmRequest class."""
    
    def __init__(self, messages: Optional[List[LlmRequestMessage]] = None):
        """Initialize the LlmRequest.
        
        Args:
            messages: List of messages
        """
        self.messages = messages or []
