"""
Mock implementation of the Google ADK Session classes.
"""

import logging
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class Session:
    """Mock implementation of the Google ADK Session class."""
    
    def __init__(self, session_id: str, data: Optional[Dict[str, Any]] = None):
        """Initialize the Session.
        
        Args:
            session_id: Session ID
            data: Session data
        """
        self.session_id = session_id
        self.data = data or {}

class BaseSessionService(ABC):
    """Abstract base class for session services."""
    
    @abstractmethod
    def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session by ID."""
        pass
    
    @abstractmethod
    def create_session(self, session_id: str, data: Optional[Dict[str, Any]] = None) -> Session:
        """Create a new session."""
        pass
    
    @abstractmethod
    def update_session(self, session_id: str, data: Dict[str, Any]) -> None:
        """Update session data."""
        pass
    
    @abstractmethod
    def delete_session(self, session_id: str) -> None:
        """Delete a session."""
        pass

class InMemorySessionService(BaseSessionService):
    """In-memory implementation of session service."""
    
    def __init__(self):
        """Initialize the service."""
        self.sessions: Dict[str, Session] = {}
        logger.info("InMemorySessionService initialized")
    
    def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session by ID."""
        return self.sessions.get(session_id)
    
    def create_session(self, session_id: str, data: Optional[Dict[str, Any]] = None) -> Session:
        """Create a new session."""
        session = Session(session_id, data)
        self.sessions[session_id] = session
        logger.debug(f"Created session {session_id}")
        return session
    
    def update_session(self, session_id: str, data: Dict[str, Any]) -> None:
        """Update session data."""
        if session_id in self.sessions:
            self.sessions[session_id].data.update(data)
            logger.debug(f"Updated session {session_id}")
    
    def delete_session(self, session_id: str) -> None:
        """Delete a session."""
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.debug(f"Deleted session {session_id}")
