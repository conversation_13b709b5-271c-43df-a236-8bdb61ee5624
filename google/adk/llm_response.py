"""
Mock implementation of the Google ADK LlmResponse class.
"""

from typing import List, Optional

class LlmResponsePart:
    """Mock implementation of response part."""
    
    def __init__(self, text: str):
        self.text = text

class LlmResponseContent:
    """Mock implementation of response content."""
    
    def __init__(self, text: str):
        self.parts = [LlmResponsePart(text)]

class LlmResponse:
    """Mock implementation of the Google ADK LlmResponse class."""
    
    def __init__(self, text: str):
        """Initialize the LlmResponse.
        
        Args:
            text: Response text
        """
        self.content = LlmResponseContent(text)
