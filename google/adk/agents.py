"""
Mock implementation of the Google ADK Agent class.

This module provides a mock implementation of the Agent class that properly
handles tool responses and generates user-friendly responses.
"""

import logging
import json
from typing import List, Optional, Dict, Any, Callable
import google.generativeai as genai
import os

# Set up logger
logger = logging.getLogger(__name__)

class Agent:
    """Mock implementation of the Google ADK Agent class with proper tool response handling."""
    
    def __init__(
        self,
        name: str,
        model: str = "gemini-2.5-flash",
        description: str = "",
        instruction: str = "",
        global_instruction: str = "",
        output_key: str = "",
        tools: Optional[List[Callable]] = None,
        sub_agents: Optional[List['Agent']] = None,
        before_model_callback: Optional[Callable] = None,
        before_tool_callback: Optional[Callable] = None,
        after_model_callback: Optional[Callable] = None,
        **kwargs
    ):
        """Initialize the Agent.
        
        Args:
            name: Agent name
            model: Model name to use
            description: Agent description
            instruction: System instruction for the agent
            global_instruction: Global instruction
            output_key: Output key for responses
            tools: List of tools available to the agent
            sub_agents: List of sub-agents
            before_model_callback: Callback before model call
            before_tool_callback: Callback before tool call
            after_model_callback: Callback after model call
        """
        self.name = name
        self.model = model
        self.description = description
        self.instruction = instruction
        self.global_instruction = global_instruction
        self.output_key = output_key
        self.tools = tools or []
        self.sub_agents = sub_agents or []
        
        # Store callbacks
        self.before_model_callback = before_model_callback
        self.before_tool_callback = before_tool_callback
        self.after_model_callback = after_model_callback
        
        # Initialize the Gemini model
        self._init_model()
        
        logger.info(f"Agent '{name}' initialized with {len(self.tools)} tools")
    
    def _init_model(self):
        """Initialize the Gemini model."""
        try:
            api_key = os.environ.get("GOOGLE_API_KEY")
            if api_key:
                genai.configure(api_key=api_key)
                self.gemini_model = genai.GenerativeModel(self.model)
                logger.debug(f"Gemini model {self.model} initialized for agent {self.name}")
            else:
                logger.warning(f"No Google API key found for agent {self.name}")
                self.gemini_model = None
        except Exception as e:
            logger.error(f"Error initializing Gemini model for agent {self.name}: {e}")
            self.gemini_model = None
    
    def process_message(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Process a user message and return a response.
        
        Args:
            message: User message
            context: Optional context dictionary
            
        Returns:
            Agent response string
        """
        try:
            logger.info(f"Agent {self.name} processing message: {message}")
            
            # Create the full prompt with instruction and message
            full_prompt = self._create_prompt(message, context)
            
            # Check if we need to call any tools
            tool_response = self._check_and_call_tools(message, context)
            
            if tool_response:
                # Generate response based on tool result
                response = self._generate_response_with_tool_result(message, tool_response, context)
            else:
                # Generate direct response
                response = self._generate_direct_response(full_prompt, context)
            
            # Apply post-processing callback if available
            if self.after_model_callback and context:
                # Create mock callback context and response objects
                from google.adk.callback_context import CallbackContext
                from google.adk.llm_response import LlmResponse
                
                callback_context = CallbackContext(agent_name=self.name, state=context)
                llm_response = LlmResponse(response)
                
                processed_response = self.after_model_callback(callback_context, llm_response)
                if processed_response:
                    response = processed_response.content.parts[0].text
            
            logger.info(f"Agent {self.name} generated response")
            return response
            
        except Exception as e:
            logger.error(f"Error processing message in agent {self.name}: {e}")
            return f"I apologize, but I encountered an error processing your request. Please try again."
    
    def _create_prompt(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Create the full prompt for the model."""
        prompt_parts = []
        
        if self.global_instruction:
            prompt_parts.append(f"Global Instruction: {self.global_instruction}")
        
        if self.instruction:
            prompt_parts.append(f"Agent Instruction: {self.instruction}")
        
        # Add tool information
        if self.tools:
            tool_descriptions = []
            for tool in self.tools:
                tool_name = getattr(tool, '__name__', str(tool))
                tool_doc = getattr(tool, '__doc__', 'No description available')
                tool_descriptions.append(f"- {tool_name}: {tool_doc}")
            
            prompt_parts.append(f"Available Tools:\n" + "\n".join(tool_descriptions))
        
        prompt_parts.append(f"User Message: {message}")
        
        return "\n\n".join(prompt_parts)
    
    def _check_and_call_tools(self, message: str, context: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """Check if any tools should be called and call them."""
        if not self.tools:
            return None
        
        # Simple tool matching logic - in a real implementation this would be more sophisticated
        message_lower = message.lower()
        
        for tool in self.tools:
            tool_name = getattr(tool, '__name__', str(tool))
            
            # Check if this tool should be called based on the message
            if self._should_call_tool(tool_name, message_lower):
                try:
                    logger.info(f"Calling tool {tool_name} for message: {message}")
                    
                    # Apply before_tool_callback if available
                    if self.before_tool_callback and context:
                        # Mock tool context
                        from google.adk.tools.tool_context import ToolContext
                        tool_context = ToolContext()
                        tool_context.state = context
                        tool_context.agent_name = self.name
                        
                        callback_result = self.before_tool_callback(tool, {}, tool_context)
                        if callback_result:  # Tool call was blocked
                            return callback_result
                    
                    # Call the tool
                    if tool_name == "transfer_to_agent":
                        # Handle transfer tool specially
                        target_agent = self._determine_transfer_target(message_lower)
                        if target_agent:
                            result = {"status": "transfer", "target_agent": target_agent, "message": message}
                        else:
                            result = {"status": "error", "message": "Could not determine target agent"}
                    else:
                        # Call the actual tool
                        result = self._call_tool_safely(tool, message, context)
                    
                    logger.info(f"Tool {tool_name} returned: {result}")
                    return {"tool_name": tool_name, "result": result}
                    
                except Exception as e:
                    logger.error(f"Error calling tool {tool_name}: {e}")
                    return {"tool_name": tool_name, "result": {"status": "error", "message": str(e)}}
        
        return None
    
    def _should_call_tool(self, tool_name: str, message_lower: str) -> bool:
        """Determine if a tool should be called based on the message."""
        # Tool-specific logic
        if tool_name == "get_policy_information":
            return any(keyword in message_lower for keyword in ["policy", "procedure", "guideline", "rule"])
        elif tool_name == "get_leave_balance":
            return any(keyword in message_lower for keyword in ["leave balance", "balance", "available leave"])
        elif tool_name == "get_employee_info":
            return any(keyword in message_lower for keyword in ["profile", "my information", "employee details"])
        elif tool_name == "transfer_to_agent":
            # This will be handled by domain-specific logic
            return False
        
        return False
    
    def _determine_transfer_target(self, message_lower: str) -> Optional[str]:
        """Determine which agent to transfer to based on the message."""
        if any(keyword in message_lower for keyword in ["leave", "vacation", "sick", "time off"]):
            return "leave_management_agent"
        elif any(keyword in message_lower for keyword in ["profile", "team", "manager", "employee"]):
            return "profile_agent"
        elif any(keyword in message_lower for keyword in ["policy", "procedure", "guideline"]):
            return "policy_agent"
        elif any(keyword in message_lower for keyword in ["attendance", "clock", "time tracking"]):
            return "attendance_management_agent"
        
        return None
    
    def _call_tool_safely(self, tool: Callable, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Safely call a tool with appropriate parameters."""
        try:
            tool_name = getattr(tool, '__name__', str(tool))
            
            # Extract parameters based on tool type
            if tool_name == "get_policy_information":
                return tool(question=message, usercountry="IN", userlegalentity="IN-MH-001")
            elif tool_name in ["get_leave_balance", "get_employee_info"]:
                # These tools use session context
                return tool()
            else:
                # Try calling with no parameters first
                return tool()
                
        except Exception as e:
            logger.error(f"Error calling tool {tool}: {e}")
            return {"status": "error", "message": str(e)}
    
    def _generate_response_with_tool_result(self, message: str, tool_response: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> str:
        """Generate a response based on tool result."""
        tool_name = tool_response.get("tool_name", "unknown")
        result = tool_response.get("result", {})
        
        # Handle different tool responses
        if tool_name == "get_policy_information":
            return self._format_policy_response(result)
        elif tool_name == "get_leave_balance":
            return self._format_leave_balance_response(result)
        elif tool_name == "get_employee_info":
            return self._format_employee_info_response(result)
        elif result.get("status") == "transfer":
            return f"Let me connect you with the appropriate specialist for your request."
        else:
            # Generic response formatting
            if result.get("status") == "success":
                return f"✅ I've successfully processed your request. {result.get('message', '')}"
            else:
                return f"❌ I encountered an issue: {result.get('message', 'Unknown error')}"
    
    def _format_policy_response(self, result: Dict[str, Any]) -> str:
        """Format policy tool response."""
        if result.get("status") == "success":
            data = result.get("data", [])
            if data:
                response = "## Policy Information\n\n"
                for policy in data[:3]:  # Show top 3 policies
                    title = policy.get("title", "Unknown Policy")
                    content = policy.get("content", "No content available")
                    response += f"**{title}**\n{content[:300]}...\n\n"
                return response
            else:
                return "**Policy Information Not Found**\n\nI couldn't find the specific policy information you're looking for. Please try rephrasing your question or contact HR for assistance."
        else:
            return f"❌ Error retrieving policy information: {result.get('message', 'Unknown error')}"
    
    def _format_leave_balance_response(self, result: Dict[str, Any]) -> str:
        """Format leave balance response."""
        if result.get("status") == "success":
            return "🏖️ **Your Leave Balance:**\n\nI've retrieved your current leave balance information."
        else:
            return f"❌ Error retrieving leave balance: {result.get('message', 'Unknown error')}"
    
    def _format_employee_info_response(self, result: Dict[str, Any]) -> str:
        """Format employee info response."""
        if result.get("status") == "success":
            return "👤 **Your Profile Information:**\n\nI've retrieved your employee profile information."
        else:
            return f"❌ Error retrieving profile information: {result.get('message', 'Unknown error')}"
    
    def _generate_direct_response(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Generate a direct response using the model."""
        if not self.gemini_model:
            return "I'm sorry, but I'm unable to process your request at the moment. Please try again later."
        
        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I apologize, but I encountered an error generating a response. Please try again."
