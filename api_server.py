
# -------------------
# API Server for HR AI Assistant Solution
# -------------------
# This module provides a FastAPI server for the HR AI solution.

# -------------------
# Route: Get all messages for a session (conversation history)
# -------------------

import asyncio
import logging
import os
import jwt
import base64
import requests
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import json
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, Request, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field
# from sse_starlette.sse import EventSourceResponse

from main import HRAgentApplication as HRApplication
from utils.logging_config import configure_logging
from utils.db_config import init_db
from utils.hr_auth import hr_auth
from api.audit_routes import router as audit_router

# Configure logging
configure_logging()
logger = logging.getLogger(__name__)


# Initialize database
try:
    init_db()
    logger.info("Database initialized successfully")
except Exception as e:
    logger.error(f"Error initializing database: {str(e)}")
    exit(1)

# Create FastAPI app
app = FastAPI(
    title="Virtual HR AI Assistant API",
    description="API for the Virtual HR AI Assistant",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Create HRAgent application
hr_app = HRApplication(
    app_name="hragent_api",
    root_model=os.environ.get("ROOT_MODEL", "gemini-2.5-flash"),
    sub_agent_model=os.environ.get("SUB_AGENT_MODEL", "gemini-2.5-flash"),
    safety_callbacks=True,
    log_level=logging.INFO
)

# Include routers
app.include_router(audit_router)

# Models
class SessionRequest(BaseModel):
    """Session creation request model."""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    initial_state: Optional[Dict[str, Any]] = None

class MessageRequest(BaseModel):
    """Message request model."""
    message: str
    user_id: str
    session_id: str
    token: Optional[str] = None

class MessageResponse(BaseModel):
    """Message response model."""
    content: Optional[str] = None
    type: str = "message"
    is_final: bool = True
    error: Optional[str] = None
    session_id: Optional[str] = None

# Add new model for Masi orchestrator request
class OrchestratorRequest(BaseModel):
    """Request model for Masi orchestrator."""
    conversation_id: Optional[str] = None
    user_id: str
    question: str

class PolicySearchRequest(BaseModel):
    """Model for policy search request."""
    query: str
    top_k: int = 5

class PolicyByIdRequest(BaseModel):
    """Model for policy by ID request."""
    policy_id: str

class PolicyByCategoryRequest(BaseModel):
    """Model for policy by category request."""
    category: str


class AttendanceRecordRequest(BaseModel):
    """Model for attendance record request."""
    employee_email: str
    status: str
    timestamp: Optional[str] = None
    location: Optional[Dict[str, float]] = None
    notes: Optional[str] = None

class AttendanceReportRequest(BaseModel):
    """Model for attendance report request."""
    employee_email: str
    start_date: str
    end_date: str

class AttendanceAnomalyRequest(BaseModel):
    """Model for attendance anomaly detection request."""
    employee_email: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    anomaly_types: Optional[List[str]] = None


def get_api_auth_token(access_token: str) -> Dict[str, Any]:
    """
    Extract and validate API authentication token from Microsoft access token.

    Args:
        access_token (str): Microsoft access token from frontend

    Returns:
        Dict[str, Any]: Dictionary containing token information and validation status

    Raises:
        HTTPException: If token is invalid or expired
    """
    if not access_token:
        logger.error("No access token provided")
        raise HTTPException(status_code=401, detail="Access token is required")

    try:
        # Remove 'Bearer ' prefix if present
        if access_token.startswith('Bearer '):
            access_token = access_token[7:]

        # Call WagonHR auth API to exchange Microsoft token for API auth token
        try:
            # Call WagonHR auth token API which takes this token and type as request body and returns an auth token
            url = "https://qa-api-wagonhr.mouritech.net/auth/user/v3/login"
            body = json.dumps({"accessToken": access_token, "type": "azure_ad_identifier"})
            response = requests.post(url, data=body, headers={"Content-Type": "application/json"})

            if response.status_code == 200:
                token_data = response.json()
                # Extract user information from the response
                data = token_data.get("data")
                api_auth_token = data.get("user").get("token")

                # Set both Microsoft and WagonHR tokens in the HR auth manager for API calls
                if hr_auth:
                    logger.info("Setting Microsoft token in HR auth manager...")
                    hr_auth.set_microsoft_token(
                        token=access_token,
                        expiry_timestamp=token_data.get("expires_at")
                    )
                    logger.info("Microsoft token set in HR auth manager")

                    # Set the WagonHR token obtained from the exchange
                    try:
                        hr_auth.set_wagonhr_token(
                            token=api_auth_token,
                            expiry_timestamp=token_data.get("expires_at")
                        )
                        logger.info("WagonHR token set in HR auth manager successfully")
                    except Exception as e:
                        logger.error(f"Failed to set WagonHR token: {str(e)}")
                else:
                    logger.error("hr_auth is None - cannot set tokens")

                # Set tenant/entity context for API clients
                user_data = data.get("user", {})
                entity_permissions = user_data.get("entityPermissions", [])
                if entity_permissions:
                    # Use the default entity (first one or the one marked as default)
                    default_entity = None
                    for entity in entity_permissions:
                        if entity.get("isDefault", False):
                            default_entity = entity
                            break
                    if not default_entity and entity_permissions:
                        default_entity = entity_permissions[0]

                    if default_entity:
                        entity_code = default_entity.get("legalEntityCode")
                        entity_name = default_entity.get("legalEntityName")
                        logger.info(f"Setting tenant context: Entity={entity_name} ({entity_code})")

                        # Import and set context for API clients
                        from utils.hr_api_client import leave_balance_client, attendance_client
                        if entity_code:
                            # Set tenant_id to 'mouritech' and entity_id to the entity code
                            leave_balance_client.set_tenant_context(
                                tenant_id="mouritech",
                                entity_id=entity_code
                            )
                            attendance_client.set_tenant_context(
                                tenant_id="mouritech",
                                entity_id=entity_code
                            )
                            logger.info("Tenant/entity context set for API clients")

                return api_auth_token
            else:
                error_text = response.text
                logger.error(f"Failed to get API auth token: {error_text}")

                # Check if it's a JWT expiry error
                if "Jwt expired" in error_text or "expired" in error_text.lower():
                    raise HTTPException(
                        status_code=401,
                        detail="Your session has expired. Please log in again to continue."
                    )
                else:
                    raise HTTPException(
                        status_code=401,
                        detail="Failed to authenticate with WagonHR API. Please try logging in again."
                    )
        except requests.RequestException as e:
            logger.error(f"Failed to call WagonHR API: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to connect to WagonHR API")

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error processing access token: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process access token")


# Routes
@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Welcome to the HR AI Service Desk API"}

@app.get("/health")
async def health_check():
    """
    Simple health check endpoint for monitoring and deployment.

    Returns:
        Dict[str, Any]: Simple health status with version and docs info
    """
    try:
        # Check critical services quickly
        is_healthy = True

        # Quick HR application check
        try:
            if not (hr_app and hasattr(hr_app, 'runner_service')):
                is_healthy = False
        except Exception:
            is_healthy = False

        # Simple response format with dynamic version (similar to Java's getImplementationVersion)
        def get_package_version():
            try:
                # Python equivalent of Java's getImplementationVersion()
                from importlib.metadata import version, PackageNotFoundError
                return f"v{version('hr-solution')}"  # Replace with your actual package name
            except (PackageNotFoundError, ImportError):
                pass

            try:
                # Alternative: try importlib_metadata for older Python versions
                from importlib_metadata import version, PackageNotFoundError
                return f"v{version('hr-solution')}"
            except (PackageNotFoundError, ImportError):
                pass

            try:
                # Try to get version from __version__ attribute in main module
                import __main__
                if hasattr(__main__, '__version__'):
                    return f"v{__main__.__version__}"
            except:
                pass

            # Fallback to environment variable
            if os.environ.get("API_VERSION"):
                return os.environ.get("API_VERSION")

            # Fallback to VERSION file
            try:
                with open("VERSION", "r") as f:
                    return f.read().strip()
            except FileNotFoundError:
                pass

            # Final fallback: timestamp-based version
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y.%m.%d")
            return f"v{timestamp}"

        api_version = get_package_version()
        response_data = {
            "version": api_version,
            "docs": {
                "url": "/docs",
                "status": "Live" if is_healthy else "Degraded"
            }
        }

        # Return appropriate status code
        status_code = 200 if is_healthy else 503

        return JSONResponse(
            content=response_data,
            status_code=status_code
        )

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        # Use same package version logic for error response
        def get_package_version():
            try:
                from importlib.metadata import version, PackageNotFoundError
                return f"v{version('hr-solution')}"
            except (PackageNotFoundError, ImportError):
                pass
            if os.environ.get("API_VERSION"):
                return os.environ.get("API_VERSION")
            try:
                with open("VERSION", "r") as f:
                    return f.read().strip()
            except FileNotFoundError:
                pass
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y.%m.%d")
            return f"v{timestamp}"

        api_version = get_package_version()
        return JSONResponse(
            content={
                "version": api_version,
                "docs": {
                    "url": "/docs",
                    "status": "Error"
                }
            },
            status_code=503
        )



@app.post("/message", response_model=MessageResponse)
async def process_message(
    request: MessageRequest,
    authorization: Optional[str] = Header(None)
):
    """Process a message and return the final response."""
    try:
        # Get access token from request body or headers
        access_token = request.token
        if not access_token and authorization:
            access_token = authorization.replace("Bearer ", "")

        if not access_token:
            raise HTTPException(status_code=401, detail="Access token is required")

        # Get API Auth token from access_token and validate it
        print("get the access token from api token")
        api_auth_token = get_api_auth_token(access_token)

        # Extract user information from token if not provided
        if not request.user_id and api_auth_token.get("user_id"):
            request.user_id = api_auth_token["user_id"]

        # Check if session_id is provided, if not create a new session
        if not request.session_id:
            print("No session_id provided, creating new session")
            session_data = await hr_app.create_session(
                user_id=request.user_id,
                session_id=None,  # Auto-generate session ID
                initial_state={"api_auth_token": api_auth_token}
            )
            request.session_id = session_data["session_id"]
            print(f"Created new session: {request.session_id}")
        else:
            # Verify session exists, if not create a new one
            try:
                session = await hr_app.runner_service.get_session(request.user_id, request.session_id)
                if not session:
                    print(f"Session {request.session_id} not found, creating new session")
                    session_data = await hr_app.create_session(
                        user_id=request.user_id,
                        session_id=None,  # Auto-generate session ID
                        initial_state={"api_auth_token": api_auth_token}
                    )
                    request.session_id = session_data["session_id"]
                    print(f"Created new session: {request.session_id}")
            except Exception as e:
                print(f"Error checking session, creating new one: {str(e)}")
                session_data = await hr_app.create_session(
                    user_id=request.user_id,
                    session_id=None,
                    initial_state={"api_auth_token": api_auth_token}
                )
                request.session_id = session_data["session_id"]
                print(f"Created new session: {request.session_id}")
        
        # Get chat history context before processing
        from utils.context_manager import EnhancedContextManager
        context_manager = EnhancedContextManager()
        conversation_context = context_manager.get_conversation_context(request.session_id, last_n_turns=3)
        
        # Enhance message with context if available
        enhanced_message = request.message
        if conversation_context and conversation_context != "No previous conversation history.":
            enhanced_message = f"Previous conversation context:\n{conversation_context}\n\nCurrent message: {request.message}"
            print(f"Enhanced message with context for session {request.session_id}")
        
        response = await hr_app.process_message(
            message=enhanced_message,
            user_id=request.user_id,
            session_id=request.session_id,
            api_auth_token=api_auth_token
        )

        print("response from process message: ", response)

        if response.get("type") == "error":
            # Return only the error message as content, not the full JSON
            error_message = response.get("content", "I'm experiencing technical difficulties. Please try again in a few moments.")
            return MessageResponse(
                content=error_message,
                type="message",  # Change to message type so it displays normally
                is_final=True
            )

        # Record the conversation turn in context manager
        try:
            context_manager.record_conversation_turn(
                session_id=request.session_id,
                user_message=request.message,
                agent_name="hr_root_agent",  # Default to root agent
                agent_response=response.get("content", ""),
                success=response.get("type") != "error"
            )
        except Exception as e:
            print(f"Error recording conversation turn: {str(e)}")

        return MessageResponse(
            content=response.get("content"),
            type=response.get("type", "message"),
            is_final=response.get("is_final", True),
            session_id=request.session_id  # Include session_id in response
        )
    except HTTPException:
        # Re-raise HTTP exceptions (like 401 Unauthorized)
        raise
    except Exception as e:
        logger.error(f"Error processing message: {str(e)}")
        # Provide user-friendly error message instead of technical details
        raise HTTPException(status_code=500, detail="I'm experiencing technical difficulties. Please try again in a few moments.")

# @app.post("/message/stream")
# async def process_message_stream(request: MessageRequest):
#     """Process a message and stream the responses."""
#     async def event_generator():
#         try:
#             async for event in hr_app.process_message_stream(
#                 message=request.message,
#                 user_id=request.user_id,
#                 session_id=request.session_id
#             ):
#                 if event.get("type") == "error":
#                     yield {
#                         "event": "error",
#                         "data": event.get("message", "Unknown error")
#                     }
#                     return
#
#                 yield {
#                     "event": "message",
#                     "data": event.get("content", ""),
#                     "id": event.get("id", ""),
#                     "retry": 1000
#                 }
#
#                 if event.get("is_final", False):
#                     yield {
#                         "event": "done",
#                         "data": ""
#                     }
#         except Exception as e:
#             logger.error(f"Error streaming message: {str(e)}")
#             yield {
#                 "event": "error",
#                 "data": str(e)
#             }
#
#     # return EventSourceResponse(event_generator())
#     return JSONResponse({"message": "Streaming not available"})


@app.post("/session", response_model=Dict[str, str])
async def create_session(request: SessionRequest):
    """Create a new session."""
    try:
        session_data = await hr_app.create_session(
            user_id=request.user_id,
            session_id=request.session_id,
            initial_state=request.initial_state
        )
        return session_data
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}")
        raise HTTPException(status_code=500, detail="I'm having trouble creating a new session. Please try again.")


@app.get("/session/{session_id}")
async def get_session_info(session_id: str, user_id: str):
    """Get session information including chat history."""
    try:
        # Get session from runner service
        session = await hr_app.runner_service.get_session(user_id, session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get chat history from context manager
        from utils.context_manager import EnhancedContextManager
        context_manager = EnhancedContextManager()
        context = context_manager.get_or_create_context(session_id)
        
        # Get conversation context
        conversation_context = context_manager.get_conversation_context(session_id)
        context_summary = context_manager.get_context_summary(session_id)
        
        return {
            "session_id": session_id,
            "user_id": user_id,
            "session_state": session.state if hasattr(session, 'state') else {},
            "conversation_history": conversation_context,
            "context_summary": context_summary,
            "active_sessions": hr_app.get_active_sessions()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session info: {str(e)}")
        raise HTTPException(status_code=500, detail="I'm having trouble retrieving session information. Please try again.")


@app.delete("/session/{session_id}")
async def delete_session(session_id: str):
    """Delete a session and clear its context. No user_id required."""
    try:
        # Clear context from context manager
        from utils.context_manager import EnhancedContextManager
        context_manager = EnhancedContextManager()
        context_manager.clear_context(session_id)

        # Remove from active sessions
        if session_id in hr_app.runner_service.active_sessions:
            del hr_app.runner_service.active_sessions[session_id]

        return {"message": f"Session {session_id} deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/sessions")
async def list_sessions():
    """List all active sessions."""
    try:
        # get_active_sessions() returns a dict of session_id -> metadata, convert to array for frontend
        active_sessions = hr_app.get_active_sessions()
        sessions = []
        for session_id, meta in active_sessions.items():
            sessions.append({
                "session_id": session_id,
                "title": meta.get("title") or "New Chat"
            })
        return {
            "sessions": sessions,
            "total_sessions": len(sessions)
        }
    except Exception as e:
        logger.error(f"Error listing sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=int(os.environ.get("PORT", 8081)),
        reload=False
    )

@app.get("/session/{session_id}/messages")
async def get_session_messages(session_id: str, user_id: Optional[str] = None):
    """
    Return the conversation history/messages for a session.
    Strictly read-only: never clears or modifies any context or session data.
    Returns a list of messages (empty if none), loading from persistent storage if not in memory.
    """
    try:
        from utils.context_manager import EnhancedContextManager
        import os
        import json
        context_manager = EnhancedContextManager()
        conversation_context = context_manager.get_conversation_context(session_id)
        logger.info(f"[DEBUG] In-memory conversation_context for session {session_id}: {conversation_context}")
        # If not found in memory, try to load from persistent storage (sessions.json)
        if not conversation_context or conversation_context == "No previous conversation history.":
            sessions_file = os.path.join(os.path.dirname(__file__), "sessions.json")
            logger.info(f"[DEBUG] Attempting to load session {session_id} from {sessions_file}")
            if os.path.exists(sessions_file):
                with open(sessions_file, "r") as f:
                    try:
                        sessions_data = json.load(f)
                        logger.info(f"[DEBUG] sessions.json keys: {list(sessions_data.keys())}")
                        session_data = sessions_data.get(session_id)
                        logger.info(f"[DEBUG] Raw session_data for {session_id}: {session_data}")
                        # Try 'messages' (current), then 'conversation_history' (legacy), then fallback
                        if session_data:
                            if "messages" in session_data:
                                conversation_context = session_data["messages"]
                                logger.info(f"[DEBUG] Found 'messages' in session_data: {conversation_context}")
                            elif "conversation_history" in session_data:
                                conversation_context = session_data["conversation_history"]
                                logger.info(f"[DEBUG] Found 'conversation_history' in session_data: {conversation_context}")
                            elif isinstance(session_data, list):
                                conversation_context = session_data
                                logger.info(f"[DEBUG] session_data is a list: {conversation_context}")
                            elif isinstance(session_data, dict):
                                # Try to extract any list value
                                for k, v in session_data.items():
                                    if isinstance(v, list):
                                        conversation_context = v
                                        logger.info(f"[DEBUG] Found list under key '{k}' in session_data: {conversation_context}")
                                        break
                    except Exception as e:
                        logger.error(f"Error loading sessions.json: {str(e)}")
        # If conversation_context is a dict with 'messages' or 'conversation_history', extract it
        if isinstance(conversation_context, dict):
            if "messages" in conversation_context:
                conversation_context = conversation_context["messages"]
                logger.info(f"[DEBUG] Extracted 'messages' from dict: {conversation_context}")
            elif "conversation_history" in conversation_context:
                conversation_context = conversation_context["conversation_history"]
                logger.info(f"[DEBUG] Extracted 'conversation_history' from dict: {conversation_context}")
        logger.info(f"[DEBUG] Final conversation_context to return for session {session_id}: {conversation_context}")
        # Always return the messages as-is, never clear or modify any context/session data
        if isinstance(conversation_context, list):
            return {"messages": conversation_context}
        else:
            return {"messages": [conversation_context] if conversation_context else []}
    except Exception as e:
        logger.error(f"Error getting session messages: {str(e)}")
        raise HTTPException(status_code=500, detail="I'm having trouble retrieving session messages. Please try again.")
