
stages:
  - deploy_dev

deploy_dev:
  stage: deploy_dev
  image: docker:24.0.6
  services:
    - docker:24.0.6-dind
  before_script: |
    apk add --no-cache curl jq python3 py3-pip
    pip3 install --no-cache-dir awscli
    aws sts assume-role --role-arn $AWS_ROLE_ARN_dev --role-session-name GitLabSession | jq -r '.Credentials' > assumed_role.json
    export AWS_ACCESS_KEY_ID=$(jq -r '.AccessKeyId' assumed_role.json)
    export AWS_SECRET_ACCESS_KEY=$(jq -r '.SecretAccessKey' assumed_role.json)
    export AWS_SESSION_TOKEN=$(jq -r '.SessionToken' assumed_role.json)
    export AWS_DEFAULT_REGION=$AWS_REGION
    echo "Fetching secret from AWS Secrets Manager..."
    export SECRET_VALUE=$(aws secretsmanager get-secret-value --secret-id "${SECRET_MANAGER}" --region $AWS_REGION --query SecretString --output text)
    echo "SECRET_VALUE: $SECRET_VALUE"
    echo "Extracting Google Application Credentials..."
    export GOOGLE_APPLICATION_CREDENTIALS_JSON=$(echo "$SECRET_VALUE" | jq -r '.GOOGLE_APPLICATION_CREDENTIALS' | base64 -d)    
    echo "GOOGLE_APPLICATION_CREDENTIALS_JSON: $GOOGLE_APPLICATION_CREDENTIALS_JSON"
    export GOOGLE_API_KEY=$(echo $SECRET_VALUE | jq -r '.GOOGLE_API_KEY')
    echo "GOOGLE_API_KEY: $GOOGLE_API_KEY"
    export GOOGLE_GENAI_USE_VERTEXAI=$(echo $SECRET_VALUE | jq -r '.GOOGLE_GENAI_USE_VERTEXAI')
    echo "GOOGLE_GENAI_USE_VERTEXAI: $GOOGLE_GENAI_USE_VERTEXAI"
    export GOOGLE_CLOUD_PROJECT=$(echo $SECRET_VALUE | jq -r '.GOOGLE_CLOUD_PROJECT')
    echo "GOOGLE_CLOUD_PROJECT: $GOOGLE_CLOUD_PROJECT"
    export GOOGLE_CLOUD_LOCATION=$(echo $SECRET_VALUE | jq -r '.GOOGLE_CLOUD_LOCATION')
    echo "GOOGLE_CLOUD_LOCATION: $GOOGLE_CLOUD_LOCATION"
    export OPENSEARCH_HOST=$(echo $SECRET_VALUE | jq -r '.OPENSEARCH_HOST')
    echo "OPENSEARCH_HOST: $OPENSEARCH_HOST"
    export OPENSEARCH_USER=$(echo $SECRET_VALUE | jq -r '.OPENSEARCH_USER')
    echo "OPENSEARCH_USER: $OPENSEARCH_USER"
    export OPENSEARCH_PASS=$(echo $SECRET_VALUE | jq -r '.OPENSEARCH_PASS')
    echo "OPENSEARCH_PASS: $OPENSEARCH_PASS"
    export POLICY_INDEX_NAME=$(echo $SECRET_VALUE | jq -r '.POLICY_INDEX_NAME')
    echo "POLICY_INDEX_NAME: $POLICY_INDEX_NAME"
    export GOOGLE_EMBEDDING_MODEL=$(echo $SECRET_VALUE | jq -r '.GOOGLE_EMBEDDING_MODEL')
    echo "GOOGLE_EMBEDDING_MODEL: $GOOGLE_EMBEDDING_MODEL"
    export WAGONHR_API_URL=$(echo $SECRET_VALUE | jq -r '.WAGONHR_API_URL')
    echo "WAGONHR_API_URL: $WAGONHR_API_URL"
    export HR_TENANT_ID=$(echo $SECRET_VALUE | jq -r '.HR_TENANT_ID')
    echo "HR_TENANT_ID: $HR_TENANT_ID"
    export HR_ENTITY_ID=$(echo $SECRET_VALUE | jq -r '.HR_ENTITY_ID')
    echo "HR_ENTITY_ID: $HR_CLIENT_ID"
    GITLABTOKEN=$(echo $SECRET_VALUE | jq -r '.gitlabDeployToken')
    IMAGE_TAG="$(echo $CI_COMMIT_SHA | head -c 8)"
    echo $GITLABTOKEN
    echo $IMAGE_TAG
  script: |
    aws ecr get-login-password --region "${AWS_REGION}" | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    docker build --no-cache -t $APP_NAME .
    docker tag $APP_NAME:latest $DOCKER_REGISTRY/$APP_NAME:$IMAGE_TAG
    docker tag $APP_NAME:latest $DOCKER_REGISTRY/$APP_NAME:latest
    docker push $DOCKER_REGISTRY/$APP_NAME:$IMAGE_TAG
    docker push $DOCKER_REGISTRY/$APP_NAME:latest
    echo $DOCKER_REGISTRY/$APP_NAME:$IMAGE_TAG
    TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_DEFINITION_NAME" --region "${AWS_REGION}")
    NEW_CONTAINER_DEFINTIION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$DOCKER_REGISTRY/$APP_NAME:$IMAGE_TAG" \
      --arg GOOGLE_CREDS "$GOOGLE_APPLICATION_CREDENTIALS" \
      --arg GOOGLE_API_KEY "$GOOGLE_API_KEY" \
      --arg GOOGLE_GENAI_USE_VERTEXAI "$GOOGLE_GENAI_USE_VERTEXAI" \
      --arg GOOGLE_CLOUD_PROJECT "$GOOGLE_CLOUD_PROJECT" \
      --arg GOOGLE_CLOUD_LOCATION "$GOOGLE_CLOUD_LOCATION" \
      --arg OPENSEARCH_HOST "$OPENSEARCH_HOST" \
      --arg OPENSEARCH_USER "$OPENSEARCH_USER" \
      --arg OPENSEARCH_PASS "$OPENSEARCH_PASS" \
      --arg POLICY_INDEX_NAME "$POLICY_INDEX_NAME" \
      --arg GOOGLE_EMBEDDING_MODEL "$GOOGLE_EMBEDDING_MODEL" \
      '.taskDefinition | .containerDefinitions[0].image = $IMAGE | .containerDefinitions[0].environment += [
        {"name": "GOOGLE_APPLICATION_CREDENTIALS", "value": $GOOGLE_CREDS},
        {"name": "GOOGLE_API_KEY", "value": $GOOGLE_API_KEY},
        {"name": "GOOGLE_GENAI_USE_VERTEXAI", "value": $GOOGLE_GENAI_USE_VERTEXAI},
        {"name": "GOOGLE_CLOUD_PROJECT", "value": $GOOGLE_CLOUD_PROJECT},
        {"name": "GOOGLE_CLOUD_LOCATION", "value": $GOOGLE_CLOUD_LOCATION},
        {"name": "OPENSEARCH_HOST", "value": $OPENSEARCH_HOST},
        {"name": "OPENSEARCH_USER", "value": $OPENSEARCH_USER},
        {"name": "OPENSEARCH_PASS", "value": $OPENSEARCH_PASS},
        {"name": "POLICY_INDEX_NAME", "value": $POLICY_INDEX_NAME},
        {"name": "GOOGLE_EMBEDDING_MODEL", "value": $GOOGLE_EMBEDDING_MODEL}
      ] | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
    echo "${NEW_CONTAINER_DEFINTIION}"
    echo "Registering new container definition..."
    aws ecs register-task-definition --region "${AWS_REGION}" --cli-input-json "$NEW_CONTAINER_DEFINTIION" --tags key=Project,value=hrms-agent key=Environment,value=$ENV_NAME
    echo "Updating the service..."
    echo "${TASK_DEFINITION_NAME}"
    aws ecs update-service --cluster "${CLUSTER_NAME}" --service "${SERVICE_NAME}" --task-definition "${TASK_DEFINITION_NAME}" --propagate-tags TASK_DEFINITION
  variables:
    ENV_NAME: Dev
    AWS_ROLE_ARN: $AWS_ROLE_ARN_dev
    SECRET_MANAGER: hrms-agent-dev-secret
    TASK_DEFINITION_NAME: ecs-hrms-agent-backend-hr-solution-task-dev
    CLUSTER_NAME: ecs-hrms-agent-dev-cluster
    SERVICE_NAME: ecs-hrms-agent-backend-hr-solution-service-dev
    APP_NAME: ecr-hrms-agent-backend-hr-solution-dev
    AWS_REGION: us-east-2
    DOCKER_REGISTRY: 613929911702.dkr.ecr.us-east-2.amazonaws.com
    DOCKER_TLS_CERTDIR: ""
    DOCKER_HOST: "tcp://docker:2375"
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      