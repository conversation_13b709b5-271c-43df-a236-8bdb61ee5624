"""
HR Policy database models.

This module contains the database models for HR policies.
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any

from sqlalchemy import Column, String, Text, DateTime, JSON, ForeignKey, Index, Integer, Float
from sqlalchemy.dialects.postgresql import UUID, ARRAY, JSONB
from sqlalchemy.orm import relationship

from utils.db_config import Base


class HRPolicy(Base):
    """HR Policy model."""

    __tablename__ = "hr_policies"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    policy_id = Column(String(50), unique=True, nullable=False, index=True)
    title = Column(String(255), nullable=False)
    category = Column(String(100), nullable=False, index=True)
    subcategory = Column(String(100), nullable=True, index=True)
    content = Column(Text, nullable=False)
    summary = Column(Text, nullable=True)
    effective_date = Column(DateTime, nullable=False)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    metadata = Column(JSONB, nullable=True)

    # Create indexes for efficient querying
    __table_args__ = (
        Index('idx_hr_policies_category_subcategory', 'category', 'subcategory'),
        Index('idx_hr_policies_effective_date', 'effective_date'),
        {'schema': 'itsm'}  # Use the ITSM schema
    )

    def __repr__(self):
        return f"<HRPolicy(policy_id='{self.policy_id}', title='{self.title}')>"


class HRPolicyVector(Base):
    """HR Policy Vector model for storing embeddings."""

    __tablename__ = "hr_policy_vectors"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    policy_id = Column(String(50), ForeignKey('itsm.hr_policies.policy_id'), nullable=False)
    chunk_index = Column(Integer, nullable=False)
    chunk_text = Column(Text, nullable=False)
    embedding = Column(ARRAY(Float), nullable=False)  # Store vector embeddings as array
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationship to the parent policy
    policy = relationship("HRPolicy", back_populates="vectors")

    # Create indexes for efficient querying
    __table_args__ = (
        Index('idx_hr_policy_vectors_policy_id', 'policy_id'),
        {'schema': 'itsm'}  # Use the ITSM schema
    )

    def __repr__(self):
        return f"<HRPolicyVector(policy_id='{self.policy_id}', chunk_index={self.chunk_index})>"


# Add the relationship to HRPolicy
HRPolicy.vectors = relationship("HRPolicyVector", back_populates="policy", cascade="all, delete-orphan")
