"""
Audit trail models.

This module contains the database models for the audit trail system.
"""

import json
import datetime
from typing import Dict, Any, Optional
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship

from utils.db_config import Base


class AgentRegistry(Base):
    """Agent registry model.

    Stores information about registered agents.
    """
    __tablename__ = "agent_registry"

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    config = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    modified_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # Relationships
    prompts = relationship("AgentPrompt", back_populates="agent")
    tools = relationship("AgentTool", back_populates="agent")
    sessions = relationship("Session", back_populates="agent")
    audit_events = relationship("AuditEvent", back_populates="agent")


class AgentPrompt(Base):
    """Agent prompt model.

    Stores prompts used by agents.
    """
    __tablename__ = "agent_prompt"

    id = Column(Integer, primary_key=True)
    agent_id = Column(Integer, ForeignKey("agent_registry.id"), nullable=False)
    prompt_type = Column(String(50), nullable=False)
    prompt_text = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)

    # Relationships
    agent = relationship("AgentRegistry", back_populates="prompts")


class AgentTool(Base):
    """Agent tool model.

    Stores tools available to agents.
    """
    __tablename__ = "agent_tool"

    id = Column(Integer, primary_key=True)
    agent_id = Column(Integer, ForeignKey("agent_registry.id"), nullable=False)
    tool_name = Column(String(100), nullable=False)
    tool_description = Column(Text, nullable=True)
    tool_config = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)

    # Relationships
    agent = relationship("AgentRegistry", back_populates="tools")


class Session(Base):
    """Session model.

    Stores information about user sessions.
    """
    __tablename__ = "session"

    id = Column(String(36), primary_key=True)
    user_identifier = Column(String(100), nullable=False)
    agent_id = Column(Integer, ForeignKey("agent_registry.id"), nullable=False)
    start_time = Column(DateTime, default=datetime.datetime.utcnow)
    end_time = Column(DateTime, nullable=True)

    # Relationships
    agent = relationship("AgentRegistry", back_populates="sessions")
    messages = relationship("SessionMessage", back_populates="session")
    audit_events = relationship("AuditEvent", back_populates="session")


class SessionMessage(Base):
    """Session message model.

    Stores messages exchanged during a session.
    """
    __tablename__ = "session_message"

    id = Column(Integer, primary_key=True)
    session_id = Column(String(36), ForeignKey("session.id"), nullable=False)
    actor_type = Column(String(50), nullable=False)  # 'user', 'agent', 'system'
    message = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=datetime.datetime.utcnow)

    # Relationships
    session = relationship("Session", back_populates="messages")


class AuditEvent(Base):
    """Audit event model.

    Stores audit events for agent actions.
    """
    __tablename__ = "audit_event"

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(36), ForeignKey("session.id"), nullable=False)
    agent_id = Column(Integer, ForeignKey("agent_registry.id"), nullable=False)
    user_identifier = Column(String(100), nullable=False)
    actor_type = Column(String(50), nullable=False)  # 'agent', 'tool', 'user', 'system'
    actor_id = Column(String(100), nullable=True)
    action_type = Column(String(100), nullable=False)  # 'tool_call', 'response', 'error', etc.
    entity_type = Column(String(100), nullable=True)  # 'password', 'ticket', 'service', etc.
    entity_id = Column(String(100), nullable=True)
    status = Column(String(50), nullable=False)  # 'success', 'error', 'pending'
    error_message = Column(Text, nullable=True)
    context_data = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    modified_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # Relationships
    session = relationship("Session", back_populates="audit_events")
    agent = relationship("AgentRegistry", back_populates="audit_events")
