"""
OTP models.

This module contains the database models for OTP storage.
"""

import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean
from utils.db_config import Base


class OTPCode(Base):
    """OTP code model.

    Stores OTP codes for user verification with audit trail.
    """
    __tablename__ = "otp_codes"

    id = Column(Integer, primary_key=True)
    user_identifier = Column(String(100), nullable=False, index=True)
    otp_code = Column(String(10), nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    expires_at = Column(DateTime, nullable=False)
    purpose = Column(String(50), default="verification")
    attempts = Column(Integer, default=0)
    verified = Column(Boolean, default=False)

    # Audit fields
    created_by = Column(String(100), nullable=True)  # User or system that created the OTP
    verified_at = Column(DateTime, nullable=True)    # When the OTP was verified
    last_attempt_at = Column(DateTime, nullable=True)  # When the last verification attempt was made

    # Status tracking
    status = Column(String(20), default="active")  # active, verified, expired, invalidated
    is_latest = Column(Boolean, default=True)      # Flag to mark the latest OTP for a user

    def is_expired(self) -> bool:
        """Check if the OTP is expired.

        Returns:
            bool: True if expired, False otherwise
        """
        return datetime.datetime.utcnow() > self.expires_at

    def is_valid(self, input_otp: str) -> bool:
        """Check if the input OTP matches and is not expired.

        Args:
            input_otp (str): The OTP to validate

        Returns:
            bool: True if valid, False otherwise
        """
        return not self.is_expired() and self.otp_code == input_otp and self.status == "active" and self.is_latest

    def mark_as_verified(self) -> None:
        """Mark the OTP as verified.
        """
        self.verified = True
        self.verified_at = datetime.datetime.utcnow()
        self.status = "verified"

    def mark_as_expired(self) -> None:
        """Mark the OTP as expired.
        """
        self.status = "expired"

    def mark_as_invalidated(self) -> None:
        """Mark the OTP as invalidated (e.g., when a new OTP is generated).
        """
        self.status = "invalidated"
        self.is_latest = False

    def record_attempt(self) -> None:
        """Record a verification attempt.
        """
        self.attempts += 1
        self.last_attempt_at = datetime.datetime.utcnow()
