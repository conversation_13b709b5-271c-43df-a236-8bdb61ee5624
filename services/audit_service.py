"""
Audit service module.

This module provides services for recording and retrieving audit events.
"""

import json
import logging
import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy.orm import Session as DbSession
from sqlalchemy.exc import SQLAlchemyError

from models.audit_models import (
    AgentRegistry,
    AgentPrompt,
    AgentTool,
    Session,
    SessionMessage,
    AuditEvent
)
from utils.db_config import get_db_session

# Set up logger
logger = logging.getLogger(__name__)


class AuditService:
    """Service for managing audit events."""

    @staticmethod
    def register_agent(
        name: str,
        description: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None,
        db: Optional[DbSession] = None
    ) -> Optional[int]:
        """Register a new agent.

        Args:
            name: The name of the agent.
            description: Optional description of the agent.
            config: Optional configuration for the agent.
            db: Optional database session.

        Returns:
            The ID of the registered agent, or None if registration failed.
        """
        close_db = False
        if db is None:
            db = get_db_session()
            close_db = True

        try:
            # Check if agent already exists
            existing_agent = db.query(AgentRegistry).filter_by(name=name).first()
            if existing_agent:
                logger.info(f"Agent {name} already registered with ID {existing_agent.id}")
                return existing_agent.id

            # Create new agent
            agent = AgentRegistry(
                name=name,
                description=description,
                config=config
            )
            db.add(agent)
            db.commit()
            db.refresh(agent)

            logger.info(f"Agent {name} registered with ID {agent.id}")
            return agent.id
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error registering agent {name}: {str(e)}")
            return None
        finally:
            if close_db:
                db.close()

    @staticmethod
    def register_agent_prompt(
        agent_id: int,
        prompt_type: str,
        prompt_text: str,
        db: Optional[DbSession] = None
    ) -> Optional[int]:
        """Register a prompt for an agent.

        Args:
            agent_id: The ID of the agent.
            prompt_type: The type of prompt (e.g., 'system', 'user').
            prompt_text: The text of the prompt.
            db: Optional database session.

        Returns:
            The ID of the registered prompt, or None if registration failed.
        """
        close_db = False
        if db is None:
            db = get_db_session()
            close_db = True

        try:
            # Check if a prompt of the same type already exists for this agent
            existing_prompt = db.query(AgentPrompt).filter_by(
                agent_id=agent_id,
                prompt_type=prompt_type
            ).first()

            if existing_prompt:
                # Update existing prompt
                existing_prompt.prompt_text = prompt_text
                db.commit()
                logger.info(f"Updated existing prompt for agent {agent_id} with ID {existing_prompt.id}")
                return existing_prompt.id
            else:
                # Create new prompt
                prompt = AgentPrompt(
                    agent_id=agent_id,
                    prompt_type=prompt_type,
                    prompt_text=prompt_text
                )
                db.add(prompt)
                db.commit()
                db.refresh(prompt)

                logger.info(f"Prompt registered for agent {agent_id} with ID {prompt.id}")
                return prompt.id
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error registering prompt for agent {agent_id}: {str(e)}")
            return None
        finally:
            if close_db:
                db.close()

    @staticmethod
    def register_agent_tool(
        agent_id: int,
        tool_name: str,
        tool_description: Optional[str] = None,
        tool_config: Optional[Dict[str, Any]] = None,
        db: Optional[DbSession] = None
    ) -> Optional[int]:
        """Register a tool for an agent.

        Args:
            agent_id: The ID of the agent.
            tool_name: The name of the tool.
            tool_description: Optional description of the tool.
            tool_config: Optional configuration for the tool.
            db: Optional database session.

        Returns:
            The ID of the registered tool, or None if registration failed.
        """
        close_db = False
        if db is None:
            db = get_db_session()
            close_db = True

        try:
            # Create new tool
            tool = AgentTool(
                agent_id=agent_id,
                tool_name=tool_name,
                tool_description=tool_description,
                tool_config=tool_config
            )
            db.add(tool)
            db.commit()
            db.refresh(tool)

            logger.info(f"Tool {tool_name} registered for agent {agent_id} with ID {tool.id}")
            return tool.id
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error registering tool {tool_name} for agent {agent_id}: {str(e)}")
            return None
        finally:
            if close_db:
                db.close()

    @staticmethod
    def create_session(
        session_id: str,
        user_identifier: str,
        agent_id: int,
        db: Optional[DbSession] = None
    ) -> bool:
        """Create a new session.

        Args:
            session_id: The ID of the session.
            user_identifier: The identifier of the user.
            agent_id: The ID of the agent.
            db: Optional database session.

        Returns:
            True if the session was created successfully, False otherwise.
        """
        close_db = False
        if db is None:
            db = get_db_session()
            close_db = True

        try:
            # Check if session already exists
            existing_session = db.query(Session).filter_by(id=session_id).first()
            if existing_session:
                logger.info(f"Session {session_id} already exists")
                return True

            # Create new session
            session = Session(
                id=session_id,
                user_identifier=user_identifier,
                agent_id=agent_id
            )
            db.add(session)
            db.commit()

            logger.info(f"Session {session_id} created for user {user_identifier} with agent {agent_id}")
            return True
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error creating session {session_id}: {str(e)}")
            return False
        finally:
            if close_db:
                db.close()

    @staticmethod
    def end_session(
        session_id: str,
        db: Optional[DbSession] = None
    ) -> bool:
        """End a session.

        Args:
            session_id: The ID of the session.
            db: Optional database session.

        Returns:
            True if the session was ended successfully, False otherwise.
        """
        close_db = False
        if db is None:
            db = get_db_session()
            close_db = True

        try:
            # Get session
            session = db.query(Session).filter_by(id=session_id).first()
            if not session:
                logger.error(f"Session {session_id} not found")
                return False

            # Update end time
            session.end_time = datetime.datetime.utcnow()
            db.commit()

            logger.info(f"Session {session_id} ended")
            return True
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error ending session {session_id}: {str(e)}")
            return False
        finally:
            if close_db:
                db.close()

    @staticmethod
    def add_session_message(
        session_id: str,
        actor_type: str,
        message: str,
        db: Optional[DbSession] = None
    ) -> Optional[int]:
        """Add a message to a session.

        Args:
            session_id: The ID of the session.
            actor_type: The type of actor (e.g., 'user', 'agent').
            message: The message text.
            db: Optional database session.

        Returns:
            The ID of the message, or None if adding failed.
        """
        close_db = False
        if db is None:
            db = get_db_session()
            close_db = True

        try:
            # Create new message
            session_message = SessionMessage(
                session_id=session_id,
                actor_type=actor_type,
                message=message
            )
            db.add(session_message)
            db.commit()
            db.refresh(session_message)

            return session_message.id
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error adding message to session {session_id}: {str(e)}")
            return None
        finally:
            if close_db:
                db.close()

    @staticmethod
    def record_audit_event(
        session_id: str,
        agent_id: int,
        user_identifier: str,
        actor_type: str,
        action_type: str,
        status: str,
        actor_id: Optional[str] = None,
        entity_type: Optional[str] = None,
        entity_id: Optional[str] = None,
        error_message: Optional[str] = None,
        context_data: Optional[Dict[str, Any]] = None,
        db: Optional[DbSession] = None
    ) -> Optional[int]:
        """Record an audit event.

        Args:
            session_id: The ID of the session.
            agent_id: The ID of the agent.
            user_identifier: The identifier of the user.
            actor_type: The type of actor (e.g., 'agent', 'tool').
            action_type: The type of action (e.g., 'tool_call', 'response').
            status: The status of the event (e.g., 'success', 'error').
            actor_id: Optional ID of the actor.
            entity_type: Optional type of entity affected.
            entity_id: Optional ID of entity affected.
            error_message: Optional error message.
            context_data: Optional context data.
            db: Optional database session.

        Returns:
            The ID of the audit event, or None if recording failed.
        """
        close_db = False
        if db is None:
            db = get_db_session()
            close_db = True

        try:
            # Create new audit event
            audit_event = AuditEvent(
                session_id=session_id,
                agent_id=agent_id,
                user_identifier=user_identifier,
                actor_type=actor_type,
                actor_id=actor_id,
                action_type=action_type,
                entity_type=entity_type,
                entity_id=entity_id,
                status=status,
                error_message=error_message,
                context_data=context_data
            )

            db.add(audit_event)

            db.commit()

            db.refresh(audit_event)

            return audit_event.id
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"SQLAlchemy error recording audit event: {str(e)}")
            logger.error(f"Error details: {type(e).__name__}")
            # Log the traceback for more detailed debugging
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

            # Check if it's a connection error
            if "connection" in str(e).lower():
                logger.error(f"Database connection error detected. Please check your PostgreSQL configuration.")
                logger.error(f"Make sure the PostgreSQL server is running and accessible.")
                logger.error(f"Check that the user 'rahula' exists and has the correct permissions.")
                logger.error(f"You may need to run: CREATE ROLE rahula WITH LOGIN PASSWORD 'postgres';")
                logger.error(f"And then: GRANT ALL PRIVILEGES ON DATABASE postgres TO rahula;")

            return None
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error recording audit event: {str(e)}")
            logger.error(f"Error type: {type(e).__name__}")
            # Log the traceback for more detailed debugging
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
        finally:
            if close_db:
                db.close()

    @staticmethod
    def get_audit_events(
        session_id: Optional[str] = None,
        agent_id: Optional[int] = None,
        user_identifier: Optional[str] = None,
        actor_type: Optional[str] = None,
        action_type: Optional[str] = None,
        status: Optional[str] = None,
        start_time: Optional[datetime.datetime] = None,
        end_time: Optional[datetime.datetime] = None,
        limit: int = 100,
        offset: int = 0,
        db: Optional[DbSession] = None
    ) -> List[Dict[str, Any]]:
        """Get audit events based on filters.

        Args:
            session_id: Optional session ID filter.
            agent_id: Optional agent ID filter.
            user_identifier: Optional user identifier filter.
            actor_type: Optional actor type filter.
            action_type: Optional action type filter.
            status: Optional status filter.
            start_time: Optional start time filter.
            end_time: Optional end time filter.
            limit: Maximum number of events to return.
            offset: Offset for pagination.
            db: Optional database session.

        Returns:
            List of audit events as dictionaries.
        """
        close_db = False
        if db is None:
            db = get_db_session()
            close_db = True

        try:
            # Build query
            query = db.query(AuditEvent)

            # Apply filters
            if session_id:
                query = query.filter(AuditEvent.session_id == session_id)
            if agent_id:
                query = query.filter(AuditEvent.agent_id == agent_id)
            if user_identifier:
                query = query.filter(AuditEvent.user_identifier == user_identifier)
            if actor_type:
                query = query.filter(AuditEvent.actor_type == actor_type)
            if action_type:
                query = query.filter(AuditEvent.action_type == action_type)
            if status:
                query = query.filter(AuditEvent.status == status)
            if start_time:
                query = query.filter(AuditEvent.created_at >= start_time)
            if end_time:
                query = query.filter(AuditEvent.created_at <= end_time)

            # Apply pagination
            query = query.order_by(AuditEvent.created_at.desc())
            query = query.limit(limit).offset(offset)

            # Execute query
            events = query.all()

            # Convert to dictionaries
            result = []
            for event in events:
                event_dict = {
                    "id": event.id,
                    "session_id": event.session_id,
                    "agent_id": event.agent_id,
                    "user_identifier": event.user_identifier,
                    "actor_type": event.actor_type,
                    "actor_id": event.actor_id,
                    "action_type": event.action_type,
                    "entity_type": event.entity_type,
                    "entity_id": event.entity_id,
                    "status": event.status,
                    "error_message": event.error_message,
                    "context_data": event.context_data,
                    "created_at": event.created_at.isoformat(),
                    "modified_at": event.modified_at.isoformat() if event.modified_at else None
                }
                result.append(event_dict)

            return result
        except SQLAlchemyError as e:
            logger.error(f"Error querying audit events: {str(e)}")
            return []
        finally:
            if close_db:
                db.close()

    @staticmethod
    def get_session_history(
        session_id: str,
        include_messages: bool = True,
        include_events: bool = True,
        db: Optional[DbSession] = None
    ) -> Dict[str, Any]:
        """Get the complete history of a session.

        Args:
            session_id: The ID of the session.
            include_messages: Whether to include session messages.
            include_events: Whether to include audit events.
            db: Optional database session.

        Returns:
            Dictionary containing session history.
        """
        close_db = False
        if db is None:
            db = get_db_session()
            close_db = True

        try:
            # Get session
            session = db.query(Session).filter_by(id=session_id).first()
            if not session:
                logger.error(f"Session {session_id} not found")
                return {"error": "Session not found"}

            # Build result
            result = {
                "session_id": session.id,
                "user_identifier": session.user_identifier,
                "agent_id": session.agent_id,
                "start_time": session.start_time.isoformat() if session.start_time else None,
                "end_time": session.end_time.isoformat() if session.end_time else None
            }

            # Include messages if requested
            if include_messages:
                messages = db.query(SessionMessage).filter_by(session_id=session_id).order_by(SessionMessage.timestamp).all()
                result["messages"] = [
                    {
                        "id": msg.id,
                        "actor_type": msg.actor_type,
                        "message": msg.message,
                        "timestamp": msg.timestamp.isoformat()
                    }
                    for msg in messages
                ]

            # Include events if requested
            if include_events:
                events = db.query(AuditEvent).filter_by(session_id=session_id).order_by(AuditEvent.created_at).all()
                result["events"] = [
                    {
                        "id": event.id,
                        "agent_id": event.agent_id,
                        "actor_type": event.actor_type,
                        "actor_id": event.actor_id,
                        "action_type": event.action_type,
                        "entity_type": event.entity_type,
                        "entity_id": event.entity_id,
                        "status": event.status,
                        "error_message": event.error_message,
                        "context_data": event.context_data,
                        "created_at": event.created_at.isoformat()
                    }
                    for event in events
                ]

            return result
        except SQLAlchemyError as e:
            logger.error(f"Error getting session history for {session_id}: {str(e)}")
            return {"error": str(e)}
        finally:
            if close_db:
                db.close()