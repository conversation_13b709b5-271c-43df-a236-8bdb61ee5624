"""
Enhanced Vertex AI Session Service.

This module provides an enhanced version of the Vertex AI Session Service
with additional functionality for the ITSM solution.
"""

import logging
import time
from typing import Dict, Any, Optional, List

from google.adk.sessions import Session, VertexAiSessionService

# Set up logger
logger = logging.getLogger(__name__)


class EnhancedVertexAiSessionService(VertexAiSessionService):
    """Enhanced Vertex AI Session Service with additional functionality."""

    def __init__(self, project: str, location: str):
        """Initialize the Enhanced Vertex AI Session Service.

        Args:
            project: The Google Cloud project ID
            location: The Google Cloud location (e.g., "us-central1")
        """
        super().__init__(project_id=project, location=location)
        self._session_events = {}
        logger.info(f"Enhanced Vertex AI Session Service initialized with project={project}, location={location}")

    def create_session(
        self,
        app_name: str,
        user_id: str,
        session_id: str,
        state: Optional[Dict[str, Any]] = None
    ) -> Session:
        """Create a new session.

        Args:
            app_name: The application name
            user_id: The user ID
            session_id: The session ID
            state: Optional initial state for the session

        Returns:
            The created session
        """
        # Initialize session events tracking
        self._session_events[session_id] = []

        # Create session with metadata
        initial_state = state or {}
        initial_state["session_metadata"] = {
            "created_at": time.time(),
            "last_activity": time.time(),
            "user_id": user_id,
            "session_id": session_id,
            "app_name": app_name
        }

        logger.info(f"Creating enhanced Vertex AI session: user_id={user_id}, session_id={session_id}")
        return super().create_session(app_name, user_id, session_id, initial_state)

    def get_session(
        self,
        app_name: str,
        user_id: str,
        session_id: str
    ) -> Optional[Session]:
        """Get an existing session.

        Args:
            app_name: The application name
            user_id: The user ID
            session_id: The session ID

        Returns:
            The session if found, None otherwise
        """
        session = super().get_session(app_name, user_id, session_id)

        # Update last activity time if session exists
        if session and session.state and "session_metadata" in session.state:
            session.state["session_metadata"]["last_activity"] = time.time()

        return session

    def record_session_event(
        self,
        session_id: str,
        event_type: str,
        event_data: Dict[str, Any]
    ) -> None:
        """Record an event for a session.

        Args:
            session_id: The session ID
            event_type: The type of event
            event_data: The event data
        """
        if session_id not in self._session_events:
            self._session_events[session_id] = []

        self._session_events[session_id].append({
            "timestamp": time.time(),
            "type": event_type,
            "data": event_data
        })

    def get_session_events(self, session_id: str) -> List[Dict[str, Any]]:
        """Get events for a specific session.

        Args:
            session_id: The session ID

        Returns:
            List of events for the session
        """
        return self._session_events.get(session_id, [])
