"""
Session Service implementation.

This module contains the InMemorySessionService implementation for the HR solution
following Google ADK standards for session, state, and memory management.
"""

import logging
import time
import uuid
from typing import Dict, Any, Optional, Tuple

from google.adk.sessions import InMemorySessionService, Session

# Set up logger
logger = logging.getLogger(__name__)


class EnhancedInMemorySessionService(InMemorySessionService):
    """Enhanced version of InMemorySessionService following ADK standards.

    This implementation follows Google ADK best practices for:
    - Proper async/await patterns
    - State management with prefixes (user:, app:, temp:)
    - Event-driven state updates
    - Session lifecycle management
    """

    def __init__(self):
        """Initialize the EnhancedInMemorySessionService."""
        super().__init__()
        logger.info("Enhanced session service initialized following ADK standards")

    async def create_session(
        self,
        *,
        app_name: str,
        user_id: str,
        state: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> Session:
        """Create a new session following ADK standards.

        Args:
            app_name: The name of the application creating the session.
            user_id: The ID of the user for whom the session is created.
            session_id: The ID to assign to the new session. If None, a UUID will be generated.
            state: Optional initial state for the session.

        Returns:
            The newly created session.
        """
        # Generate session_id if not provided
        if session_id is None:
            session_id = str(uuid.uuid4())

        logger.info(f"Creating session: app={app_name}, user={user_id}, session={session_id}")

        # Create initial state following ADK state prefix standards
        initial_state = state or {}

        # Add session metadata (no prefix = session-scoped)
        initial_state.update({
            "session_metadata": {
                "created_at": time.time(),
                "last_activity": time.time(),
                "session_id": session_id
            }
        })

        # Add user-scoped metadata with proper prefix
        initial_state.update({
            "user:profile_loaded": False,
            "user:session_count": initial_state.get("user:session_count", 0) + 1
        })

        # Add app-scoped metadata with proper prefix
        initial_state.update({
            "app:name": app_name,
            "app:version": "1.0.0"
        })

        # Use parent class create_session method for proper ADK integration
        session = await super().create_session(
            app_name=app_name,
            user_id=user_id,
            state=initial_state,
            session_id=session_id
        )

        logger.info(f"Session created successfully: {session_id}")
        return session

    async def get_session(
        self,
        *,
        app_name: str,
        user_id: str,
        session_id: str,
        config: Optional[Any] = None
    ) -> Optional[Session]:
        """Get a session by ID following ADK standards.

        Args:
            app_name: The name of the application.
            user_id: The ID of the user.
            session_id: The ID of the session to retrieve.
            config: Optional configuration (not used in this implementation).

        Returns:
            The session if found, None otherwise.
        """
        logger.debug(f"Getting session: app={app_name}, user={user_id}, session={session_id}")

        # Use parent class method for proper ADK integration
        session = await super().get_session(
            app_name=app_name,
            user_id=user_id,
            session_id=session_id,
            config=config
        )

        if session:
            # Update last activity time following ADK state management patterns
            await self._update_session_activity(session)
            logger.debug(f"Session {session_id} retrieved successfully")
            return session
        else:
            logger.warning(f"Session {session_id} not found")
            return None

    async def _update_session_activity(self, session: Session) -> None:
        """Update session activity timestamp following ADK patterns.

        Args:
            session: The session to update.
        """
        # Update activity timestamp directly for now
        # In a full ADK implementation, this would use EventActions
        if "session_metadata" in session.state:
            session.state["session_metadata"]["last_activity"] = time.time()

        logger.debug(f"Updated activity timestamp for session {session.id}")

    async def update_session_state_via_event(
        self,
        session: Session,
        state_delta: Dict[str, Any],
        author: str = "system"
    ) -> None:
        """Update session state following ADK standards.

        This method updates state with proper validation and logging.

        Args:
            session: The session to update.
            state_delta: The changes to apply to the session state.
            author: The author of the state change event.
        """
        logger.debug(f"Updating session state: session={session.id}")

        # Validate and apply state changes
        validated_delta = self.validate_state_keys(state_delta)
        session.state.update(validated_delta)

        # Update last modified time
        if "session_metadata" in session.state:
            session.state["session_metadata"]["last_modified"] = time.time()

        logger.debug(f"Session {session.id} state updated successfully")

    def validate_state_keys(self, state_delta: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and organize state keys according to ADK prefix standards.

        Args:
            state_delta: The state changes to validate.

        Returns:
            Validated state delta with proper prefixes.
        """
        validated_delta = {}

        for key, value in state_delta.items():
            # Check if key follows ADK prefix standards
            if key.startswith(('user:', 'app:', 'temp:')):
                # Key already has proper prefix
                validated_delta[key] = value
            elif key in ['session_metadata']:
                # Session-scoped keys (no prefix)
                validated_delta[key] = value
            else:
                # Default to session scope for backward compatibility
                logger.warning(f"State key '{key}' lacks ADK prefix, treating as session-scoped")
                validated_delta[key] = value

        return validated_delta