"""
Service implementations for the HR solution.

This package contains all service implementations following Google ADK standards:
- Session Service: Enhanced session management with proper state handling
- Memory Service: Long-term knowledge storage and retrieval across sessions
- Logging Service: Comprehensive logging and audit trails
"""

from .session_service import EnhancedInMemorySessionService
from .memory_service import EnhancedInMemoryMemoryService

__all__ = [
    'EnhancedInMemorySessionService',
    'EnhancedInMemoryMemoryService'
]