"""
Memory Service implementation.

This module contains the MemoryService implementation for the HR solution
following Google ADK standards for long-term knowledge storage and retrieval.
"""

import logging
import time
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from google.adk.sessions import Session
from google.adk.memory import BaseMemoryService, InMemoryMemoryService

# Set up logger
logger = logging.getLogger(__name__)


@dataclass
class HRMemoryResult:
    """Memory result for HR-specific information."""

    session_id: str
    user_id: str
    timestamp: float
    content: str
    metadata: Dict[str, Any]


@dataclass
class SearchMemoryResponse:
    """Response object for memory search operations."""

    results: List[HRMemoryResult]


class EnhancedInMemoryMemoryService(InMemoryMemoryService):
    """Enhanced version of InMemoryMemoryService following ADK standards.
    
    This implementation follows Google ADK best practices for:
    - Long-term knowledge storage across sessions
    - Semantic search capabilities
    - Session data ingestion
    - Cross-session information retrieval
    """

    def __init__(self):
        """Initialize the EnhancedInMemoryMemoryService."""
        super().__init__()
        self.memory_store: Dict[str, List[HRMemoryResult]] = {}
        self.user_memories: Dict[str, List[HRMemoryResult]] = {}
        logger.info("Enhanced memory service initialized following ADK standards")

    async def add_session_to_memory(self, session: Session) -> None:
        """Add session information to long-term memory following ADK standards.

        Args:
            session: The session to add to memory.
        """
        logger.info(f"Adding session {session.id} to memory for user {session.user_id}")
        
        # Extract meaningful information from session events
        memory_entries = self._extract_memory_from_session(session)
        
        # Store in app-wide memory
        app_key = session.app_name
        if app_key not in self.memory_store:
            self.memory_store[app_key] = []
        
        self.memory_store[app_key].extend(memory_entries)
        
        # Store in user-specific memory
        user_key = f"{session.app_name}:{session.user_id}"
        if user_key not in self.user_memories:
            self.user_memories[user_key] = []
        
        self.user_memories[user_key].extend(memory_entries)
        
        logger.info(f"Added {len(memory_entries)} memory entries from session {session.id}")

    async def search_memory(
        self,
        app_name: str,
        user_id: str,
        query: str,
        max_results: int = 5
    ) -> SearchMemoryResponse:
        """Search memory for relevant information following ADK standards.

        Args:
            app_name: The application name.
            user_id: The user ID.
            query: The search query.
            max_results: Maximum number of results to return.

        Returns:
            SearchMemoryResponse containing relevant memory results.
        """
        logger.debug(f"Searching memory: app={app_name}, user={user_id}, query='{query}'")
        
        # Search user-specific memories first
        user_key = f"{app_name}:{user_id}"
        user_results = self._search_memory_entries(
            self.user_memories.get(user_key, []), 
            query, 
            max_results // 2
        )
        
        # Search app-wide memories
        app_results = self._search_memory_entries(
            self.memory_store.get(app_name, []), 
            query, 
            max_results - len(user_results)
        )
        
        # Combine and deduplicate results
        all_results = user_results + app_results
        unique_results = self._deduplicate_results(all_results)[:max_results]
        
        logger.debug(f"Found {len(unique_results)} relevant memory entries")
        
        return SearchMemoryResponse(results=unique_results)

    def _extract_memory_from_session(self, session: Session) -> List[HRMemoryResult]:
        """Extract meaningful information from session for memory storage.
        
        Args:
            session: The session to extract memory from.
            
        Returns:
            List of memory results extracted from the session.
        """
        memory_entries = []
        
        # Extract from session events
        for event in session.events:
            if event.content and hasattr(event.content, 'parts'):
                for part in event.content.parts:
                    if hasattr(part, 'text') and part.text:
                        # Create memory entry for meaningful content
                        if len(part.text.strip()) > 10:  # Filter out very short content
                            memory_entry = HRMemoryResult(
                                session_id=session.id,
                                user_id=session.user_id,
                                timestamp=event.timestamp or time.time(),
                                content=part.text.strip(),
                                metadata={
                                    "event_author": event.author,
                                    "event_id": event.invocation_id,
                                    "app_name": session.app_name,
                                    "session_state_snapshot": dict(session.state)
                                }
                            )
                            memory_entries.append(memory_entry)
        
        # Extract from session state for important user preferences
        user_state = {k: v for k, v in session.state.items() if k.startswith('user:')}
        if user_state:
            state_summary = f"User preferences and settings: {json.dumps(user_state, indent=2)}"
            memory_entry = HRMemoryResult(
                session_id=session.id,
                user_id=session.user_id,
                timestamp=time.time(),
                content=state_summary,
                metadata={
                    "type": "user_state",
                    "app_name": session.app_name,
                    "extracted_at": time.time()
                }
            )
            memory_entries.append(memory_entry)
        
        return memory_entries

    def _search_memory_entries(
        self,
        entries: List[HRMemoryResult],
        query: str,
        max_results: int
    ) -> List[HRMemoryResult]:
        """Search memory entries for relevant content.
        
        Args:
            entries: List of memory entries to search.
            query: The search query.
            max_results: Maximum number of results to return.
            
        Returns:
            List of relevant memory results.
        """
        query_lower = query.lower()
        scored_results = []
        
        for entry in entries:
            content_lower = entry.content.lower()
            
            # Simple keyword matching with scoring
            score = 0
            query_words = query_lower.split()
            
            for word in query_words:
                if word in content_lower:
                    score += content_lower.count(word)
            
            if score > 0:
                scored_results.append((score, entry))
        
        # Sort by score (descending) and return top results
        scored_results.sort(key=lambda x: x[0], reverse=True)
        return [entry for _, entry in scored_results[:max_results]]

    def _deduplicate_results(self, results: List[HRMemoryResult]) -> List[HRMemoryResult]:
        """Remove duplicate results based on content similarity.
        
        Args:
            results: List of memory results to deduplicate.
            
        Returns:
            Deduplicated list of memory results.
        """
        unique_results = []
        seen_content = set()
        
        for result in results:
            # Use first 100 characters as deduplication key
            content_key = result.content[:100].strip()
            if content_key not in seen_content:
                seen_content.add(content_key)
                unique_results.append(result)
        
        return unique_results

    def get_memory_stats(self, app_name: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get statistics about stored memory.
        
        Args:
            app_name: The application name.
            user_id: Optional user ID for user-specific stats.
            
        Returns:
            Dictionary containing memory statistics.
        """
        stats = {
            "app_memory_count": len(self.memory_store.get(app_name, [])),
            "total_apps": len(self.memory_store),
        }
        
        if user_id:
            user_key = f"{app_name}:{user_id}"
            stats["user_memory_count"] = len(self.user_memories.get(user_key, []))
            stats["total_users"] = len(self.user_memories)
        
        return stats
