"""
Example demonstrating how headers (tenantId, entityId, etc.) flow through the HR API system.

This example shows:
1. How to set default headers via environment variables
2. How to set tenant context programmatically
3. How to pass custom headers per request
4. Complete flow from API call to HTTP request
"""

import os
import sys
sys.path.append('..')

from utils.hr_api_client import leave_balance_client, attendance_client
from utils.hr_auth import hr_auth

def example_1_environment_headers():
    """Example 1: Setting headers via environment variables"""
    print("=== Example 1: Environment Headers ===")
    
    # These would typically be set in .env file:
    os.environ["HR_TENANT_ID"] = "tenant-123"
    os.environ["HR_ENTITY_ID"] = "entity-456"
    os.environ["HR_ORGANIZATION_ID"] = "org-789"
    
    # When client is initialized, it automatically picks up these headers
    print(f"Default headers: {leave_balance_client.default_headers}")
    
    # API call will include these headers automatically
    result = leave_balance_client.get_leave_balance(email="<EMAIL>")
    print(f"Result: {result}")

def example_2_programmatic_context():
    """Example 2: Setting tenant context programmatically"""
    print("\n=== Example 2: Programmatic Context ===")
    
    # Set tenant context for specific client
    leave_balance_client.set_tenant_context(
        tenant_id="dynamic-tenant-999",
        entity_id="dynamic-entity-888",
        organization_id="dynamic-org-777"
    )
    
    print(f"Updated headers: {leave_balance_client.default_headers}")
    
    # All subsequent calls will use these headers
    result = leave_balance_client.get_leave_balance(employee_id="EMP001")
    print(f"Result: {result}")

def example_3_per_request_headers():
    """Example 3: Custom headers per request"""
    print("\n=== Example 3: Per-Request Headers ===")
    
    # Pass custom headers for specific request
    custom_headers = {
        "tenantId": "special-tenant-555",
        "entityId": "special-entity-444",
        "customHeader": "custom-value",
        "requestId": "req-12345"
    }
    
    result = leave_balance_client.get_leave_balance(
        email="<EMAIL>",
        headers=custom_headers
    )
    print(f"Result: {result}")

def example_4_complete_flow():
    """Example 4: Complete header flow demonstration"""
    print("\n=== Example 4: Complete Flow ===")

    print("1. Default headers from environment:")
    print(f"   {leave_balance_client.default_headers}")

    print("\n2. Adding per-request headers:")
    request_headers = {
        "requestId": "flow-demo-001",
        "userAgent": "HR-Assistant/1.0"
    }

    print("3. Headers will be merged in this order:")
    print("   a) Default headers (from environment/context)")
    print("   b) Per-request headers")
    print("   c) Authentication headers (added by auth manager)")

    merged = leave_balance_client._merge_headers(request_headers)
    print(f"   Merged headers (without auth): {merged}")

    print("\n4. Getting complete headers including Authorization:")
    complete_headers = leave_balance_client.get_all_headers(request_headers)
    print("   Complete headers with Authorization:")
    for key, value in complete_headers.items():
        if key.lower() == 'authorization':
            # Mask the token for security
            masked_value = f"Bearer ***{value[-10:] if len(value) > 10 else '***'}"
            print(f"   {key}: {masked_value}")
        else:
            print(f"   {key}: {value}")

    print("\n5. Final HTTP request headers structure:")
    print("   ✓ Authorization: Bearer <wagonhr-token>")
    print("   ✓ Content-Type: application/json")
    print("   ✓ tenantId: <tenant-id>")
    print("   ✓ entityId: <entity-id>")
    print("   ✓ requestId: <request-id>")

def example_5_api_call_with_headers():
    """Example 5: Actual API call showing header flow"""
    print("\n=== Example 5: API Call with Headers ===")
    
    # This demonstrates the complete flow:
    # 1. get_leave_balance() -> LeaveBalanceClient.get_leave_balance()
    # 2. -> HRAPIClient.get() with merged headers
    # 3. -> HRAuthManager.make_authenticated_request() with auth headers
    # 4. -> requests.request() with final headers
    
    print("Making API call with custom headers...")
    
    custom_headers = {
        "tenantId": "demo-tenant",
        "entityId": "demo-entity", 
        "correlationId": "demo-correlation-123"
    }
    
    try:
        result = leave_balance_client.get_leave_balance(
            email="<EMAIL>",
            headers=custom_headers
        )
        print(f"API Response: {result}")
    except Exception as e:
        print(f"API Error (expected in demo): {e}")

if __name__ == "__main__":
    print("HR API Header Flow Examples")
    print("=" * 50)
    
    example_1_environment_headers()
    example_2_programmatic_context()
    example_3_per_request_headers()
    example_4_complete_flow()
    example_5_api_call_with_headers()
    
    print("\n" + "=" * 50)
    print("Header Flow Summary:")
    print("1. Environment variables -> Default headers")
    print("2. set_tenant_context() -> Updates default headers")
    print("3. Per-request headers -> Merged with defaults")
    print("4. Authentication headers -> Added by auth manager")
    print("5. Final HTTP request -> All headers combined")
