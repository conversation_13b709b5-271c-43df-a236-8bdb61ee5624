"""
LLM-Powered Leave Agent Integration Demo.

This script demonstrates how the new LLM-powered leave agent is integrated
across the application to handle any leave request format dynamically.
"""

import sys
import os
import logging
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demonstrate_llm_powered_integration():
    """Demonstrate the LLM-powered leave agent integration."""
    print("🚀 LLM-Powered Leave Agent Integration Demo")
    print("=" * 60)
    print()
    
    # Import the tools
    try:
        from tools.leave_tools import request_leave_intelligent
        from tools.llm_powered_leave_agent import request_leave_llm_powered
        from utils.conversation_state_manager import get_conversation_state
        print("✅ Successfully imported LLM-powered leave agent")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return
    
    # Test cases that were problematic before
    problematic_cases = [
        "please apply a leave for me",
        "please apply a vacation leave from 5 aug to 10 aug",
        "I want to take a day off tomorrow",
        "Need sick leave, not feeling well",
        "Apply annual leave from July 28 to August 2",
        "Help me with leave",
        "Check my leave balance",
        "Cancel my leave for tomorrow"
    ]
    
    print("🧪 Testing Problematic Cases with LLM-Powered Agent")
    print("-" * 50)
    
    session_id = "demo_session"
    
    for i, test_case in enumerate(problematic_cases, 1):
        print(f"\n{i}. Testing: '{test_case}'")
        print("   " + "─" * 40)
        
        try:
            # Test with the new LLM-powered agent
            result = request_leave_llm_powered(
                leave_request_text=test_case,
                session_id=session_id,
                employee_id=None,
                tool_context=None
            )
            
            status = result.get("status", "unknown")
            message = result.get("message", "No message")
            llm_powered = result.get("llm_powered", False)
            confidence = result.get("confidence", 0.0)
            
            print(f"   📊 Status: {status}")
            print(f"   🤖 LLM-Powered: {llm_powered}")
            print(f"   📈 Confidence: {confidence:.2f}")
            print(f"   💬 Response: {message[:100]}{'...' if len(message) > 100 else ''}")
            
            if status == "success" and result.get("action") == "process_leave":
                print(f"   ✅ Ready to process:")
                print(f"      📅 Dates: {result.get('start_date')} to {result.get('end_date')}")
                print(f"      🏷️  Type: {result.get('leave_type')}")
                print(f"      📝 Reason: {result.get('reason')}")
            elif status == "clarification_needed":
                missing = result.get("missing_fields", [])
                print(f"   🔄 Needs clarification: {', '.join(missing)}")
            elif status in ["help", "check_balance", "cancel_leave"]:
                print(f"   🔀 Redirect to: {result.get('action', status)}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🔄 Testing Conversation Context")
    print("-" * 30)
    
    # Test conversation context
    conversation_tests = [
        ("I need leave", "Initial vague request"),
        ("For next Friday", "Adding date information"),
        ("It's for a doctor appointment", "Adding reason"),
        ("Make it sick leave", "Specifying leave type")
    ]
    
    context_session = "context_demo"
    
    for i, (message, description) in enumerate(conversation_tests, 1):
        print(f"\n{i}. {description}: '{message}'")
        
        try:
            result = request_leave_llm_powered(
                leave_request_text=message,
                session_id=context_session,
                employee_id=None,
                tool_context=None
            )
            
            status = result.get("status", "unknown")
            response_message = result.get("message", "No message")
            
            print(f"   📊 Status: {status}")
            print(f"   💬 Response: {response_message[:80]}{'...' if len(response_message) > 80 else ''}")
            
            if status == "success":
                print("   ✅ Complete information collected through conversation!")
                break
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("📊 Integration Summary")
    print("-" * 20)
    print("✅ LLM-powered agent integrated into existing leave tools")
    print("✅ Handles ANY leave request format without code changes")
    print("✅ Maintains conversation context across interactions")
    print("✅ Provides natural, user-friendly responses")
    print("✅ Seamlessly integrates with existing HR workflow")
    print("✅ Fallback to legacy system if needed")
    
    # Show conversation state
    try:
        state_manager = get_conversation_state(context_session)
        summary = state_manager.get_conversation_summary()
        print(f"\n🧠 Conversation State Summary:")
        print(f"   📊 Turns: {summary['turn_count']}")
        print(f"   ⏱️  Duration: {summary['duration_seconds']:.1f}s")
        print(f"   📋 Stage: {summary['stage']}")
        print(f"   ✅ Complete: {summary['is_complete']}")
        if summary['missing_fields']:
            print(f"   ❓ Missing: {', '.join(summary['missing_fields'])}")
    except Exception as e:
        print(f"   ⚠️  Could not retrieve conversation state: {e}")


def test_integration_with_existing_tools():
    """Test integration with existing leave management tools."""
    print("\n" + "=" * 60)
    print("🔧 Testing Integration with Existing Tools")
    print("-" * 40)
    
    try:
        from tools.leave_tools import request_leave_intelligent
        
        # Test that the existing tool now uses the LLM-powered agent
        test_request = "I need sick leave for tomorrow because I'm not feeling well"
        
        print(f"Testing existing tool with: '{test_request}'")
        
        result = request_leave_intelligent(
            leave_request_text=test_request,
            employee_id=None,
            tool_context=None
        )
        
        print(f"📊 Result status: {result.get('status', 'unknown')}")
        print(f"🤖 LLM-powered: {result.get('llm_powered', False)}")
        print(f"💬 Message: {result.get('message', 'No message')[:100]}...")
        
        if result.get("success") or (result.get("status") == "success"):
            print("✅ Integration successful - existing tool now uses LLM-powered agent")
        else:
            print("🔄 Tool working but may need clarification")
            
    except Exception as e:
        print(f"❌ Integration test failed: {e}")


if __name__ == "__main__":
    try:
        demonstrate_llm_powered_integration()
        test_integration_with_existing_tools()
        
        print("\n" + "🎉" * 20)
        print("LLM-Powered Leave Agent Successfully Integrated!")
        print("🎉" * 20)
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
