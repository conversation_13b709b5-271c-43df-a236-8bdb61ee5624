"""
HR Agent Integration Example.

This example shows how to integrate the Flexible WagonHR Agent into HR chatbots
and assistants for seamless API integration.
"""

import logging
from typing import Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HRAssistant:
    """Example HR Assistant using the Flexible WagonHR Agent."""
    
    def __init__(self):
        """Initialize the HR Assistant."""
        self.session_state = {}
        
    def handle_user_request(self, user_message: str) -> str:
        """
        Handle user requests and route to appropriate handlers.
        
        Args:
            user_message: User's natural language request
            
        Returns:
            Formatted response for the user
        """
        try:
            # Import the flexible agent
            from tools.flexible_wagonhr_agent import process_wagonhr_request
            
            # Check if this is an HR-related request
            hr_keywords = [
                "leave", "vacation", "attendance", "profile", "salary", 
                "timesheet", "balance", "request", "apply", "record"
            ]
            
            is_hr_request = any(keyword in user_message.lower() for keyword in hr_keywords)
            
            if is_hr_request:
                # Process with flexible WagonHR agent
                result = process_wagonhr_request(user_message, self)
                return self.format_hr_response(result, user_message)
            else:
                return "I'm your HR assistant. I can help you with leave management, attendance, employee information, and more. What would you like to know?"
                
        except Exception as e:
            logger.error(f"Error handling user request: {e}")
            return "I'm sorry, I encountered an error processing your request. Please try again."
    
    def format_hr_response(self, result: Dict[str, Any], original_request: str) -> str:
        """
        Format the API response into a user-friendly message.
        
        Args:
            result: Response from the flexible WagonHR agent
            original_request: Original user request
            
        Returns:
            Formatted response string
        """
        if result.get("status") == "success":
            data = result.get("data", {})
            endpoint = result.get("endpoint", "")
            
            # Format based on endpoint type
            if endpoint == "leave_balance":
                return self.format_leave_balance(data)
            elif endpoint == "leave_request":
                return self.format_leave_request_response(data)
            elif endpoint == "leave_history":
                return self.format_leave_history(data)
            elif endpoint == "attendance_record":
                return self.format_attendance_response(data)
            elif endpoint == "attendance_report":
                return self.format_attendance_report(data)
            elif endpoint == "employee_profile":
                return self.format_employee_profile(data)
            else:
                return f"✅ Successfully processed your request: {result.get('message', 'Operation completed')}"
        
        elif result.get("status") == "error":
            error_message = result.get("message", "Unknown error occurred")
            
            if "authenticate" in error_message.lower():
                return "🔐 I need to authenticate with WagonHR first. Please make sure you're logged in to the system."
            elif "endpoint" in error_message.lower():
                available = result.get("available_endpoints", [])
                return f"❓ I couldn't understand your request. I can help you with: {', '.join(available)}"
            else:
                return f"❌ Sorry, I encountered an issue: {error_message}"
        
        return "I processed your request, but didn't receive a clear response. Please try rephrasing your question."
    
    def format_leave_balance(self, data: Dict[str, Any]) -> str:
        """Format leave balance response."""
        if isinstance(data, dict) and "leave_balance" in data:
            balance_info = data["leave_balance"]
            response = "🏖️ **Your Leave Balance:**\n"
            for leave_type, days in balance_info.items():
                emoji = "🏖️" if leave_type.lower() == "vacation" else "🤒" if leave_type.lower() == "sick" else "📅"
                response += f"{emoji} {leave_type.title()}: {days} days\n"
            return response
        else:
            return "🏖️ I retrieved your leave balance information. Please check with HR for specific details."
    
    def format_leave_request_response(self, data: Dict[str, Any]) -> str:
        """Format leave request submission response."""
        if isinstance(data, dict):
            request_id = data.get("requestId", "N/A")
            status = data.get("status", "submitted")
            return f"✅ **Leave Request Submitted Successfully!**\n📋 Request ID: {request_id}\n📊 Status: {status.title()}\n\nYour manager will be notified for approval."
        else:
            return "✅ Your leave request has been submitted successfully! You'll receive a confirmation email shortly."
    
    def format_leave_history(self, data: Dict[str, Any]) -> str:
        """Format leave history response."""
        if isinstance(data, list) and data:
            response = "📋 **Your Leave History:**\n"
            for i, leave in enumerate(data[:5], 1):  # Show last 5 leaves
                start_date = leave.get("startDate", "N/A")
                end_date = leave.get("endDate", "N/A")
                leave_type = leave.get("leaveType", "N/A")
                status = leave.get("status", "N/A")
                
                status_emoji = "✅" if status.lower() == "approved" else "⏳" if status.lower() == "pending" else "❌"
                response += f"{i}. {leave_type.title()} ({start_date} to {end_date}) {status_emoji} {status.title()}\n"
            
            if len(data) > 5:
                response += f"\n... and {len(data) - 5} more entries"
            return response
        else:
            return "📋 No leave history found or you haven't taken any leaves yet."
    
    def format_attendance_response(self, data: Dict[str, Any]) -> str:
        """Format attendance recording response."""
        if isinstance(data, dict):
            timestamp = data.get("timestamp", "now")
            status = data.get("status", "recorded")
            return f"✅ **Attendance Recorded Successfully!**\n⏰ Time: {timestamp}\n📊 Status: {status.title()}"
        else:
            return "✅ Your attendance has been recorded successfully!"
    
    def format_attendance_report(self, data: Dict[str, Any]) -> str:
        """Format attendance report response."""
        if isinstance(data, dict):
            response = "📊 **Your Attendance Report:**\n"
            for key, value in data.items():
                if key not in ["raw_data", "metadata"]:
                    emoji = "📅" if "days" in key.lower() else "⏰" if "hours" in key.lower() else "📈"
                    response += f"{emoji} {key.replace('_', ' ').title()}: {value}\n"
            return response
        else:
            return "📊 I've generated your attendance report. Please check your email for the detailed report."
    
    def format_employee_profile(self, data: Dict[str, Any]) -> str:
        """Format employee profile response."""
        if isinstance(data, dict):
            name = data.get("name", "N/A")
            department = data.get("department", "N/A")
            position = data.get("position", "N/A")
            manager = data.get("manager", "N/A")
            
            response = f"👤 **Your Profile Information:**\n"
            response += f"📛 Name: {name}\n"
            response += f"🏢 Department: {department}\n"
            response += f"💼 Position: {position}\n"
            response += f"👨‍💼 Manager: {manager}\n"
            return response
        else:
            return "👤 I've retrieved your profile information. Please check with HR for any updates needed."

def demonstrate_custom_api_integration():
    """Demonstrate how to add custom APIs to the HR assistant."""
    try:
        from tools.flexible_wagonhr_agent import add_custom_endpoint, call_custom_api
        
        # Add a custom payroll API
        payroll_endpoint = {
            "path": "/api/hrms/payroll/slip",
            "method": "GET",
            "description": "Get employee payroll slip",
            "parameters": ["month", "year", "format"]
        }
        
        add_custom_endpoint("payroll_slip", payroll_endpoint)
        logger.info("Added custom payroll endpoint")
        
        # Add a custom benefits API
        benefits_endpoint = {
            "path": "/api/hrms/benefits/enrollment",
            "method": "POST",
            "description": "Enroll in employee benefits",
            "parameters": ["benefitType", "coverage", "dependents"]
        }
        
        add_custom_endpoint("benefits_enrollment", benefits_endpoint)
        logger.info("Added custom benefits endpoint")
        
        # Now the HR assistant can handle these requests automatically
        assistant = HRAssistant()
        
        # Test payroll request
        payroll_response = assistant.handle_user_request("Show me my payroll slip for January 2024")
        logger.info(f"Payroll response: {payroll_response}")
        
        # Test benefits request
        benefits_response = assistant.handle_user_request("I want to enroll in health insurance with family coverage")
        logger.info(f"Benefits response: {benefits_response}")
        
    except Exception as e:
        logger.error(f"Error demonstrating custom API integration: {e}")

def example_conversation():
    """Example conversation with the HR assistant."""
    assistant = HRAssistant()
    
    # Simulate a conversation
    conversation = [
        "Hi, how many leave days do I have left?",
        "I want to apply for casual leave from 2024-03-15 to 2024-03-17 because I'm sick",
        "Show me my leave history",
        "Record my attendance as present today",
        "Generate my attendance report for February 2024",
        "Show me my employee profile"
    ]
    
    logger.info("=== HR ASSISTANT CONVERSATION EXAMPLE ===")
    
    for i, user_message in enumerate(conversation, 1):
        logger.info(f"\n--- Turn {i} ---")
        logger.info(f"User: {user_message}")
        
        response = assistant.handle_user_request(user_message)
        logger.info(f"Assistant: {response}")

if __name__ == "__main__":
    logger.info("HR Agent Integration Example")
    
    # Run example conversation
    example_conversation()
    
    print("\n" + "="*50 + "\n")
    
    # Demonstrate custom API integration
    demonstrate_custom_api_integration()
    
    logger.info("HR Agent Integration Example completed!")
