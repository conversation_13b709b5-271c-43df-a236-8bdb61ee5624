"""
Attendance Management Agent implementation.

This module contains the Attendance Management Agent for handling attendance tracking,
verification, and reporting with support for multi-timezone and geo-fencing.
"""

import logging
from typing import List, Optional

from google.adk.agents import Agent
from google.adk.tools import transfer_to_agent

from tools.attendance_tools import (
    get_attendance_report, get_attendance_summary
)

from tools.hr_tools import get_employee_info
from utils.callbacks import input_safety_guardrail, tool_safety_guardrail, combined_post_processing_callback
from utils.system_prompts import ATTENDANCE_MANAGEMENT_AGENT_PROMPT

# Set up logger
logger = logging.getLogger(__name__)


def create_attendance_management_agent(
    model_name: str = "gemini-2.5-flash",
    safety_callbacks: bool = True
) -> Agent:
    """Creates an Attendance Management Agent.

    Args:
        model_name: Name of the LLM model to use.
        safety_callbacks: Whether to enable safety callbacks.

    Returns:
        A configured Attendance Management Agent.
    """
    logger.info(f"Creating Attendance Management Agent with model {model_name}")

    # Define tools
    tools = [
        # Attendance tools
        get_attendance_report,
        get_attendance_summary,

        # Transfer tool
        transfer_to_agent
    ]

    # Define callbacks
    callbacks = {}
    if safety_callbacks:
        callbacks["before_model_callback"] = input_safety_guardrail
        callbacks["before_tool_callback"] = tool_safety_guardrail

    # Always include the combined post-processing callback
    callbacks["after_model_callback"] = combined_post_processing_callback

    # Create the agent
    agent = Agent(
        name="attendance_management_agent",
        model=model_name,
        description="Agent for handling attendance tracking, verification, and reporting with support for multi-timezone and geo-fencing.",
        instruction=ATTENDANCE_MANAGEMENT_AGENT_PROMPT,
        output_key="attendance_management_agent_response",
        tools=tools,
        **callbacks
    )

    logger.info("Attendance Management Agent created successfully")
    return agent
