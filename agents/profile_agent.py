"""
Profile Agent implementation.

This module contains the Profile Agent for handling Profile related queries.
"""

import logging
from typing import List, Optional

from google.adk.agents import Agent
from google.adk.tools import transfer_to_agent

from tools.profile_tools import (
    get_employee_info, get_employee_work_profile, get_my_team, search_employee
)
from utils.callbacks import input_safety_guardrail, tool_safety_guardrail, combined_post_processing_callback
from utils.system_prompts import PROFILE_AGENT_PROMPT

# Set up logger with a specific name for profile agent
logger = logging.getLogger("profile_agent")
logger.setLevel(logging.INFO)

# Create console handler with formatting
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)


def create_profile_agent(
    model_name: str = "gemini-2.5-flash",
    safety_callbacks: bool = True
) -> Agent:
    """ Creates a Profile Agent.

    Args:
        model_name: Name of the LLM model to use.
        safety_callbacks: Whether to enable safety callbacks.

    Returns:
        A configured Profile Agent.
    """
    logger.info(f"Creating Profile Agent with model {model_name}")

    # Define tools
    tools = [
        # Profile tools
        get_employee_info,
        get_employee_work_profile,
        get_my_team,
        search_employee,

        # Transfer tool
        transfer_to_agent
    ]
    logger.info("Profile tools configured: get_employee_info, get_employee_work_profile")

    # Define callbacks
    callbacks = {}
    if safety_callbacks:
        logger.info("Enabling safety callbacks")
        callbacks["before_model_callback"] = input_safety_guardrail
        callbacks["before_tool_callback"] = tool_safety_guardrail

    # Always include the combined post-processing callback
    logger.info("Adding post-processing callback")
    callbacks["after_model_callback"] = combined_post_processing_callback

    # Create the agent
    logger.info("Initializing Profile Agent with configuration")
    agent = Agent(
        name="profile_agent",
        model=model_name,
        description="Agent for handling profile related queries.",
        instruction=PROFILE_AGENT_PROMPT,
        output_key="profile_agent_response",
        tools=tools,
        **callbacks
    )

    logger.info("Profile Agent created successfully")
    return agent


