"""
Leave Management Agent implementation.

This module contains the Leave Management Agent for handling leave requests,
approvals, and workload-based leave suggestions.
"""

import logging
from typing import List, Optional

from google.adk.agents import Agent
from google.adk.tools import transfer_to_agent

from tools.leave_tools import (
    get_leave_balance, request_leave, request_leave_intelligent, get_leave_history, get_holidays,
    approve_reject_leave, cancel_leave, cancel_leave_intelligent, modify_leave, get_pending_leaves, get_comp_off_leaves, get_my_team_leaves_overview, get_my_team_pending_approvals, get_my_team_comp_off_pending_approvals, get_my_teams_leave_history,
    update_employee_leave_details, get_all_pending_leaves_approvals, get_all_past_leaves, get_all_leaves_overview, get_all_comp_off_pending_approvals
)
from tools.hr_tools import get_employee_info
from utils.callbacks import input_safety_guardrail, tool_safety_guardrail, combined_post_processing_callback
from utils.system_prompts import LEAVE_MANAGEMENT_AGENT_PROMPT

# Set up logger
logger = logging.getLogger(__name__)


def create_leave_management_agent(
    model_name: str = "gemini-2.5-flash",
    safety_callbacks: bool = True
) -> Agent:
    """Creates a Leave Management Agent.

    Args:
        model_name: Name of the LLM model to use.
        safety_callbacks: Whether to enable safety callbacks.

    Returns:
        A configured Leave Management Agent.
    """
    logger.info(f"Creating Leave Management Agent with model {model_name}")

    # Define tools
    tools = [
        # Leave management tools
        get_leave_balance,
        request_leave_intelligent,  # AI-powered natural language leave request parsing
        get_leave_history,
        get_pending_leaves,  # Get pending leave requests
        approve_reject_leave,
        cancel_leave,
        cancel_leave_intelligent,  # User-friendly leave cancellation
        modify_leave,
        get_holidays,
        get_comp_off_leaves,
        get_my_team_leaves_overview,
        get_my_team_pending_approvals,
        get_my_team_comp_off_pending_approvals,
        get_my_teams_leave_history,
        update_employee_leave_details,
        get_all_pending_leaves_approvals,
        get_all_past_leaves,
        get_all_leaves_overview,
        get_all_comp_off_pending_approvals,

        # Transfer tool
        transfer_to_agent
    ]

    # Define callbacks
    callbacks = {}
    if safety_callbacks:
        callbacks["before_model_callback"] = input_safety_guardrail
        callbacks["before_tool_callback"] = tool_safety_guardrail

    # Always include the combined post-processing callback
    callbacks["after_model_callback"] = combined_post_processing_callback

    # Create the agent
    agent = Agent(
        name="leave_management_agent",
        model=model_name,
        description="Agent for handling leave requests, approvals, and workload-based leave suggestions.",
        instruction=LEAVE_MANAGEMENT_AGENT_PROMPT,
        output_key="leave_management_agent_response",
        tools=tools,
        **callbacks
    )

    logger.info("Leave Management Agent created successfully")
    return agent
