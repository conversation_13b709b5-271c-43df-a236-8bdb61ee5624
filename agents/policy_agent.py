"""
Policy Agent implementation.

This module contains the Policy Agent for handling Internal Policies.
"""

import logging
from typing import List, Optional

from google.adk.agents import Agent
from google.adk.tools import transfer_to_agent

from tools.policy_qna import (
    get_policy_information
)
from utils.callbacks import input_safety_guardrail, tool_safety_guardrail, combined_post_processing_callback
from utils.system_prompts import POLICY_PROMPT

# Set up logger
logger = logging.getLogger(__name__)


def create_policy_agent(
    model_name: str = "gemini-2.5-flash",
    safety_callbacks: bool = True
) -> Agent:
    """Creates an Policy Agent.

    Args:
        model_name: Name of the LLM model to use.
        safety_callbacks: Whether to enable safety callbacks.

    Returns:
        A configured Policy Agent.
    """
    logger.info(f"Creating Policy Agent with model {model_name}")

    tools = [
        get_policy_information,
        transfer_to_agent  # Required for transferring non-policy queries to appropriate agents
    ]

        # Define callbacks
    callbacks = {}
    if safety_callbacks:
        callbacks["before_model_callback"] = input_safety_guardrail
        callbacks["before_tool_callback"] = tool_safety_guardrail

    # Always include the combined post-processing callback
    callbacks["after_model_callback"] = combined_post_processing_callback

    # Create the agent
    agent = Agent(
        name="policy_agent",
        model=model_name,
        description="Agent for handling policy related queries.",
        instruction=POLICY_PROMPT,
        output_key="policy_agent_response",
        tools=tools,
        **callbacks
    )

    logger.info("Policy Agent created successfully")
    return agent