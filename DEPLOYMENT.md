# Deploying HR Solution on AWS

This guide provides instructions for deploying the HR Solution on Amazon Web Services (AWS) as a containerized application.

## Prerequisites

- AWS account with appropriate permissions
- AWS CLI installed and configured
- Docker installed locally
- Access to Amazon ECR (Elastic Container Registry)
- Amazon RDS instance (PostgreSQL) or other PostgreSQL database

## Deployment Steps

### 1. Set up Environment Variables

Create a `.env` file with your configuration:

```
# Environment Configuration
ENVIRONMENT=prod  # dev, staging, prod

# Google API Key - Required for Google ADK
GOOGLE_API_KEY=your-google-api-key-here
GOOGLE_GENAI_USE_VERTEXAI=False

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# Database Configuration
DATABASE_URL=*****************************************************/postgres

# Model Configuration
ROOT_MODEL=gemini-2.0-flash
SUB_AGENT_MODEL=gemini-2.0-flash

# Logging Configuration
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
```

### 2. Build and Test the Docker Image Locally

```bash
# Build the Docker image
docker build -t hr-solution .

# Run the container locally
docker run -p 8000:8000 --env-file .env hr-solution
```

Alternatively, use Docker Compose:

```bash
docker-compose up
```

Verify the application is working by accessing http://localhost:8000 in your browser.

### 3. Push the Docker Image to Amazon ECR

```bash
# Log in to Amazon ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin your-aws-account-id.dkr.ecr.us-east-1.amazonaws.com

# Create a repository if it doesn't exist
aws ecr create-repository --repository-name hr-solution --region us-east-1

# Tag the image
docker tag hr-solution:latest your-aws-account-id.dkr.ecr.us-east-1.amazonaws.com/hr-solution:latest

# Push to ECR
docker push your-aws-account-id.dkr.ecr.us-east-1.amazonaws.com/hr-solution:latest
```

### 4. Deploy to AWS ECS Fargate

#### Create a Task Definition

Create a file named `task-definition.json`:

```json
{
  "family": "hr-solution",
  "networkMode": "awsvpc",
  "executionRoleArn": "arn:aws:iam::your-aws-account-id:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "hr-solution",
      "image": "your-aws-account-id.dkr.ecr.us-east-1.amazonaws.com/hr-solution:latest",
      "essential": true,
      "portMappings": [
        {
          "containerPort": 8000,
          "hostPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        { "name": "ENVIRONMENT", "value": "prod" },
        { "name": "LOG_LEVEL", "value": "INFO" },
        { "name": "GOOGLE_GENAI_USE_VERTEXAI", "value": "False" },
        { "name": "ROOT_MODEL", "value": "gemini-2.5-flash" },
        { "name": "SUB_AGENT_MODEL", "value": "gemini-2.5-flash" },
        { "name": "AWS_REGION", "value": "us-east-1" }
      ],
      "secrets": [
        { "name": "GOOGLE_API_KEY", "valueFrom": "arn:aws:ssm:us-east-1:your-aws-account-id:parameter/hr-solution/GOOGLE_API_KEY" },
        { "name": "DATABASE_URL", "valueFrom": "arn:aws:ssm:us-east-1:your-aws-account-id:parameter/hr-solution/DATABASE_URL" }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/hr-solution",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ],
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048"
}
```

Register the task definition:

```bash
aws ecs register-task-definition --cli-input-json file://task-definition.json
```

#### Create a Service

```bash
aws ecs create-service \
  --cluster your-cluster-name \
  --service-name hr-solution \
  --task-definition hr-solution:1 \
  --desired-count 1 \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-********,subnet-********],securityGroups=[sg-********],assignPublicIp=ENABLED}" \
  --load-balancers "targetGroupArn=arn:aws:elasticloadbalancing:us-east-1:your-aws-account-id:targetgroup/hr-solution/********90abcdef,containerName=hr-solution,containerPort=8000"
```

### 5. Set up Amazon RDS (if not already done)

```bash
aws rds create-db-instance \
  --db-instance-identifier hr-db \
  --db-instance-class db.t3.small \
  --engine postgres \
  --engine-version 14.5 \
  --master-username postgres \
  --master-user-password your-password \
  --allocated-storage 20 \
  --vpc-security-group-ids sg-******** \
  --db-subnet-group-name your-subnet-group \
  --publicly-accessible \
  --backup-retention-period 7
```

### 6. Set up a Custom Domain with Route 53 and ALB (Optional)

1. Create an Application Load Balancer (ALB) pointing to your ECS service
2. Create a Route 53 record pointing to the ALB
3. Set up an SSL certificate using AWS Certificate Manager

## Continuous Deployment with AWS CodePipeline

1. Create a CodeCommit repository or connect to GitHub
2. Set up a CodeBuild project with the following `buildspec.yml`:

```yaml
version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
      - REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/hr-solution
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${COMMIT_HASH:=latest}
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build -t $REPOSITORY_URI:latest .
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - echo Writing image definitions file...
      - aws ecs update-service --cluster your-cluster-name --service hr-solution --force-new-deployment

artifacts:
  files:
    - appspec.yml
    - taskdef.json
```

3. Create a CodePipeline with source, build, and deploy stages

## Monitoring and Logging

- Use Amazon CloudWatch to monitor application performance
- Set up CloudWatch Alarms for critical metrics
- Use CloudWatch Logs for application logs
- Consider using AWS X-Ray for distributed tracing

## Scaling

Configure Auto Scaling for your ECS service:

```bash
aws application-autoscaling register-scalable-target \
  --service-namespace ecs \
  --scalable-dimension ecs:service:DesiredCount \
  --resource-id service/your-cluster-name/hr-solution \
  --min-capacity 1 \
  --max-capacity 10

aws application-autoscaling put-scaling-policy \
  --service-namespace ecs \
  --scalable-dimension ecs:service:DesiredCount \
  --resource-id service/your-cluster-name/hr-solution \
  --policy-name cpu-tracking-scaling-policy \
  --policy-type TargetTrackingScaling \
  --target-tracking-scaling-policy-configuration '{"TargetValue": 70.0, "PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}}'
```

## Troubleshooting

- Check CloudWatch Logs for application errors
- Verify RDS database connectivity
- Ensure all required environment variables and secrets are set correctly
- Check security group rules to ensure proper network connectivity
