# Web Framework
fastapi==0.116.1
uvicorn==0.35.0

# Google Cloud and Vertex AI
google-adk==1.7.0
google-api-python-client==2.176.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-generativeai==0.8.5
google-cloud-aiplatform==1.104.0

# Data Validation and Serialization
pydantic==2.11.7

# HTTP Client
requests==2.32.4

# Environment and Configuration
python-dotenv==1.0.0

# Database
sqlalchemy==2.0.41

# Caching
redis==6.2.0

# AWS Services
boto3==1.39.10
requests-aws4auth==1.3.1

# Search and Vector Database
opensearch-py==3.0.0

# Authentication
PyJWT==2.8.0

# Testing
pytest==8.4.1

# Retry Logic
tenacity==8.5.0

# Async Support
anyio==4.9.0
starlette==0.47.2

# Additional Dependencies
aiohttp==3.9.1
deprecated==1.2.14

# Core Dependencies (from installation)
httpx==0.28.1
python-multipart==0.0.20