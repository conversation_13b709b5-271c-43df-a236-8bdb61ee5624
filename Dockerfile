FROM python:3.12-slim
ENV PYTHONDONTWRITEBYTECODE=1 \
        PYTHONUNBUFFERED=1 \
        PYTHONPATH=/app

    WORKDIR /app

    # Install only required build tools for psycopg2-binary (not libpq-dev)

    RUN apt-get update && apt-get install -y --no-install-recommends \
        gcc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

    COPY requirements.txt .

    # Pre-install wheel to speed up builds

    RUN pip install --no-cache-dir --upgrade pip wheel && \
        pip install --no-cache-dir -r requirements.txt

    COPY . .

    # Create startup script 

    RUN echo '#!/bin/bash\n\
    if [ ! -z "$GOOGLE_APPLICATION_CREDENTIALS_JSON" ]; then\n\
        echo "$GOOGLE_APPLICATION_CREDENTIALS_JSON" > /app/google-credentials.json\n\
        export GOOGLE_APPLICATION_CREDENTIALS=/app/google-credentials.json\n\
        echo "Google credentials file created from environment variable at: $GOOGLE_APPLICATION_CREDENTIALS"\n\
    elif [ -f "/app/google-credentials.json" ]; then\n\
        export GOOGLE_APPLICATION_CREDENTIALS=/app/google-credentials.json\n\
        echo "Using existing Google credentials file at: $GOOGLE_APPLICATION_CREDENTIALS"\n\
    else\n\
        echo "Warning: No Google credentials file found."\n\
    fi\n\
    exec uvicorn api_server:app --host 0.0.0.0 --port 8081\n\
    ' > /app/start.sh && chmod +x /app/start.sh
 
EXPOSE 8081

CMD ["/app/start.sh"]
 