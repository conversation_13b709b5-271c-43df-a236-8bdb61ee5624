#!/usr/bin/env python3
"""
Test script for Intelligent Routing System.

This script demonstrates how the LLM-powered intent classification
works compared to hardcoded keyword matching.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.intelligent_routing import route_message_intelligently, get_routing_decision

def test_intelligent_routing():
    """Test the intelligent routing system with various queries."""
    
    test_queries = [
        # Leave Management Queries
        "What is the exit process?",
        "I need to take some time off",
        "How many vacation days do I have?",
        "Can I apply for sick leave?",
        "Show me my leave history",
        
        # Attendance Queries
        "What's my attendance for this month?",
        "I need to clock in",
        "Show me my work hours",
        "How do I record my attendance?",
        
        # Profile Queries
        "Who is my manager?",
        "Show me my team members",
        "What's my employee profile?",
        "Who are the people in my department?",
        
        # Policy Queries
        "What's the company policy on remote work?",
        "How do I resign from the company?",
        "What are the HR procedures?",
        "Tell me about the termination process",
        
        # General HR Queries
        "How do I update my contact information?",
        "What HR services are available?",
        "I have a general HR question",
        "Can you help me with something?"
    ]
    
    print("🤖 INTELLIGENT ROUTING SYSTEM TEST")
    print("=" * 50)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Query: '{query}'")
        
        # Get routing decision
        decision = get_routing_decision(query)
        
        print(f"   Intent: {decision['classified_intent']}")
        print(f"   Agent: {decision['selected_agent']}")
        print(f"   Confidence: {decision['confidence']}")
        print(f"   Reasoning: {decision['reasoning']}")
    
    print("\n" + "=" * 50)
    print("✅ Intelligent routing test completed!")

def compare_with_hardcoded():
    """Compare intelligent routing with hardcoded keyword matching."""
    
    print("\n🔍 COMPARISON: Intelligent vs Hardcoded Routing")
    print("=" * 60)
    
    # Test cases that would fail with hardcoded keywords
    edge_cases = [
        "What's the exit process?",  # No "exit" in hardcoded leave keywords
        "I'm thinking of leaving the company",  # Natural language variation
        "How do I hand in my resignation?",  # Different phrasing
        "What's the process for quitting?",  # Informal language
        "I want to know about offboarding",  # Technical term
        "Can you tell me about the separation process?",  # Formal term
    ]
    
    print("\nEdge cases that would fail with hardcoded keywords:")
    for query in edge_cases:
        decision = get_routing_decision(query)
        print(f"\nQuery: '{query}'")
        print(f"Intelligent Routing: {decision['selected_agent']} ({decision['classified_intent']})")
        print(f"Hardcoded would fail because: No exact keyword match")

if __name__ == "__main__":
    test_intelligent_routing()
    compare_with_hardcoded() 